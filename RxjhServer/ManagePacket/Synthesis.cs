﻿using System;
using System.Linq;
using RxjhServer.DbClss;
using RxjhServer.HelperTools;

namespace RxjhServer;

public partial class Players
{
	private int[] neoTypes = { 511, 512, 513, 514,610, 611, 612, 613, 614 };

	public void SynthesisSystem(byte[] PacketData, int PacketSize)
	{
		//Converter.ToString(PacketData);
		int num = BitConverter.ToUInt16(PacketData, 10);
		HeThongNhacNho("Case " + num);
		int fromPos = PacketData[26];
		int fromBag = PacketData[14];
		int num34 = PacketData[110];
		if (!OpenWarehouse && num < 260)
		{
			return;
		}
		try
		{
			PacketModification(PacketData, PacketSize);
			OpenWarehouse = true;
			byte[] array = Converter.HexStringToByte("AA557A000302510075003F010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFF0000000000000000000000000000000055AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(0L), 0, array, 98, 8);
			if (PacketSize <= 29)
			{
				return;
			}
			byte[] array24 = new byte[4];
			System.Buffer.BlockCopy(PacketData, 10, array24, 0, 4);
			if (GMMode == 1)
			{
				HeThongNhacNho("List:" + num, 10);
			}
			 if (neoTypes.Contains(num))
            {
                Enhance_Neo(PacketData);
                return;
            }
			switch (num)
			{
			case 140:
				try
				{
					byte[] arrayx = new byte[2];
					System.Buffer.BlockCopy(PacketData, 110, arrayx, 0, 2);
					byte[] arrayxx = new byte[4];
					System.Buffer.BlockCopy(PacketData, 14, arrayxx, 0, 4);
					int numxx = BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(arrayxx, 0)].VatPham_ID, 0);
					int num20 = BitConverter.ToInt16(arrayx, 0);
					HcItimesClass hcItimesClass113;
					HcItimesClass hcItimesClass115;
					HcItimesClass hcItimesClass117;
					ItmeClass value63;
					if (numxx != 0 && HopThanhVatPham_Table.Count > 0)
					{
						hcItimesClass113 = null;
						hcItimesClass115 = null;
						hcItimesClass117 = null;
						if (HopThanhVatPham_Table.ContainsKey(1))
						{
							hcItimesClass113 = HopThanhVatPham_Table[1];
						}
						if (HopThanhVatPham_Table.ContainsKey(2))
						{
							hcItimesClass115 = HopThanhVatPham_Table[2];
						}
						if (HopThanhVatPham_Table.ContainsKey(3))
						{
							hcItimesClass117 = HopThanhVatPham_Table[3];
						}
						if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass113.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass113.ItemGlobal_ID, 0) && BitConverter.ToInt64(Item_In_Bag[hcItimesClass115.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass115.ItemGlobal_ID, 0) && (hcItimesClass117 == null || BitConverter.ToInt64(Item_In_Bag[hcItimesClass117.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass117.ItemGlobal_ID, 0)))
						{
							hcItimesClass113.DatDuocThuocTinh();
							hcItimesClass113.CuongHoaThuocTinhGiaiDoan();
							hcItimesClass115.CuongHoaThuocTinhGiaiDoan();
							if (Item_In_Bag[hcItimesClass113.Position].Vat_Pham_Khoa_Lai)
							{
								SynthesisHint(21, 3, 0, Item_In_Bag[hcItimesClass113.Position]);
								HopThanhVatPham_Table.Clear();
							}
							else if (World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass113.VatPham_id, 0), out value63))
							{
								if (value63.FLD_RESIDE2 != 1 && value63.FLD_RESIDE2 != 2 && value63.FLD_RESIDE2 != 4 && value63.FLD_RESIDE2 != 5 && value63.FLD_RESIDE2 != 6 && value63.FLD_RESIDE2 != 12)
								{
									SynthesisHint(21, 5, 0, Item_In_Bag[hcItimesClass113.Position]);
									HopThanhVatPham_Table.Clear();
								}
								else if (value63.FLD_RESIDE2 == 12)
								{
									if (BitConverter.ToInt32(hcItimesClass115.VatPham_id, 0) == 800000013)
									{
										goto IL_12e7;
									}
									SynthesisHint(21, 5, 0, Item_In_Bag[hcItimesClass113.Position]);
									HopThanhVatPham_Table.Clear();
									Form1.WriteLine(6, "Synthesis system WGF BUG1[" + BitConverter.ToInt32(hcItimesClass113.VatPham_id, 0) + "]-[" + BitConverter.ToInt32(hcItimesClass115.VatPham_id, 0) + "]");
								}
								else
								{
									if (value63.FLD_RESIDE2 == 1 || value63.FLD_RESIDE2 == 2 || value63.FLD_RESIDE2 == 5 || value63.FLD_RESIDE2 == 6)
									{
										int num47 = BitConverter.ToInt32(hcItimesClass115.VatPham_id, 0);
										switch (num47)
										{
										default:
											SynthesisHint(21, 5, 0, Item_In_Bag[hcItimesClass113.Position]);
											HopThanhVatPham_Table.Clear();
											Form1.WriteLine(6, "Synthesis system WGF BUG2[" + BitConverter.ToInt32(hcItimesClass113.VatPham_id, 0) + "]-[" + BitConverter.ToInt32(hcItimesClass115.VatPham_id, 0) + "]");
											return;
										case 800000002:
										case 800000024:
										case 800000062:
										case 800000068:
											break;
										}
										if (value63.FLD_LEVEL >= 150)
										{
											if (num47 != 800000068)
											{
												SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
												break;
											}
										}
										else if (value63.FLD_LEVEL >= 130)
										{
											if (num47 != 800000062)
											{
												SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
												break;
											}
										}
										else if (value63.FLD_LEVEL >= 80 && value63.FLD_LEVEL < 130)
										{
											if (num47 != 800000002 && num47 != 800000024)
											{
												SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
												break;
											}
										}
										else if (num47 != 800000002)
										{
											SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
											break;
										}
										if (value63.FLD_RESIDE2 == 6 && (hcItimesClass113.ThuocTinh3.ThuocTinhSoLuong != 0 || hcItimesClass113.ThuocTinh4.ThuocTinhSoLuong != 0))
										{
											break;
										}
										goto IL_12e7;
									}
									if (value63.FLD_RESIDE2 == 4)
									{
										int num49 = BitConverter.ToInt32(hcItimesClass115.VatPham_id, 0);
										if (num49 != 800000001 && num49 != 800000023 && num49 != 800000061 && num49 != 800000067)
										{
											SynthesisHint(21, 5, 0, Item_In_Bag[hcItimesClass113.Position]);
											HopThanhVatPham_Table.Clear();
											Form1.WriteLine(6, "Synthesis system WGF BUG3[" + BitConverter.ToInt32(hcItimesClass113.VatPham_id, 0) + "]-[" + BitConverter.ToInt32(hcItimesClass115.VatPham_id, 0) + "]");
											break;
										}
										if (value63.FLD_LEVEL >= 150)
										{
											if (num49 != 800000067)
											{
												SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
												break;
											}
										}
										else if (value63.FLD_LEVEL >= 130)
										{
											if (num49 != 800000061)
											{
												SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
												break;
											}
										}
										else if (value63.FLD_LEVEL >= 80 && value63.FLD_LEVEL < 130)
										{
											if (num49 != 800000001 && num49 != 800000023)
											{
												SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
												break;
											}
										}
										else if (num49 != 800000001)
										{
											SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
											break;
										}
										goto IL_12e7;
									}
									SynthesisHint(21, 5, 0, Item_In_Bag[hcItimesClass113.Position]);
									HopThanhVatPham_Table.Clear();
								}
							}
							else
							{
								SynthesisHint(21, 5, 0, Item_In_Bag[hcItimesClass113.Position]);
								HopThanhVatPham_Table.Clear();
							}
						}
					}
					goto end_IL_05c6;
					IL_12e7:
					if (Phi_HopThanh <= 0)
					{
						goto IL_0d9b;
					}
					if (value63.FLD_NJ == 0)
					{
						if (base.Player_Money >= Phi_HopThanh)
						{
							base.Player_Money -= Phi_HopThanh;
							UpdateMoneyAndWeight();
							goto IL_0d9b;
						}
					}
					else if (base.Player_Money >= Phi_HopThanh)
					{
						hcItimesClass113.FLD_FJ_NJ = 0;
						base.Player_Money -= Phi_HopThanh;
						UpdateMoneyAndWeight();
						Update_Item_In_Bag();
						goto IL_0d9b;
					}
					SynthesisHint(11, 4, 0, Item_In_Bag[hcItimesClass113.Position]);
					HopThanhVatPham_Table.Clear();
					goto end_IL_05c6;
					IL_0d9b:
					Random random10 = new Random();
					string[] array11 = World.TongXacSuat_HopThanh.Split(';');
					double num51 = random10.Next(int.Parse(array11[0]), int.Parse(array11[1]));
					double num53 = 75.0;
					if (hcItimesClass117 != null)
					{
						num51 += (double)LuckyCharmBonus(BitConverter.ToInt32(hcItimesClass117.VatPham_id, 0));
					}
					if (NguyenBao_HopThanh_MoRa == 1)
					{
						num51 += 5.0;
					}
					if (base.FLD_VIP == 1)
					{
					}
					if (World.TyLe_HopThanh != 0.0)
					{
						num51 += 100.0 * World.TyLe_HopThanh;
					}
					if (base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
					{
						num51 += 100.0 * base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
					}
					if (base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
					{
						num51 += 100.0 * base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
					}
					if (num51 >= num53)
					{
						switch (num20)
						{
						case 0:
							if (hcItimesClass113.ThuocTinh1.ThuocTinhLoaiHinh != 0)
							{
								if (hcItimesClass115.GiaiDoanLoaiHinh == 8)
								{
									hcItimesClass113.ThuocTinh1.KhiCongThuocTinhLoaiHinh = hcItimesClass115.KhiCongThuocTinhLoaiHinh;
								}
								hcItimesClass113.ThuocTinh1.ThuocTinhLoaiHinh = hcItimesClass115.GiaiDoanLoaiHinh;
								hcItimesClass113.ThuocTinh1.ThuocTinhSoLuong = hcItimesClass115.GiaiDoanSoLuong;
								hcItimesClass113.ThietLap_ThuocTinh();
							}
							break;
						case 1:
							if (hcItimesClass113.ThuocTinh2.ThuocTinhLoaiHinh != 0)
							{
								if (hcItimesClass115.GiaiDoanLoaiHinh == 8)
								{
									hcItimesClass113.ThuocTinh2.KhiCongThuocTinhLoaiHinh = hcItimesClass115.KhiCongThuocTinhLoaiHinh;
								}
								hcItimesClass113.ThuocTinh2.ThuocTinhLoaiHinh = hcItimesClass115.GiaiDoanLoaiHinh;
								hcItimesClass113.ThuocTinh2.ThuocTinhSoLuong = hcItimesClass115.GiaiDoanSoLuong;
								hcItimesClass113.ThietLap_ThuocTinh();
							}
							break;
						case 2:
							if (value63.FLD_RESIDE2 == 6)
							{
								if (hcItimesClass113.ThuocTinh2.ThuocTinhLoaiHinh != 0)
								{
									if (hcItimesClass115.GiaiDoanLoaiHinh == 8)
									{
										hcItimesClass113.ThuocTinh2.KhiCongThuocTinhLoaiHinh = hcItimesClass115.KhiCongThuocTinhLoaiHinh;
									}
									hcItimesClass113.ThuocTinh2.ThuocTinhLoaiHinh = hcItimesClass115.GiaiDoanLoaiHinh;
									hcItimesClass113.ThuocTinh2.ThuocTinhSoLuong = hcItimesClass115.GiaiDoanSoLuong;
									hcItimesClass113.ThietLap_ThuocTinh();
								}
							}
							else if (hcItimesClass113.ThuocTinh3.ThuocTinhLoaiHinh != 0)
							{
								if (hcItimesClass115.GiaiDoanLoaiHinh == 8)
								{
									hcItimesClass113.ThuocTinh3.KhiCongThuocTinhLoaiHinh = hcItimesClass115.KhiCongThuocTinhLoaiHinh;
								}
								hcItimesClass113.ThuocTinh3.ThuocTinhLoaiHinh = hcItimesClass115.GiaiDoanLoaiHinh;
								hcItimesClass113.ThuocTinh3.ThuocTinhSoLuong = hcItimesClass115.GiaiDoanSoLuong;
								hcItimesClass113.ThietLap_ThuocTinh();
							}
							break;
						case 3:
							if (hcItimesClass113.ThuocTinh4.ThuocTinhLoaiHinh != 0)
							{
								if (hcItimesClass115.GiaiDoanLoaiHinh == 8)
								{
									hcItimesClass113.ThuocTinh4.KhiCongThuocTinhLoaiHinh = hcItimesClass115.KhiCongThuocTinhLoaiHinh;
								}
								hcItimesClass113.ThuocTinh4.ThuocTinhLoaiHinh = hcItimesClass115.GiaiDoanLoaiHinh;
								hcItimesClass113.ThuocTinh4.ThuocTinhSoLuong = hcItimesClass115.GiaiDoanSoLuong;
								hcItimesClass113.ThietLap_ThuocTinh();
							}
							break;
						}
						RxjhClass.SyntheticRecord(base.Userid, base.UserName, value63.ItmeNAME, num, "HopThanh", "Thanh Cong", Item_In_Bag[hcItimesClass113.Position]);
						SynthesisHint(num, 1, Phi_HopThanh, Item_In_Bag[hcItimesClass113.Position]);
					}
					else
					{
						RxjhClass.SyntheticRecord(base.Userid, base.UserName, value63.ItmeNAME, num, "HopThanh", "That Bai", Item_In_Bag[hcItimesClass113.Position]);
						SynthesisHint(num, 0, Phi_HopThanh, Item_In_Bag[hcItimesClass113.Position]);
					}
					SubtractItems(BitConverter.ToInt32(arrayxx, 0), 1);
					SubtractItems(hcItimesClass115.Position, 1);
					if (hcItimesClass117 != null)
					{
						SubtractItems(hcItimesClass117.Position, 1);
					}
					if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass113.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass113.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass113.ItemGlobal_ID, 0))
					{
						SubtractItems(hcItimesClass113.Position, 1);
						AddItems(hcItimesClass113.ItemGlobal_ID, hcItimesClass113.VatPham_id, hcItimesClass113.Position, hcItimesClass113.VatPhamSoLuong, hcItimesClass113.VatPham_ThuocTinh);
					}
					Item_In_Bag[hcItimesClass113.Position].Khoa_Chat = false;
					HopThanhVatPham_Table.Clear();
					SynthesisSystemUnlocked();
					NguyenBao_HopThanh_MoRa = 0;
					Phi_HopThanh = 0;
					end_IL_05c6:;
				}
				catch (Exception ex69)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "Synthesis system error![" + base.Userid + "]-[" + base.UserName + "]" + ex69.Message);
				}
				goto default;
			case 141:
				try
				{
					if (HopThanhVatPham_Table.ContainsKey(1))
					{
						break;
					}
					ItmeClass value13;
					if (Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
					}
					else if (World.Itme.TryGetValue((int)Item_In_Bag[fromPos].GetVatPham_ID, out value13))
					{
						if (value13.FLD_RESIDE2 != 1 && value13.FLD_RESIDE2 != 2 && value13.FLD_RESIDE2 != 4 && value13.FLD_RESIDE2 != 5 && value13.FLD_RESIDE2 != 6)
						{
							SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
							break;
						}
						HcItimesClass hcItimesClass50 = new HcItimesClass();
						hcItimesClass50.Position = fromPos;
						hcItimesClass50.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(1, hcItimesClass50);
						SynthesisHint(num, 1, 10000000, Item_In_Bag[fromPos]);
					}
					else
					{
						SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
					}
					break;
				}
				catch (Exception ex115)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "Change Option Item 141 Error![" + base.Userid + "]-[" + base.UserName + "]" + ex115.Message);
					break;
				}
			case 142:
				try
				{
					if (HopThanhVatPham_Table != null && HopThanhVatPham_Table.Count != 0 && HopThanhVatPham_Table.ContainsKey(1))
					{
						HcItimesClass hcItimesClass148 = HopThanhVatPham_Table[1];
						if (World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass148.VatPham_id, 0), out var value77) && !HopThanhVatPham_Table.ContainsKey(2))
						{
							if (value77.FLD_RESIDE2 != 1 && value77.FLD_RESIDE2 != 2 && value77.FLD_RESIDE2 != 5 && value77.FLD_RESIDE2 != 6)
							{
								if (value77.FLD_RESIDE2 == 4)
								{
									if (Item_In_Bag[fromPos].GetVatPham_ID != 800000061 && Item_In_Bag[fromPos].GetVatPham_ID != 800000023 && Item_In_Bag[fromPos].GetVatPham_ID != 800000001 && Item_In_Bag[fromPos].GetVatPham_ID != 800000067)
									{
										SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
									}
									else if (value77.FLD_LEVEL < 80)
									{
										if (Item_In_Bag[fromPos].GetVatPham_ID != 800000001)
										{
											SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
										}
									}
									else if (value77.FLD_LEVEL >= 80 && value77.FLD_LEVEL < 130)
									{
										if (Item_In_Bag[fromPos].GetVatPham_ID != 800000001 && Item_In_Bag[fromPos].GetVatPham_ID != 800000023)
										{
											SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
										}
									}
									else if (value77.FLD_LEVEL >= 130 && value77.FLD_LEVEL < 150)
									{
										if (Item_In_Bag[fromPos].GetVatPham_ID != 800000061)
										{
											SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
										}
									}
									else if (Item_In_Bag[fromPos].GetVatPham_ID != 800000067 || value77.FLD_LEVEL < 150)
									{
										SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
									}
								}
								else if (value77.FLD_RESIDE2 == 12)
								{
									if (Item_In_Bag[fromPos].GetVatPham_ID != 800000013)
									{
										SynthesisHint(num, 5, 0, Item_In_Bag[hcItimesClass148.Position]);
									}
									else if (Item_In_Bag[fromPos].FLD_MAGIC0 == 0)
									{
										Item_In_Bag[fromPos] = new X_Vat_Pham_Loai(new byte[World.Item_Db_Byte_Length]);
										SynthesisHint(num, 5, 0, Item_In_Bag[hcItimesClass148.Position]);
									}
								}
								else
								{
									SynthesisHint(num, 5, 0, Item_In_Bag[hcItimesClass148.Position]);
								}
							}
							else if (Item_In_Bag[fromPos].GetVatPham_ID != 800000062 && Item_In_Bag[fromPos].GetVatPham_ID != 800000024 && Item_In_Bag[fromPos].GetVatPham_ID != 800000002 && Item_In_Bag[fromPos].GetVatPham_ID != 800000068)
							{
								SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
							}
							else if (value77.FLD_LEVEL >= 150)
							{
								if (Item_In_Bag[fromPos].GetVatPham_ID != 800000068)
								{
									SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
								}
							}
							else if (value77.FLD_LEVEL >= 130 && value77.FLD_LEVEL < 150)
							{
								if (Item_In_Bag[fromPos].GetVatPham_ID != 800000062)
								{
									SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
								}
							}
							else if (value77.FLD_LEVEL >= 80 && value77.FLD_LEVEL < 130)
							{
								if (Item_In_Bag[fromPos].GetVatPham_ID != 800000002 && Item_In_Bag[fromPos].GetVatPham_ID != 800000024)
								{
									SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
								}
							}
							else if (Item_In_Bag[fromPos].GetVatPham_ID != 800000002)
							{
								SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
							}
						}
					}
					else
					{
						SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
					}
					HopThanhVatPham_Table.Add(2, new HcItimesClass
					{
						Position = fromPos,
						VatPham = Item_In_Bag[fromPos].VatPham_byte
					});
					Item_In_Bag[fromPos].Khoa_Chat = true;
					SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
				}
				catch (Exception ex83)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成2 error![" + base.Userid + "]-[" + base.UserName + "]" + ex83.Message);
				}
				goto default;
			case 143:
				try
				{
					if (HopThanhVatPham_Table.Count == 0)
					{
						SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
					}
					else if (!HopThanhVatPham_Table.ContainsKey(3))
					{
						HcItimesClass hcItimesClass80 = new HcItimesClass();
						hcItimesClass80.Position = fromPos;
						hcItimesClass80.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(3, hcItimesClass80);
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
					break;
				}
				catch (Exception ex116)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "Change Option Item 143 Error![" + base.Userid + "]-[" + base.UserName + "]" + ex116.Message);
					break;
				}
			case 410:
				try
				{
					if (HopThanhVatPham_Table.TryGetValue(1, out var value100))
					{
						Item_In_Bag[value100.Position].Khoa_Chat = false;
						SynthesisHint(num, 1, 0, Item_In_Bag[value100.Position]);
						HopThanhVatPham_Table.Clear();
						SynthesisSystemUnlocked();
					}
					NguyenBao_HopThanh_MoRa = 0;
					SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
					break;
				}
				catch (Exception ex114)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 强化合成 合成4 出错![" + base.Userid + "]-[" + base.UserName + "]" + ex114.Message);
					break;
				}
			case 411:
				try
				{
					if (HopThanhVatPham_Table.ContainsKey(1))
					{
						break;
					}
					ItmeClass value5;
					if (Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
					}
					else if (World.Itme.TryGetValue((int)Item_In_Bag[fromPos].GetVatPham_ID, out value5))
					{
						if (value5.FLD_RESIDE2 != 32 && value5.FLD_RESIDE2 != 33 && value5.FLD_RESIDE2 != 34 && value5.FLD_RESIDE2 != 35 && value5.FLD_RESIDE2 != 36)
						{
							SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
							break;
						}
						HcItimesClass hcItimesClass47 = new HcItimesClass();
						hcItimesClass47.Position = fromPos;
						hcItimesClass47.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(1, hcItimesClass47);
						SynthesisHint(num, 0, 10000000, Item_In_Bag[fromPos]);
					}
					else
					{
						SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
					}
					break;
				}
				catch (Exception ex6)
				{
					Form1.WriteLine(1, "Synthesis System Orb Synthesis 合成1 出错![" + base.Userid + "]-[" + base.UserName + "]" + ex6.Message);
					break;
				}
			case 412:
				try
				{
					if (HopThanhVatPham_Table.Count == 0)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
						break;
					}
					HcItimesClass hcItimesClass35 = null;
					if (HopThanhVatPham_Table.ContainsKey(1))
					{
						hcItimesClass35 = HopThanhVatPham_Table[1];
					}
					hcItimesClass35.DatDuocThuocTinh();
					hcItimesClass35.CuongHoaThuocTinhGiaiDoan();
					if (!HopThanhVatPham_Table.ContainsKey(2))
					{
						if (Item_In_Bag[fromPos].GetVatPham_ID != BitConverter.ToInt32(hcItimesClass35.VatPham_id, 0))
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
							break;
						}
						if (Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
							break;
						}
						HcItimesClass hcItimesClass37 = new HcItimesClass();
						hcItimesClass37.Position = fromPos;
						hcItimesClass37.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(2, hcItimesClass37);
						SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
					}
					break;
				}
				catch (Exception ex118)
				{
					Form1.WriteLine(1, "Synthesis System Orb Synthesis 合成1 出错![" + base.Userid + "]-[" + base.UserName + "]" + ex118.Message);
					break;
				}
			case 413:
				try
				{
					if (HopThanhVatPham_Table.Count >= 2 && !HopThanhVatPham_Table.ContainsKey(3))
					{
						if (Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
						}
						else if (Item_In_Bag[fromPos].GetVatPham_ID == 1008002480)
						{
							HcItimesClass hcItimesClass29 = new HcItimesClass();
							hcItimesClass29.Position = fromPos;
							hcItimesClass29.VatPham = Item_In_Bag[fromPos].VatPham_byte;
							Item_In_Bag[fromPos].Khoa_Chat = true;
							HopThanhVatPham_Table.Add(3, hcItimesClass29);
							SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
						}
					}
					break;
				}
				catch (Exception ex110)
				{
					Form1.WriteLine(1, "Synthesis System Orb Synthesis 合成1 出错![" + base.Userid + "]-[" + base.UserName + "]" + ex110.Message);
					break;
				}
			case 414:
				try
				{
					if (HopThanhVatPham_Table.Count <= 0)
					{
						break;
					}
					HcItimesClass hcItimesClass126 = null;
					HcItimesClass hcItimesClass128 = null;
					HcItimesClass hcItimesClass130 = null;
					if (HopThanhVatPham_Table.ContainsKey(1))
					{
						hcItimesClass126 = HopThanhVatPham_Table[1];
					}
					if (HopThanhVatPham_Table.ContainsKey(2))
					{
						hcItimesClass128 = HopThanhVatPham_Table[2];
					}
					if (HopThanhVatPham_Table.ContainsKey(3))
					{
						hcItimesClass130 = HopThanhVatPham_Table[3];
					}
					if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass126.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass126.ItemGlobal_ID, 0))
					{
						SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
						break;
					}
					if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass128.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass128.ItemGlobal_ID, 0))
					{
						SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
						break;
					}
					if (hcItimesClass130 != null && BitConverter.ToInt64(Item_In_Bag[hcItimesClass130.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass130.ItemGlobal_ID, 0))
					{
						SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
						break;
					}
					hcItimesClass126.DatDuocThuocTinh();
					hcItimesClass126.CuongHoaThuocTinhGiaiDoan();
					hcItimesClass128.DatDuocThuocTinh();
					hcItimesClass128.CuongHoaThuocTinhGiaiDoan();
					if (Item_In_Bag[hcItimesClass126.Position].Vat_Pham_Khoa_Lai)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass126.Position]);
					}
					else
					{
						if (!World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass126.VatPham_id, 0), out var value72))
						{
							break;
						}
						if (value72.FLD_RESIDE2 != 32 && value72.FLD_RESIDE2 != 33 && value72.FLD_RESIDE2 != 34 && value72.FLD_RESIDE2 != 35 && value72.FLD_RESIDE2 != 36)
						{
							Form1.WriteLine(6, "Synthesis System Orb Synthesis BUG11[" + BitConverter.ToInt32(hcItimesClass126.VatPham_id, 0) + "]-[" + BitConverter.ToInt32(hcItimesClass128.VatPham_id, 0) + "]");
							SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
							break;
						}
						if (base.Player_Money < 10000000)
						{
							SynthesisHint(num, 1, 10000000, Item_In_Bag[fromPos]);
							HeThongNhacNho("Không đủ tiền", 50);
							break;
						}
						int minrate = int.Parse(World.NangCap_ThanChau_RandomRate[0]);
						int maxrate = int.Parse(World.NangCap_ThanChau_RandomRate[1]);
						double RandomRate = RNG.Next(minrate, maxrate);
						int level = ItmeClass.Get_OrbLevel(BitConverter.ToInt32(hcItimesClass126.VatPham_id, 0));
						double SuccessRate = double.Parse(World.NangCap_ThanChau_SuccessRate[level - 1]);
						if (BitConverter.ToInt32(hcItimesClass126.VatPham_id, 0) != BitConverter.ToInt32(hcItimesClass128.VatPham_id, 0))
						{
							SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
							break;
						}
						if (hcItimesClass130 != null)
						{
							int num54 = BitConverter.ToInt32(hcItimesClass130.VatPham_id, 0);
							int num56 = num54;
							if (num56 == 1008002480)
							{
								RandomRate += 10.0;
							}
						}
						if (PublicDrugs.ContainsKey(1008000312))
						{
							RandomRate += 5.0;
						}
						if (base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
						{
							RandomRate += 100.0 * base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
						}
						if (NguyenBao_HopThanh_MoRa == 1)
						{
							RandomRate += 5.0;
						}
						int num59 = BitConverter.ToInt32(hcItimesClass126.VatPham_id, 0);
						if (RandomRate > SuccessRate)
						{
							num59 += 5;
							SynthesisHint(num, 0, 10000000, Item_In_Bag[fromPos]);
							base.Player_Money -= 10000000L;
						}
						else
						{
							SynthesisHint(num, -1, 0, Item_In_Bag[fromPos]);
						}
						SubtractItems(hcItimesClass128.Position, 1);
						if (hcItimesClass130 != null)
						{
							SubtractItems(hcItimesClass130.Position, 1);
						}
						if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass126.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass126.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass126.ItemGlobal_ID, 0))
						{
							SubtractItems(hcItimesClass126.Position, 1);
							AddItems(hcItimesClass126.ItemGlobal_ID, BitConverter.GetBytes(num59), hcItimesClass126.Position, hcItimesClass126.VatPhamSoLuong, hcItimesClass126.VatPham_ThuocTinh);
						}
						Item_In_Bag[hcItimesClass126.Position].Khoa_Chat = false;
						HopThanhVatPham_Table.Clear();
						SynthesisSystemUnlocked();
						NguyenBao_HopThanh_MoRa = 0;
					}
					break;
				}
				catch (Exception ex85)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "Synthesis System Orb Synthesis 合成 出错![" + base.Userid + "]-[" + base.UserName + "]" + ex85.Message);
					break;
				}
			case 69:
				try
				{
					if (HopThanhVatPham_Table.TryGetValue(1, out var value29))
					{
						Item_In_Bag[value29.Position].Khoa_Chat = false;
						SynthesisHint(num, 1, 0, Item_In_Bag[value29.Position]);
					}
					HopThanhVatPham_Table.Clear();
					SynthesisSystemUnlocked();
					NguyenBao_HopThanh_MoRa = 0;
				}
				catch (Exception ex32)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 合成ThuocTinhGiaiDoan 合成4 error![" + base.Userid + "]-[" + base.UserName + "]" + ex32.Message);
				}
				goto default;
			case 169:
				try
				{
					if (HopThanhVatPham_Table.TryGetValue(1, out var value30))
					{
						Item_In_Bag[value30.Position].Khoa_Chat = false;
						HopThanhVatPham_Table.Clear();
						SynthesisSystemUnlocked();
						SynthesisHint(num, 1, 0, Item_In_Bag[value30.Position]);
					}
					OpenWarehouse = false;
					NguyenBao_HopThanh_MoRa = 0;
				}
				catch (Exception ex33)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 合成ThuocTinhGiaiDoan 合成4 error![" + base.Userid + "]-[" + base.UserName + "]" + ex33.Message);
				}
				goto default;
			case 70:
				if (base.CurrentOperationType == 103)
				{
					byte[] array30 = new byte[4];
					byte[] dst2 = new byte[4];
					System.Buffer.BlockCopy(PacketData, 14, array30, 0, 4);
					System.Buffer.BlockCopy(PacketData, 18, dst2, 0, 4);
					if (BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array30, 0)].VatPham_ID, 0) != 0 && HopThanhVatPham_Table.Count > 0)
					{
						HcItimesClass hcItimesClass151 = null;
						if (HopThanhVatPham_Table.ContainsKey(1))
						{
							hcItimesClass151 = HopThanhVatPham_Table[1];
						}
						if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass151.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass151.ItemGlobal_ID, 0))
						{
							if (Item_In_Bag[hcItimesClass151.Position].Vat_Pham_Khoa_Lai)
							{
								SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass151.Position]);
							}
							else
							{
								hcItimesClass151.CuongHoaThuocTinhGiaiDoan();
								hcItimesClass151.GiaiDoanLoaiHinh = 0;
								hcItimesClass151.GiaiDoanSoLuong = 0;
								hcItimesClass151.ThietLap_GiaiDoanThuocTinh();
								SubtractItems(BitConverter.ToInt32(array30, 0), 1);
								if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass151.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass151.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass151.ItemGlobal_ID, 0))
								{
									SubtractItems(hcItimesClass151.Position, 1);
									AddItems(hcItimesClass151.ItemGlobal_ID, hcItimesClass151.VatPham_id, hcItimesClass151.Position, hcItimesClass151.VatPhamSoLuong, hcItimesClass151.VatPham_ThuocTinh);
								}
								Item_In_Bag[hcItimesClass151.Position].Khoa_Chat = false;
								HopThanhVatPham_Table.Clear();
								SynthesisSystemUnlocked();
								NguyenBao_HopThanh_MoRa = 0;
								SynthesisHint(num, 1, 0, Item_In_Bag[hcItimesClass151.Position]);
							}
						}
					}
				}
				goto default;
			case 71:
				try
				{
					if (base.CurrentOperationType == 103 && !HopThanhVatPham_Table.ContainsKey(1) && !Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai && Item_In_Bag[fromPos].FLDThuocTinhLoaiHinh != 0 && !Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai && (!World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0), out var value87) || value87.FLD_RESIDE2 == 1 || value87.FLD_RESIDE2 == 4))
					{
						HcItimesClass hcItimesClass17 = new HcItimesClass();
						hcItimesClass17.Position = fromPos;
						hcItimesClass17.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(1, hcItimesClass17);
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex99)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinhGiaiDoan 合成1 error![" + base.Userid + "]-[" + base.UserName + "]" + ex99.Message);
				}
				goto default;
			case 39:
				try
				{
					if (HopThanhVatPham_Table.TryGetValue(1, out var value82))
					{
						Item_In_Bag[value82.Position].Khoa_Chat = false;
					}
					if (HopThanhVatPham_Table.TryGetValue(2, out value82))
					{
						Item_In_Bag[value82.Position].Khoa_Chat = false;
					}
					if (HopThanhVatPham_Table.TryGetValue(3, out value82))
					{
						Item_In_Bag[value82.Position].Khoa_Chat = false;
					}
					if (HopThanhVatPham_Table.TryGetValue(4, out value82))
					{
						Item_In_Bag[value82.Position].Khoa_Chat = false;
					}
					NguyenBao_HopThanh_MoRa = 0;
					HopThanhVatPham_Table.Clear();
					SynthesisSystemUnlocked();
					SynthesisHint(num, 1, 0, Item_In_Bag[value82.Position]);
				}
				catch (Exception ex91)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 再造合成石-取消 error![" + base.Userid + "]-[" + base.UserName + "]" + ex91.Message);
				}
				goto default;
			case 40:
				try
				{
					byte[] array31;
					int num70;
					HcItimesClass hcItimesClass6;
					HcItimesClass hcItimesClass7;
					HcItimesClass hcItimesClass8;
					HcItimesClass hcItimesClass9;
					HcItimesClass hcItimesClass10;
					ItmeClass value84;
					if (base.CurrentOperationType == 101)
					{
						array31 = new byte[4];
						byte[] dst4 = new byte[4];
						System.Buffer.BlockCopy(PacketData, 14, array31, 0, 4);
						System.Buffer.BlockCopy(PacketData, 18, dst4, 0, 4);
						num70 = BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array31, 0)].VatPham_ID, 0);
						if (num70 != 0 && HopThanhVatPham_Table.Count > 0)
						{
							hcItimesClass6 = null;
							hcItimesClass7 = null;
							hcItimesClass8 = null;
							hcItimesClass9 = null;
							hcItimesClass10 = null;
							if (HopThanhVatPham_Table.ContainsKey(1))
							{
								hcItimesClass6 = HopThanhVatPham_Table[1];
							}
							if (HopThanhVatPham_Table.ContainsKey(2))
							{
								hcItimesClass7 = HopThanhVatPham_Table[2];
							}
							if (HopThanhVatPham_Table.ContainsKey(3))
							{
								hcItimesClass8 = HopThanhVatPham_Table[3];
							}
							if (HopThanhVatPham_Table.ContainsKey(4))
							{
								hcItimesClass9 = HopThanhVatPham_Table[4];
							}
							if (HopThanhVatPham_Table.ContainsKey(5))
							{
								hcItimesClass10 = HopThanhVatPham_Table[5];
							}
							if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass6.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass6.ItemGlobal_ID, 0))
							{
								SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
								HopThanhVatPham_Table.Clear();
							}
							else if (hcItimesClass7 != null)
							{
								if (hcItimesClass7 != null && BitConverter.ToInt64(Item_In_Bag[hcItimesClass7.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass7.ItemGlobal_ID, 0))
								{
									SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass7.Position]);
									HopThanhVatPham_Table.Clear();
								}
								else if (hcItimesClass8 != null && BitConverter.ToInt64(Item_In_Bag[hcItimesClass8.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass8.ItemGlobal_ID, 0))
								{
									SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass8.Position]);
									HopThanhVatPham_Table.Clear();
								}
								else if (hcItimesClass9 != null && BitConverter.ToInt64(Item_In_Bag[hcItimesClass9.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass9.ItemGlobal_ID, 0))
								{
									SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass9.Position]);
									HopThanhVatPham_Table.Clear();
								}
								else if (hcItimesClass10 != null && BitConverter.ToInt64(Item_In_Bag[hcItimesClass10.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass10.ItemGlobal_ID, 0))
								{
									SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass10.Position]);
									HopThanhVatPham_Table.Clear();
								}
								else
								{
									hcItimesClass6.DatDuocThuocTinh();
									hcItimesClass6.CuongHoaThuocTinhGiaiDoan();
									if (!World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass6.VatPham_id, 0), out value84))
									{
										SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
										HopThanhVatPham_Table.Clear();
									}
									else if (value84.FLD_RESIDE2 != 1 && value84.FLD_RESIDE2 != 2 && value84.FLD_RESIDE2 != 4 && value84.FLD_RESIDE2 != 5 && value84.FLD_RESIDE2 != 6 && value84.FLD_RESIDE2 != 14 && value84.FLD_RESIDE2 != 23 && value84.FLD_RESIDE2 != 24 && value84.FLD_RESIDE2 != 25)
									{
										SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
										HopThanhVatPham_Table.Clear();
									}
									else if (value84.FLD_RESIDE2 == 4)
									{
										if (num70 == ********** || num70 == 1008000112 || num70 == 1008000113 || num70 == 1008001057 || num70 == **********)
										{
											goto IL_321d;
										}
										SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
									}
									else
									{
										if (num70 == ********** || num70 == 1008000115 || num70 == 1008000116 || num70 == 1008001058 || num70 == **********)
										{
											goto IL_321d;
										}
										SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
									}
								}
							}
						}
					}
					goto end_IL_2d3f;
					IL_321d:
					if ((num70 == ********** || num70 == **********) && Item_In_Bag[hcItimesClass6.Position].FLD_CuongHoaSoLuong != 5)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
					}
					else if ((num70 == ********** || num70 == ********** || num70 == 1008000113 || num70 == 1008000116 || num70 == 1008000112 || num70 == 1008000115) && Item_In_Bag[hcItimesClass6.Position].FLD_CuongHoaSoLuong >= 10)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
					}
					else if ((num70 == 1008001057 || num70 == 1008001058) && Item_In_Bag[hcItimesClass6.Position].FLD_CuongHoaSoLuong < 10)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
					}
					else if (Item_In_Bag[hcItimesClass6.Position].Vat_Pham_Khoa_Lai)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
						HopThanhVatPham_Table.Clear();
					}
					else if (hcItimesClass6.CuongHoaSoLuong >= 15)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
						HopThanhVatPham_Table.Clear();
					}
					else if (value84.FLD_LEVEL >= 130)
					{
						if (hcItimesClass7 != null && BitConverter.ToInt32(hcItimesClass7.VatPham_id, 0) != 800000060)
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
							HopThanhVatPham_Table.Clear();
						}
						else if (hcItimesClass8 != null && BitConverter.ToInt32(hcItimesClass8.VatPham_id, 0) != 800000060)
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
							HopThanhVatPham_Table.Clear();
						}
						else
						{
							if (hcItimesClass9 == null || BitConverter.ToInt32(hcItimesClass9.VatPham_id, 0) == 800000060)
							{
								goto IL_47ce;
							}
							SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
							HopThanhVatPham_Table.Clear();
						}
					}
					else if (hcItimesClass7 != null && BitConverter.ToInt32(hcItimesClass7.VatPham_id, 0) != 800000006)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
						HopThanhVatPham_Table.Clear();
					}
					else if (hcItimesClass8 != null && BitConverter.ToInt32(hcItimesClass8.VatPham_id, 0) != 800000006)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
						HopThanhVatPham_Table.Clear();
					}
					else
					{
						if (hcItimesClass9 == null || BitConverter.ToInt32(hcItimesClass9.VatPham_id, 0) == 800000006)
						{
							goto IL_47ce;
						}
						SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
						HopThanhVatPham_Table.Clear();
					}
					goto end_IL_2d3f;
					IL_47ce:
					if (Phi_HopThanh <= 0)
					{
						goto IL_35b3;
					}
					if (value84.FLD_NJ == 0)
					{
						if (base.Player_Money >= Phi_HopThanh)
						{
							base.Player_Money -= Phi_HopThanh;
							UpdateMoneyAndWeight();
							goto IL_35b3;
						}
					}
					else if (base.Player_Money >= Phi_HopThanh)
					{
						hcItimesClass6.FLD_FJ_NJ = 0;
						base.Player_Money -= Phi_HopThanh;
						UpdateMoneyAndWeight();
						Update_Item_In_Bag();
						goto IL_35b3;
					}
					SynthesisHint(11, 4, 0, Item_In_Bag[hcItimesClass6.Position]);
					HopThanhVatPham_Table.Clear();
					goto end_IL_2d3f;
					IL_35b3:
					double num71 = new Random((int)DateTime.Now.Ticks).Next(1, 110);
					int i = 0;
					while (i < new Random((int)DateTime.Now.Ticks).Next(5, 15))
					{
						num71 = new Random(World.GetRandomSeed()).Next(1, 110);
						int giaiDoanSoLuong = i + 1;
						i = giaiDoanSoLuong;
					}
					if (hcItimesClass10 != null)
					{
						num71 += (double)LuckyCharmBonus(BitConverter.ToInt32(hcItimesClass10.VatPham_id, 0));
					}
					if (NguyenBao_HopThanh_MoRa == 1)
					{
						num71 += 5.0;
					}
					if (base.FLD_VIP == 1)
					{
					}
					if (World.TyLe_CuongHoa_ToiCao != 0.0)
					{
						num71 += World.TyLe_CuongHoa_ToiCao;
					}
					if (base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
					{
						num71 += 100.0 * base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
					}
					if (base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
					{
						num71 += 100.0 * base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
					}
					if (hcItimesClass6.ThuocTinh1.ThuocTinhSoLuong + hcItimesClass6.ThuocTinh2.ThuocTinhSoLuong != 0 && hcItimesClass8 == null)
					{
						HeThongNhacNho("Tảng đá thả thiếu đi ít nhất đê\u0301n thả 2 cái.");
					}
					else if (hcItimesClass6.ThuocTinh1.ThuocTinhSoLuong + hcItimesClass6.ThuocTinh2.ThuocTinhSoLuong != 0 && hcItimesClass6.ThuocTinh3.ThuocTinhSoLuong + hcItimesClass6.ThuocTinh4.ThuocTinhSoLuong != 0 && hcItimesClass9 == null)
					{
						HeThongNhacNho("Tảng đá thả thiếu đi ít nhất đê\u0301n thả 3 cái.");
					}
					else
					{
						int numx2 = 0;
						int num_value2 = Item_In_Bag[hcItimesClass6.Position].FLD_MAGIC0;
						numx2 = num_value2;
						int num92 = num70;
						int num93 = num92;
						if (num70 == ********** || num70 == ********** || num70 == ********** || num70 == **********)
						{
							num71 += World.TyLe_TangCuong_VatPham;
						}
						if ((hcItimesClass6.CuongHoaSoLuong == 0 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh1) || (hcItimesClass6.CuongHoaSoLuong == 1 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh2) || (hcItimesClass6.CuongHoaSoLuong == 2 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh3) || (hcItimesClass6.CuongHoaSoLuong == 3 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh4) || (hcItimesClass6.CuongHoaSoLuong == 4 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh5) || (hcItimesClass6.CuongHoaSoLuong == 5 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh6) || (hcItimesClass6.CuongHoaSoLuong == 6 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh7) || (hcItimesClass6.CuongHoaSoLuong == 7 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh8) || (hcItimesClass6.CuongHoaSoLuong == 8 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh9) || (hcItimesClass6.CuongHoaSoLuong == 9 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh10) || (hcItimesClass6.CuongHoaSoLuong == 10 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh11) || (hcItimesClass6.CuongHoaSoLuong == 11 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh12) || (hcItimesClass6.CuongHoaSoLuong == 12 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh13) || (hcItimesClass6.CuongHoaSoLuong == 13 && !(num71 < 100.0 - World.ToiCaoLayNgoc_Manh14)) || ((hcItimesClass6.CuongHoaSoLuong == 14 && !(num71 < 100.0 - World.ToiCaoLayNgoc_Manh15)) ? true : false))
						{
							hcItimesClass6.CuongHoaLoaiHinh = ((value84.FLD_RESIDE2 == 4) ? 1 : 2);
							switch (BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array31, 0)].VatPham_ID, 0))
							{
							case **********:
								hcItimesClass6.CuongHoaSoLuong++;
								break;
							case 1008000112:
							{
								HcItimesClass hcItimesClass156 = hcItimesClass6;
								int giaiDoanSoLuong = hcItimesClass156.CuongHoaSoLuong + 1;
								hcItimesClass156.CuongHoaSoLuong = giaiDoanSoLuong;
								break;
							}
							case 1008000113:
							{
								Random random6 = new Random(DateTime.Now.Millisecond);
								int random18 = random6.Next(1, 100);
								if (random18 > 50)
								{
									hcItimesClass6.CuongHoaSoLuong += 2;
								}
								else if (random18 > 80)
								{
									hcItimesClass6.CuongHoaSoLuong += 3;
								}
								else
								{
									hcItimesClass6.CuongHoaSoLuong++;
								}
								if (hcItimesClass6.CuongHoaSoLuong > 10)
								{
									hcItimesClass6.CuongHoaSoLuong = 10;
								}
								break;
							}
							case **********:
								hcItimesClass6.CuongHoaSoLuong++;
								break;
							case 1008000115:
							{
								HcItimesClass hcItimesClass156 = hcItimesClass6;
								int giaiDoanSoLuong = hcItimesClass156.CuongHoaSoLuong + 1;
								hcItimesClass156.CuongHoaSoLuong = giaiDoanSoLuong;
								break;
							}
							case 1008000116:
							{
								Random random5 = new Random(DateTime.Now.Millisecond);
								int random17 = random5.Next(1, 100);
								if (random17 > 50)
								{
									hcItimesClass6.CuongHoaSoLuong += 2;
								}
								else if (random17 > 80)
								{
									hcItimesClass6.CuongHoaSoLuong += 3;
								}
								else
								{
									hcItimesClass6.CuongHoaSoLuong++;
								}
								if (hcItimesClass6.CuongHoaSoLuong > 10)
								{
									hcItimesClass6.CuongHoaSoLuong = 10;
								}
								break;
							}
							case 1008000024:
							{
								HcItimesClass hcItimesClass156 = hcItimesClass6;
								int giaiDoanSoLuong = hcItimesClass156.CuongHoaSoLuong + 1;
								hcItimesClass156.CuongHoaSoLuong = giaiDoanSoLuong;
								break;
							}
							case 1008000023:
							{
								HcItimesClass hcItimesClass156 = hcItimesClass6;
								int giaiDoanSoLuong = hcItimesClass156.CuongHoaSoLuong + 1;
								hcItimesClass156.CuongHoaSoLuong = giaiDoanSoLuong;
								break;
							}
							case 1008001058:
							{
								HcItimesClass hcItimesClass156 = hcItimesClass6;
								int giaiDoanSoLuong = hcItimesClass156.CuongHoaSoLuong + 1;
								hcItimesClass156.CuongHoaSoLuong = giaiDoanSoLuong;
								break;
							}
							case 1008001057:
							{
								HcItimesClass hcItimesClass156 = hcItimesClass6;
								int giaiDoanSoLuong = hcItimesClass156.CuongHoaSoLuong + 1;
								hcItimesClass156.CuongHoaSoLuong = giaiDoanSoLuong;
								break;
							}
							case **********:
							{
								HcItimesClass hcItimesClass156 = hcItimesClass6;
								int giaiDoanSoLuong = hcItimesClass156.CuongHoaSoLuong + 1;
								hcItimesClass156.CuongHoaSoLuong = giaiDoanSoLuong;
								break;
							}
							case **********:
							{
								HcItimesClass hcItimesClass156 = hcItimesClass6;
								int giaiDoanSoLuong = hcItimesClass156.CuongHoaSoLuong + 1;
								hcItimesClass156.CuongHoaSoLuong = giaiDoanSoLuong;
								break;
							}
							}
							if (hcItimesClass6.CuongHoaSoLuong > 15)
							{
								hcItimesClass6.CuongHoaSoLuong = 15;
							}
							hcItimesClass6.ThietLap_GiaiDoanThuocTinh();
							SubtractItems(BitConverter.ToInt32(array31, 0), 1);
							if (hcItimesClass6.CuongHoaSoLuong >= 10)
							{
								SendNewsletter(BitConverter.ToInt32(hcItimesClass6.VatPham_id, 0), base.UserName, hcItimesClass6.CuongHoaSoLuong, base.Player_Zx);
							}
							if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass6.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass6.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass6.ItemGlobal_ID, 0))
							{
								SubtractItems(hcItimesClass6.Position, 1);
								AddItems(hcItimesClass6.ItemGlobal_ID, hcItimesClass6.VatPham_id, hcItimesClass6.Position, hcItimesClass6.VatPhamSoLuong, hcItimesClass6.VatPham_ThuocTinh);
							}
							Item_In_Bag[hcItimesClass6.Position].Khoa_Chat = false;
							RxjhClass.SyntheticRecord(base.Userid, base.UserName, value84.ItmeNAME, num, Item_In_Bag[BitConverter.ToInt32(array31, 0)].DatDuocVatPhamTen_XungHao(), "Thanh Cong", Item_In_Bag[hcItimesClass6.Position], Item_In_Bag[hcItimesClass6.Position].FLD_CuongHoaSoLuong, numx2);
							SynthesisHint(num, 1, Phi_HopThanh, Item_In_Bag[hcItimesClass6.Position]);
						}
						else
						{
							int capcuonghoa = 0;
							switch (BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array31, 0)].VatPham_ID, 0))
							{
							case 1008000024:
								hcItimesClass6.CuongHoaLoaiHinh = 0;
								hcItimesClass6.CuongHoaSoLuong = 0;
								capcuonghoa = 0;
								hcItimesClass6.ThietLap_GiaiDoanThuocTinh();
								if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass6.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass6.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass6.ItemGlobal_ID, 0))
								{
									SubtractItems(hcItimesClass6.Position, 1);
									AddItems(hcItimesClass6.ItemGlobal_ID, hcItimesClass6.VatPham_id, hcItimesClass6.Position, hcItimesClass6.VatPhamSoLuong, hcItimesClass6.VatPham_ThuocTinh);
								}
								break;
							case 1008000023:
								hcItimesClass6.CuongHoaLoaiHinh = 0;
								hcItimesClass6.CuongHoaSoLuong = 0;
								capcuonghoa = 0;
								hcItimesClass6.ThietLap_GiaiDoanThuocTinh();
								if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass6.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass6.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass6.ItemGlobal_ID, 0))
								{
									SubtractItems(hcItimesClass6.Position, 1);
									AddItems(hcItimesClass6.ItemGlobal_ID, hcItimesClass6.VatPham_id, hcItimesClass6.Position, hcItimesClass6.VatPhamSoLuong, hcItimesClass6.VatPham_ThuocTinh);
								}
								break;
							case 1008001058:
								hcItimesClass6.CuongHoaSoLuong -= 2;
								if (hcItimesClass6.CuongHoaSoLuong <= 0)
								{
									hcItimesClass6.CuongHoaSoLuong = 0;
									hcItimesClass6.CuongHoaLoaiHinh = 0;
								}
								capcuonghoa = hcItimesClass6.CuongHoaSoLuong;
								hcItimesClass6.ThietLap_GiaiDoanThuocTinh();
								if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass6.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass6.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass6.ItemGlobal_ID, 0))
								{
									SubtractItems(hcItimesClass6.Position, 1);
									AddItems(hcItimesClass6.ItemGlobal_ID, hcItimesClass6.VatPham_id, hcItimesClass6.Position, hcItimesClass6.VatPhamSoLuong, hcItimesClass6.VatPham_ThuocTinh);
								}
								break;
							case 1008001057:
								hcItimesClass6.CuongHoaSoLuong -= 2;
								if (hcItimesClass6.CuongHoaSoLuong <= 0)
								{
									hcItimesClass6.CuongHoaSoLuong = 0;
									hcItimesClass6.CuongHoaLoaiHinh = 0;
								}
								capcuonghoa = hcItimesClass6.CuongHoaSoLuong;
								hcItimesClass6.ThietLap_GiaiDoanThuocTinh();
								if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass6.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass6.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass6.ItemGlobal_ID, 0))
								{
									SubtractItems(hcItimesClass6.Position, 1);
									AddItems(hcItimesClass6.ItemGlobal_ID, hcItimesClass6.VatPham_id, hcItimesClass6.Position, hcItimesClass6.VatPhamSoLuong, hcItimesClass6.VatPham_ThuocTinh);
								}
								break;
							case 1008000112:
								hcItimesClass6.CuongHoaSoLuong -= 2;
								if (hcItimesClass6.CuongHoaSoLuong <= 0)
								{
									hcItimesClass6.CuongHoaSoLuong = 0;
									hcItimesClass6.CuongHoaLoaiHinh = 0;
								}
								capcuonghoa = hcItimesClass6.CuongHoaSoLuong;
								hcItimesClass6.ThietLap_GiaiDoanThuocTinh();
								if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass6.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass6.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass6.ItemGlobal_ID, 0))
								{
									SubtractItems(hcItimesClass6.Position, 1);
									AddItems(hcItimesClass6.ItemGlobal_ID, hcItimesClass6.VatPham_id, hcItimesClass6.Position, hcItimesClass6.VatPhamSoLuong, hcItimesClass6.VatPham_ThuocTinh);
								}
								break;
							case 1008000113:
								hcItimesClass6.CuongHoaLoaiHinh = 0;
								hcItimesClass6.CuongHoaSoLuong = 0;
								hcItimesClass6.ThietLap_GiaiDoanThuocTinh();
								capcuonghoa = hcItimesClass6.CuongHoaSoLuong;
								if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass6.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass6.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass6.ItemGlobal_ID, 0))
								{
									SubtractItems(hcItimesClass6.Position, 1);
									AddItems(hcItimesClass6.ItemGlobal_ID, hcItimesClass6.VatPham_id, hcItimesClass6.Position, hcItimesClass6.VatPhamSoLuong, hcItimesClass6.VatPham_ThuocTinh);
								}
								break;
							case 1008000115:
								hcItimesClass6.CuongHoaSoLuong -= 2;
								if (hcItimesClass6.CuongHoaSoLuong <= 0)
								{
									hcItimesClass6.CuongHoaSoLuong = 0;
									hcItimesClass6.CuongHoaLoaiHinh = 0;
								}
								hcItimesClass6.ThietLap_GiaiDoanThuocTinh();
								capcuonghoa = hcItimesClass6.CuongHoaSoLuong;
								if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass6.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass6.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass6.ItemGlobal_ID, 0))
								{
									SubtractItems(hcItimesClass6.Position, 1);
									AddItems(hcItimesClass6.ItemGlobal_ID, hcItimesClass6.VatPham_id, hcItimesClass6.Position, hcItimesClass6.VatPhamSoLuong, hcItimesClass6.VatPham_ThuocTinh);
								}
								break;
							case 1008000116:
								hcItimesClass6.CuongHoaLoaiHinh = 0;
								hcItimesClass6.CuongHoaSoLuong = 0;
								hcItimesClass6.ThietLap_GiaiDoanThuocTinh();
								capcuonghoa = hcItimesClass6.CuongHoaSoLuong;
								if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass6.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass6.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass6.ItemGlobal_ID, 0))
								{
									SubtractItems(hcItimesClass6.Position, 1);
									AddItems(hcItimesClass6.ItemGlobal_ID, hcItimesClass6.VatPham_id, hcItimesClass6.Position, hcItimesClass6.VatPhamSoLuong, hcItimesClass6.VatPham_ThuocTinh);
								}
								break;
							}
							RxjhClass.SyntheticRecord(base.Userid, base.UserName, value84.ItmeNAME, num, Item_In_Bag[BitConverter.ToInt32(array31, 0)].DatDuocVatPhamTen_XungHao(), "That Bai", Item_In_Bag[hcItimesClass6.Position], capcuonghoa, numx2);
							SynthesisHint(num, 0, Phi_HopThanh, Item_In_Bag[hcItimesClass6.Position]);
							SubtractItems(BitConverter.ToInt32(array31, 0), 1);
						}
						SubtractItems(hcItimesClass7.Position, 1);
						if (hcItimesClass8 != null)
						{
							SubtractItems(hcItimesClass8.Position, 1);
						}
						if (hcItimesClass9 != null)
						{
							SubtractItems(hcItimesClass9.Position, 1);
						}
						if (hcItimesClass10 != null)
						{
							SubtractItems(hcItimesClass10.Position, 1);
						}
						HopThanhVatPham_Table.Clear();
						SynthesisSystemUnlocked();
						NguyenBao_HopThanh_MoRa = 0;
						Phi_HopThanh = 0;
						OpenWarehouse = false;
					}
					end_IL_2d3f:;
				}
				catch (Exception ex95)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成 error![" + base.Userid + "]-[" + base.UserName + "]" + ex95.Message);
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成 error![" + base.Userid + "]-[" + base.UserName + "]" + ex95.StackTrace);
				}
				goto default;
			case 41:
			case 611:
				try
				{
					ItmeClass value101;
					int num79;
					if ((base.CurrentOperationType == 101 || base.CurrentOperationType == 324) && !HopThanhVatPham_Table.ContainsKey(1))
					{
						if (Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
						}
						else
						{
							byte[] array33 = new byte[4];
							System.Buffer.BlockCopy(PacketData, 14, array33, 0, 4);
							if (World.Itme.TryGetValue((int)Item_In_Bag[fromPos].GetVatPham_ID, out value101))
							{
								if (value101.FLD_RESIDE2 == 12)
								{
									SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
								}
								else if (Item_In_Bag[fromPos].FLD_CuongHoaSoLuong < 15)
								{
									if (value101.FLD_RESIDE2 != 1 && value101.FLD_RESIDE2 != 2 && value101.FLD_RESIDE2 != 4 && value101.FLD_RESIDE2 != 5 && value101.FLD_RESIDE2 != 6 && value101.FLD_RESIDE2 != 14)
									{
										SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
									}
									else
									{
										num79 = (int)Item_In_Bag[BitConverter.ToInt32(array33, 0)].GetVatPham_ID;
										if (value101.FLD_RESIDE2 == 4)
										{
											if (num79 == ********** || num79 == 1008000112 || num79 == 1008000113 || num79 == 1008001057 || num79 == **********)
											{
												goto IL_4c22;
											}
											if (num79 ==1008002540 || num79== 1008002534 || num79==1008002535|| num79==1008002537|| num79==1008002538)
												{
													goto IL_4c22;
												}
											SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
										}
										else
										{
											if (num79 == ********** || num79 == 1008000115 || num79 == 1008000116 || num79 == 1008001058 || num79 == **********)
											{
												goto IL_4c22;
											}
											SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
										}
									}
								}
							}
						}
					}
					goto end_IL_4979;
					IL_4c22:
					if ((num79 == ********** || num79 == **********) && Item_In_Bag[fromPos].FLD_CuongHoaSoLuong != 5)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
					}
					else if ((num79 == ********** || num79 == ********** || num79 == 1008000113 || num79 == 1008000116 || num79 == 1008000112 || num79 == 1008000115) && Item_In_Bag[fromPos].FLD_CuongHoaSoLuong >= 10)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
					}
					else if ((num79 == 1008001057 || num79 == 1008001058) && Item_In_Bag[fromPos].FLD_CuongHoaSoLuong < 10)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
					}
					else if ((num79 == 1008001057 || num79 == 1008001058) && value101.FLD_LEVEL < 120)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
					}
					else
					{
						int SoTien6 = (Phi_HopThanh = CalculateSyntheticEnhancementCost(value101, fromPos, num));
						HcItimesClass hcItimesClass26 = new HcItimesClass();
						hcItimesClass26.Position = fromPos;
						hcItimesClass26.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(1, hcItimesClass26);
						SynthesisHint(num, 1, SoTien6, Item_In_Bag[fromPos]);
					}
					end_IL_4979:;
				}
				catch (Exception ex109)
				{
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成1 error![" + base.Userid + "]-[" + base.UserName + "]" + ex109.Message);
				}
				goto default;
			case 42:
			case 612:
				try
				{
					HcItimesClass hcItimesClass152;
					if ((base.CurrentOperationType == 101|| base.CurrentOperationType == 324) && !HopThanhVatPham_Table.ContainsKey(4))
					{
						if (HopThanhVatPham_Table.ContainsKey(1))
						{
							hcItimesClass152 = HopThanhVatPham_Table[1];
							ItmeClass value79;
							if (HopThanhVatPham_Table.Count == 0)
							{
								SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
							}
							else if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) >= 1 && World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass152.VatPham_id, 0), out value79))
							{
								if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 800000006 && BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 800000060)
								{
									SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
								}
								else if (value79.FLD_LEVEL >= 130)
								{
									if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) == 800000060)
									{
										goto IL_5148;
									}
									SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
								}
								else
								{
									if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) == 800000006)
									{
										goto IL_5148;
									}
									SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
								}
							}
						}
						else
						{
							SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
						}
					}
					goto end_IL_4e97;
					IL_509f:
					HcItimesClass hcItimesClass153;
					if (hcItimesClass152.ThuocTinh1.ThuocTinhSoLuong + hcItimesClass152.ThuocTinh2.ThuocTinhSoLuong != 0 && hcItimesClass152.ThuocTinh3.ThuocTinhSoLuong + hcItimesClass152.ThuocTinh4.ThuocTinhSoLuong != 0 && BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) >= 3 && !HopThanhVatPham_Table.ContainsKey(4))
					{
						HopThanhVatPham_Table.Add(4, hcItimesClass153);
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
					goto end_IL_4e97;
					IL_5148:
					hcItimesClass153 = new HcItimesClass();
					hcItimesClass153.Position = fromPos;
					hcItimesClass153.VatPham = Item_In_Bag[fromPos].VatPham_byte;
					Item_In_Bag[fromPos].Khoa_Chat = true;
					if (!HopThanhVatPham_Table.ContainsKey(2))
					{
						HopThanhVatPham_Table.Add(2, hcItimesClass153);
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
					hcItimesClass152.DatDuocThuocTinh();
					if (hcItimesClass152.ThuocTinh1.ThuocTinhSoLuong + hcItimesClass152.ThuocTinh2.ThuocTinhSoLuong == 0)
					{
						goto IL_509f;
					}
					if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) >= 2)
					{
						if (!HopThanhVatPham_Table.ContainsKey(3))
						{
							HopThanhVatPham_Table.Add(3, hcItimesClass153);
							SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
						}
						goto IL_509f;
					}
					end_IL_4e97:;
				}
				catch (Exception ex87)
				{
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成2 error![" + base.Userid + "]-[" + base.UserName + "]" + ex87.Message);
				}
				goto default;
			case 43:
			case 613:
				try
				{
					if (base.CurrentOperationType == 101)
					{
						if (HopThanhVatPham_Table.Count == 0)
						{
							SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
						}
						else if (!HopThanhVatPham_Table.ContainsKey(5))
						{
							HcItimesClass hcItimesClass18 = new HcItimesClass();
							hcItimesClass18.Position = fromPos;
							hcItimesClass18.VatPham = Item_In_Bag[fromPos].VatPham_byte;
							Item_In_Bag[fromPos].Khoa_Chat = true;
							HopThanhVatPham_Table.Add(5, hcItimesClass18);
							SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
						}
					}
				}
				catch (Exception ex102)
				{
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成3 error![" + base.Userid + "]-[" + base.UserName + "]" + ex102.Message);
				}
				goto default;
			case 49:
				try
				{
					if (HopThanhVatPham_Table.TryGetValue(1, out var value88))
					{
						Item_In_Bag[value88.Position].Khoa_Chat = false;
						SynthesisHint(num, 1, 0, Item_In_Bag[value88.Position]);
					}
					HopThanhVatPham_Table.Clear();
					SynthesisSystemUnlocked();
					NguyenBao_HopThanh_MoRa = 0;
					OpenWarehouse = false;
				}
				catch (Exception ex100)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成4 error![" + base.Userid + "]-[" + base.UserName + "]" + ex100.Message);
				}
				goto default;
			case 50:
				if (base.CurrentOperationType == 102)
				{
					byte[] array2 = new byte[4];
					byte[] dst = new byte[4];
					System.Buffer.BlockCopy(PacketData, 14, array2, 0, 4);
					System.Buffer.BlockCopy(PacketData, 18, dst, 0, 4);
					if (BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array2, 0)].VatPham_ID, 0) != 0 && HopThanhVatPham_Table.Count > 0)
					{
						HcItimesClass hcItimesClass69 = null;
						if (HopThanhVatPham_Table.ContainsKey(1))
						{
							hcItimesClass69 = HopThanhVatPham_Table[1];
						}
						if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass69.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass69.ItemGlobal_ID, 0))
						{
							if (Item_In_Bag[hcItimesClass69.Position].Vat_Pham_Khoa_Lai)
							{
								SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass69.Position]);
							}
							else
							{
								hcItimesClass69.DatDuocThuocTinh();
								if (hcItimesClass69.ThuocTinh4.ThuocTinhLoaiHinh != 0)
								{
									hcItimesClass69.ThuocTinh4.ThuocTinhLoaiHinh = 0;
									hcItimesClass69.ThuocTinh4.ThuocTinhSoLuong = 0;
									hcItimesClass69.ThietLap_ThuocTinh();
								}
								else if (hcItimesClass69.ThuocTinh3.ThuocTinhLoaiHinh != 0)
								{
									hcItimesClass69.ThuocTinh3.ThuocTinhLoaiHinh = 0;
									hcItimesClass69.ThuocTinh3.ThuocTinhSoLuong = 0;
									hcItimesClass69.ThietLap_ThuocTinh();
								}
								else if (hcItimesClass69.ThuocTinh2.ThuocTinhLoaiHinh != 0)
								{
									hcItimesClass69.ThuocTinh2.ThuocTinhLoaiHinh = 0;
									hcItimesClass69.ThuocTinh2.ThuocTinhSoLuong = 0;
									hcItimesClass69.ThietLap_ThuocTinh();
								}
								else if (hcItimesClass69.ThuocTinh1.ThuocTinhLoaiHinh != 0)
								{
									hcItimesClass69.ThuocTinh1.ThuocTinhLoaiHinh = 0;
									hcItimesClass69.ThuocTinh1.ThuocTinhSoLuong = 0;
									hcItimesClass69.ThietLap_ThuocTinh();
								}
								SubtractItems(BitConverter.ToInt32(array2, 0), 1);
								if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass69.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass69.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass69.ItemGlobal_ID, 0))
								{
									SubtractItems(hcItimesClass69.Position, 1);
									AddItems(hcItimesClass69.ItemGlobal_ID, hcItimesClass69.VatPham_id, hcItimesClass69.Position, hcItimesClass69.VatPhamSoLuong, hcItimesClass69.VatPham_ThuocTinh);
								}
								Item_In_Bag[hcItimesClass69.Position].Khoa_Chat = false;
								SynthesisHint(num, 1, 0, Item_In_Bag[hcItimesClass69.Position]);
								HopThanhVatPham_Table.Clear();
								SynthesisSystemUnlocked();
								NguyenBao_HopThanh_MoRa = 0;
								OpenWarehouse = false;
								RxjhClass.SyntheticRecord(base.Userid, base.UserName, Item_In_Bag[hcItimesClass69.Position].DatDuocVatPhamTen_XungHao(), num, "八卦回天符", "Thanh Cong", Item_In_Bag[hcItimesClass69.Position]);
							}
						}
					}
				}
				goto default;
			case 51:
				try
				{
					if (base.CurrentOperationType == 102 && !HopThanhVatPham_Table.ContainsKey(1) && !Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai && Item_In_Bag[fromPos].VatPham_ThuocTinh_Manh <= 0)
					{
						HcItimesClass hcItimesClass144 = new HcItimesClass();
						hcItimesClass144.Position = fromPos;
						hcItimesClass144.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(1, hcItimesClass144);
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex79)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinhGiaiDoan 合成1 error![" + base.Userid + "]-[" + base.UserName + "]" + ex79.Message);
				}
				goto default;
			case 59:
				try
				{
					if (HopThanhVatPham_Table.TryGetValue(1, out var value73))
					{
						Item_In_Bag[value73.Position].Khoa_Chat = false;
						SynthesisHint(num, 1, 0, Item_In_Bag[value73.Position]);
					}
					HopThanhVatPham_Table.Clear();
					SynthesisSystemUnlocked();
					NguyenBao_HopThanh_MoRa = 0;
					OpenWarehouse = false;
				}
				catch (Exception ex75)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 八卦回天符 合成4 error![" + base.Userid + "]-[" + base.UserName + "]" + ex75.Message);
				}
				goto default;
			case 60:
				try
				{
					if (base.CurrentOperationType == 19 && HopThanhVatPham_Table != null && HopThanhVatPham_Table.Count > 0)
					{
						HcItimesClass hcItimesClass137 = null;
						HcItimesClass hcItimesClass139 = null;
						HcItimesClass hcItimesClass141 = null;
						if (HopThanhVatPham_Table.ContainsKey(1))
						{
							hcItimesClass137 = HopThanhVatPham_Table[1];
						}
						if (HopThanhVatPham_Table.ContainsKey(2))
						{
							hcItimesClass139 = HopThanhVatPham_Table[2];
						}
						if (HopThanhVatPham_Table.ContainsKey(3))
						{
							hcItimesClass141 = HopThanhVatPham_Table[3];
						}
						ItmeClass value74;
						if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass137.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass137.ItemGlobal_ID, 0))
						{
							SynthesisHint(61, 5, 0, Item_In_Bag[hcItimesClass137.Position]);
							HopThanhVatPham_Table.Clear();
						}
						else if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass139.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass139.ItemGlobal_ID, 0))
						{
							SynthesisHint(61, 5, 0, Item_In_Bag[hcItimesClass139.Position]);
							HopThanhVatPham_Table.Clear();
						}
						else if (hcItimesClass141 != null && BitConverter.ToInt64(Item_In_Bag[hcItimesClass141.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass141.ItemGlobal_ID, 0))
						{
							SynthesisHint(61, 5, 0, Item_In_Bag[hcItimesClass141.Position]);
							HopThanhVatPham_Table.Clear();
						}
						else if (World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass137.VatPham_id, 0), out value74))
						{
							if (value74.FLD_RESIDE2 != 1 && value74.FLD_RESIDE2 != 4)
							{
								SynthesisHint(61, 5, 0, Item_In_Bag[hcItimesClass137.Position]);
								HopThanhVatPham_Table.Clear();
							}
							else if (Item_In_Bag[hcItimesClass137.Position].Vat_Pham_Khoa_Lai)
							{
								SynthesisHint(num, 3, 0, Item_In_Bag[hcItimesClass137.Position]);
								HopThanhVatPham_Table.Clear();
							}
							else
							{
								hcItimesClass137.CuongHoaThuocTinhGiaiDoan();
								hcItimesClass139.CuongHoaThuocTinhGiaiDoan();
								Random random4 = new Random();
								string[] array7 = World.TongXacSuat_ThuocTinh.Split(';');
								double num64 = random4.Next(int.Parse(array7[0]), int.Parse(array7[1]));
								double num66 = hcItimesClass137.GiaiDoanSoLuong * 10;
								if (hcItimesClass137.GiaiDoanSoLuong >= 10)
								{
									SynthesisHint(61, 3, 0, Item_In_Bag[hcItimesClass137.Position]);
									HopThanhVatPham_Table.Clear();
								}
								else
								{
									if (hcItimesClass141 != null)
									{
										num64 += (double)LuckyCharmBonus(BitConverter.ToInt32(hcItimesClass141.VatPham_id, 0));
									}
									if (NguyenBao_HopThanh_MoRa == 1)
									{
										num64 += 5.0;
									}
									if (base.FLD_VIP == 1)
									{
									}
									if (World.TyLe_HopThanh != 0.0)
									{
										num64 += 100.0 * World.TyLe_HopThanh;
									}
									if (base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
									{
										num64 += 100.0 * base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
									}
									if (base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
									{
										num64 += 100.0 * base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
									}
									if (num64 >= num66)
									{
										if (hcItimesClass137.GiaiDoanSoLuong != 0 && hcItimesClass139.GiaiDoanLoaiHinh != hcItimesClass137.GiaiDoanLoaiHinh)
										{
											hcItimesClass137.GiaiDoanSoLuong = 0;
											hcItimesClass137.ThietLap_GiaiDoanThuocTinh();
											SynthesisHint(num, 0, 0, Item_In_Bag[hcItimesClass137.Position]);
											RxjhClass.SyntheticRecord(base.Userid, base.UserName, value74.ItmeNAME, num, "ThuocTinh", "ThatBai", Item_In_Bag[hcItimesClass137.Position]);
										}
										else
										{
											hcItimesClass137.GiaiDoanLoaiHinh = hcItimesClass139.GiaiDoanLoaiHinh;
											HcItimesClass hcItimesClass156 = hcItimesClass137;
											int giaiDoanSoLuong = hcItimesClass156.GiaiDoanSoLuong + 1;
											hcItimesClass156.GiaiDoanSoLuong = giaiDoanSoLuong;
											hcItimesClass137.ThietLap_GiaiDoanThuocTinh();
											SynthesisHint(num, 1, 0, Item_In_Bag[hcItimesClass137.Position]);
											RxjhClass.SyntheticRecord(base.Userid, base.UserName, value74.ItmeNAME, num, "ThuocTinh", "ThanhCong", Item_In_Bag[hcItimesClass137.Position]);
										}
									}
									else
									{
										hcItimesClass137.GiaiDoanSoLuong = 0;
										hcItimesClass137.ThietLap_GiaiDoanThuocTinh();
										SynthesisHint(num, 0, 0, Item_In_Bag[hcItimesClass137.Position]);
									}
									SubtractItems(hcItimesClass139.Position, 1);
									if (hcItimesClass141 != null)
									{
										SubtractItems(hcItimesClass141.Position, 1);
									}
									if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass137.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass137.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass137.ItemGlobal_ID, 0))
									{
										SubtractItems(hcItimesClass137.Position, 1);
										AddItems(hcItimesClass137.ItemGlobal_ID, hcItimesClass137.VatPham_id, hcItimesClass137.Position, hcItimesClass137.VatPhamSoLuong, hcItimesClass137.VatPham_ThuocTinh);
									}
									Item_In_Bag[hcItimesClass137.Position].Khoa_Chat = false;
									HopThanhVatPham_Table.Clear();
									SynthesisSystemUnlocked();
									NguyenBao_HopThanh_MoRa = 0;
								}
							}
						}
					}
				}
				catch (Exception ex76)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "HopThanhThuocTinh error![" + base.Userid + "]-[" + base.UserName + "]" + ex76.Message);
				}
				goto default;
			case 160:
				try
				{
					if (base.CurrentOperationType == 138 && HopThanhVatPham_Table != null && HopThanhVatPham_Table.Count > 0)
					{
						HcItimesClass hcItimesClass138 = null;
						HcItimesClass hcItimesClass140 = null;
						HcItimesClass hcItimesClass142 = null;
						byte[] array32 = new byte[4];
						byte[] dst5 = new byte[4];
						System.Buffer.BlockCopy(PacketData, 14, array32, 0, 4);
						System.Buffer.BlockCopy(PacketData, 18, dst5, 0, 4);
						BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array32, 0)].VatPham_ID, 0);
						if (HopThanhVatPham_Table.ContainsKey(1))
						{
							hcItimesClass138 = HopThanhVatPham_Table[1];
						}
						if (HopThanhVatPham_Table.ContainsKey(2))
						{
							hcItimesClass140 = HopThanhVatPham_Table[2];
						}
						if (HopThanhVatPham_Table.ContainsKey(3))
						{
							hcItimesClass142 = HopThanhVatPham_Table[3];
						}
						ItmeClass value75;
						if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass138.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass138.ItemGlobal_ID, 0))
						{
							SynthesisHint(161, 5, 0, Item_In_Bag[hcItimesClass138.Position]);
							HopThanhVatPham_Table.Clear();
						}
						else if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass140.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass140.ItemGlobal_ID, 0))
						{
							SynthesisHint(161, 5, 0, Item_In_Bag[hcItimesClass140.Position]);
							HopThanhVatPham_Table.Clear();
						}
						else if (hcItimesClass142 != null && BitConverter.ToInt64(Item_In_Bag[hcItimesClass142.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass142.ItemGlobal_ID, 0))
						{
							SynthesisHint(161, 5, 0, Item_In_Bag[hcItimesClass142.Position]);
							HopThanhVatPham_Table.Clear();
						}
						else if (World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass138.VatPham_id, 0), out value75))
						{
							if (value75.FLD_RESIDE2 != 1 && value75.FLD_RESIDE2 != 4)
							{
								SynthesisHint(161, 5, 0, Item_In_Bag[hcItimesClass138.Position]);
								HopThanhVatPham_Table.Clear();
							}
							else if (Item_In_Bag[hcItimesClass138.Position].Vat_Pham_Khoa_Lai)
							{
								SynthesisHint(num, 3, 0, Item_In_Bag[hcItimesClass138.Position]);
								HopThanhVatPham_Table.Clear();
							}
							else
							{
								hcItimesClass138.CuongHoaThuocTinhGiaiDoan();
								hcItimesClass140.CuongHoaThuocTinhGiaiDoan();
								double num65 = RNG.Next(0, 100);
								if (hcItimesClass138.GiaiDoanSoLuong >= 10)
								{
									SynthesisHint(161, 3, 0, Item_In_Bag[hcItimesClass138.Position]);
									HopThanhVatPham_Table.Clear();
								}
								else
								{
									if (hcItimesClass142 != null)
									{
										num65 += (double)LuckyCharmBonus(BitConverter.ToInt32(hcItimesClass142.VatPham_id, 0));
									}
									if (NguyenBao_HopThanh_MoRa == 1)
									{
										num65 += 5.0;
									}
									if (base.FLD_VIP == 1)
									{
									}
									if (World.TyLe_HopThanh != 0.0)
									{
										num65 += 100.0 * World.TyLe_HopThanh;
									}
									if (base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
									{
										num65 += 100.0 * base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
									}
									if (base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
									{
										num65 += 100.0 * base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
									}
									if ((hcItimesClass138.GiaiDoanSoLuong == 0 && num65 >= 100.0 - World.ThuocTinhChoThem_1) || (hcItimesClass138.GiaiDoanSoLuong == 1 && num65 >= 100.0 - World.ThuocTinhChoThem_2) || (hcItimesClass138.GiaiDoanSoLuong == 2 && num65 >= 100.0 - World.ThuocTinhChoThem_3) || (hcItimesClass138.GiaiDoanSoLuong == 3 && num65 >= 100.0 - World.ThuocTinhChoThem_4) || (hcItimesClass138.GiaiDoanSoLuong == 4 && num65 >= 100.0 - World.ThuocTinhChoThem_5) || (hcItimesClass138.GiaiDoanSoLuong == 5 && num65 >= 100.0 - World.ThuocTinhChoThem_6) || (hcItimesClass138.GiaiDoanSoLuong == 6 && num65 >= 100.0 - World.ThuocTinhChoThem_7) || (hcItimesClass138.GiaiDoanSoLuong == 7 && num65 >= 100.0 - World.ThuocTinhChoThem_8) || (hcItimesClass138.GiaiDoanSoLuong == 8 && num65 >= 100.0 - World.ThuocTinhChoThem_9) || (hcItimesClass138.GiaiDoanSoLuong == 9 && num65 >= 100.0 - World.ThuocTinhChoThem_10) || (hcItimesClass138.GiaiDoanSoLuong == 10 && num65 >= 100.0 - World.ThuocTinhChoThem_10))
									{
										if (hcItimesClass138.GiaiDoanSoLuong != 0 && hcItimesClass140.GiaiDoanLoaiHinh != hcItimesClass138.GiaiDoanLoaiHinh)
										{
											hcItimesClass138.GiaiDoanSoLuong = 0;
											hcItimesClass138.ThietLap_GiaiDoanThuocTinh();
											SynthesisHint(num, 0, 0, Item_In_Bag[hcItimesClass138.Position]);
											RxjhClass.SyntheticRecord(base.Userid, base.UserName, value75.ItmeNAME, num, "ThuocTinhChamr", "ThatBai", Item_In_Bag[hcItimesClass138.Position]);
										}
										else
										{
											hcItimesClass138.GiaiDoanLoaiHinh = hcItimesClass140.GiaiDoanLoaiHinh;
											int random16 = RNG.Next(1, 100);
											if (random16 < 5)
											{
												hcItimesClass138.GiaiDoanSoLuong += 3;
											}
											else if (random16 < 20)
											{
												hcItimesClass138.GiaiDoanSoLuong += 2;
											}
											else if (random16 < 100)
											{
												hcItimesClass138.GiaiDoanSoLuong++;
											}
											if (hcItimesClass138.GiaiDoanSoLuong > 10)
											{
												hcItimesClass138.GiaiDoanSoLuong = 10;
											}
											hcItimesClass138.ThietLap_GiaiDoanThuocTinh();
											SynthesisHint(num, 1, 0, Item_In_Bag[hcItimesClass138.Position]);
											RxjhClass.SyntheticRecord(base.Userid, base.UserName, value75.ItmeNAME, num, "ThuocTinhChamr", "ThanhCong", Item_In_Bag[hcItimesClass138.Position]);
										}
									}
									else
									{
										int random16z = RNG.Next(1, 100);
										if (random16z < 20)
										{
											hcItimesClass138.GiaiDoanSoLuong -= 3;
										}
										else if (random16z < 70)
										{
											hcItimesClass138.GiaiDoanSoLuong -= 2;
										}
										else if (random16z < 100)
										{
											hcItimesClass138.GiaiDoanSoLuong--;
										}
										if (hcItimesClass138.GiaiDoanSoLuong < 0)
										{
											hcItimesClass138.GiaiDoanSoLuong = 0;
										}
										hcItimesClass138.ThietLap_GiaiDoanThuocTinh();
										SynthesisHint(num, 0, 0, Item_In_Bag[hcItimesClass138.Position]);
									}
									SubtractItems(hcItimesClass140.Position, 1);
									SubtractItems(BitConverter.ToInt32(array32, 0), 1);
									if (hcItimesClass142 != null)
									{
										SubtractItems(hcItimesClass142.Position, 1);
									}
									if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass138.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass138.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass138.ItemGlobal_ID, 0))
									{
										SubtractItems(hcItimesClass138.Position, 1);
										AddItems(hcItimesClass138.ItemGlobal_ID, hcItimesClass138.VatPham_id, hcItimesClass138.Position, hcItimesClass138.VatPhamSoLuong, hcItimesClass138.VatPham_ThuocTinh);
									}
									Item_In_Bag[hcItimesClass138.Position].Khoa_Chat = false;
									HopThanhVatPham_Table.Clear();
									SynthesisSystemUnlocked();
									NguyenBao_HopThanh_MoRa = 0;
								}
							}
						}
					}
				}
				catch (Exception ex77)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 合成ThuocTinhGiaiDoan 合成 error![" + base.Userid + "]-[" + base.UserName + "]" + ex77.Message);
				}
				goto default;
			case 61:
				try
				{
					if (base.CurrentOperationType == 19)
					{
						if (HopThanhVatPham_Table.ContainsKey(1))
						{
							SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
						}
						else if (!Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai && Item_In_Bag[fromPos].FLDThuocTinhSoLuong < 10)
						{
							if (World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0), out var value78) && value78.FLD_RESIDE2 != 1 && value78.FLD_RESIDE2 != 4)
							{
								SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
							}
							else
							{
								HcItimesClass hcItimesClass149 = new HcItimesClass();
								hcItimesClass149.Position = fromPos;
								hcItimesClass149.VatPham = Item_In_Bag[fromPos].VatPham_byte;
								Item_In_Bag[fromPos].Khoa_Chat = true;
								HopThanhVatPham_Table.Add(1, hcItimesClass149);
								SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
							}
						}
						else
						{
							SynthesisHint(num, 3, 0, Item_In_Bag[fromPos]);
						}
					}
				}
				catch (Exception ex84)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinhGiaiDoan 合成1 error![" + base.Userid + "]-[" + base.UserName + "]" + ex84.Message);
				}
				goto default;
			case 161:
				try
				{
					if (base.CurrentOperationType == 138)
					{
						if (HopThanhVatPham_Table.ContainsKey(1))
						{
							SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
						}
						else if (!Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai && Item_In_Bag[fromPos].FLDThuocTinhSoLuong < 10)
						{
							if (World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0), out var vp214215) && vp214215.FLD_RESIDE2 != 1 && vp214215.FLD_RESIDE2 != 4)
							{
								SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
							}
							else
							{
								HcItimesClass hcItimesClass59 = new HcItimesClass();
								hcItimesClass59.Position = fromPos;
								hcItimesClass59.VatPham = Item_In_Bag[fromPos].VatPham_byte;
								Item_In_Bag[fromPos].Khoa_Chat = true;
								HopThanhVatPham_Table.Add(1, hcItimesClass59);
								SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
							}
						}
						else
						{
							SynthesisHint(num, 3, 0, Item_In_Bag[fromPos]);
						}
					}
				}
				catch (Exception ex22)
				{
					Form1.WriteLine(1, "214 - 215 PutItem1 error![" + base.Userid + "]-[" + base.UserName + "]" + ex22.Message);
				}
				goto default;
			case 62:
				try
				{
					if (base.CurrentOperationType == 19 && HopThanhVatPham_Table.Count != 0 && !HopThanhVatPham_Table.ContainsKey(2))
					{
						HcItimesClass hcItimesClass14 = new HcItimesClass();
						hcItimesClass14.Position = fromPos;
						hcItimesClass14.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(2, hcItimesClass14);
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex96)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinhGiaiDoan 合成2 error![" + base.Userid + "]-[" + base.UserName + "]" + ex96.Message);
				}
				goto default;
			case 162:
				try
				{
					if (base.CurrentOperationType == 138 && HopThanhVatPham_Table.Count != 0 && !HopThanhVatPham_Table.ContainsKey(2))
					{
						HcItimesClass hcItimesClass15 = new HcItimesClass();
						hcItimesClass15.Position = fromPos;
						hcItimesClass15.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(2, hcItimesClass15);
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex97)
				{
					Form1.WriteLine(1, "214 - 215 PutItem2 error![" + base.Userid + "]-[" + base.UserName + "]" + ex97.Message);
				}
				goto default;
			case 63:
				try
				{
					if (base.CurrentOperationType == 19 && HopThanhVatPham_Table.Count != 0 && !HopThanhVatPham_Table.ContainsKey(3))
					{
						HcItimesClass hcItimesClass145 = new HcItimesClass();
						hcItimesClass145.Position = fromPos;
						hcItimesClass145.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(3, hcItimesClass145);
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex80)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinhGiaiDoan 合成3 error![" + base.Userid + "]-[" + base.UserName + "]" + ex80.Message);
				}
				goto default;
			case 163:
				try
				{
					if (base.CurrentOperationType == 138 && HopThanhVatPham_Table.Count != 0 && !HopThanhVatPham_Table.ContainsKey(3))
					{
						HcItimesClass hcItimesClass146 = new HcItimesClass();
						hcItimesClass146.Position = fromPos;
						hcItimesClass146.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(3, hcItimesClass146);
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex81)
				{
					Form1.WriteLine(1, "214 - 215 PutItem3 error![" + base.Userid + "]-[" + base.UserName + "]" + ex81.Message);
				}
				goto default;
			case 20:
				try
				{
					HcItimesClass hcItimesClass112;
					HcItimesClass hcItimesClass114;
					HcItimesClass hcItimesClass116;
					ItmeClass value62;
					if (base.CurrentOperationType == 6 && HopThanhVatPham_Table.Count > 0)
					{
						hcItimesClass112 = null;
						hcItimesClass114 = null;
						hcItimesClass116 = null;
						if (HopThanhVatPham_Table.ContainsKey(1))
						{
							hcItimesClass112 = HopThanhVatPham_Table[1];
						}
						if (HopThanhVatPham_Table.ContainsKey(2))
						{
							hcItimesClass114 = HopThanhVatPham_Table[2];
						}
						if (HopThanhVatPham_Table.ContainsKey(3))
						{
							hcItimesClass116 = HopThanhVatPham_Table[3];
						}
						if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass112.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass112.ItemGlobal_ID, 0) && BitConverter.ToInt64(Item_In_Bag[hcItimesClass114.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass114.ItemGlobal_ID, 0) && (hcItimesClass116 == null || BitConverter.ToInt64(Item_In_Bag[hcItimesClass116.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass116.ItemGlobal_ID, 0)))
						{
							hcItimesClass112.DatDuocThuocTinh();
							hcItimesClass112.CuongHoaThuocTinhGiaiDoan();
							hcItimesClass114.CuongHoaThuocTinhGiaiDoan();
							if (Item_In_Bag[hcItimesClass112.Position].Vat_Pham_Khoa_Lai)
							{
								SynthesisHint(21, 3, 0, Item_In_Bag[hcItimesClass112.Position]);
								HopThanhVatPham_Table.Clear();
							}
							else if (World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass112.VatPham_id, 0), out value62))
							{
								if (value62.FLD_RESIDE2 != 1 && value62.FLD_RESIDE2 != 2 && value62.FLD_RESIDE2 != 4 && value62.FLD_RESIDE2 != 5 && value62.FLD_RESIDE2 != 6 && value62.FLD_RESIDE2 != 12)
								{
									SynthesisHint(21, 5, 0, Item_In_Bag[hcItimesClass112.Position]);
									HopThanhVatPham_Table.Clear();
								}
								else if (value62.FLD_RESIDE2 == 12)
								{
									if (BitConverter.ToInt32(hcItimesClass114.VatPham_id, 0) == 800000013)
									{
										goto IL_891f;
									}
									SynthesisHint(21, 5, 0, Item_In_Bag[hcItimesClass112.Position]);
									HopThanhVatPham_Table.Clear();
									Form1.WriteLine(6, "Synthesis system WGF BUG1[" + BitConverter.ToInt32(hcItimesClass112.VatPham_id, 0) + "]-[" + BitConverter.ToInt32(hcItimesClass114.VatPham_id, 0) + "]");
								}
								else
								{
									if (value62.FLD_RESIDE2 == 1 || value62.FLD_RESIDE2 == 2 || value62.FLD_RESIDE2 == 5 || value62.FLD_RESIDE2 == 6)
									{
										int num46 = BitConverter.ToInt32(hcItimesClass114.VatPham_id, 0);
										switch (num46)
										{
										default:
											SynthesisHint(21, 5, 0, Item_In_Bag[hcItimesClass112.Position]);
											HopThanhVatPham_Table.Clear();
											Form1.WriteLine(6, "Synthesis system WGF BUG2[" + BitConverter.ToInt32(hcItimesClass112.VatPham_id, 0) + "]-[" + BitConverter.ToInt32(hcItimesClass114.VatPham_id, 0) + "]");
											return;
										case 800000002:
										case 800000024:
										case 800000062:
										case 800000068:
											break;
										}
										if (value62.FLD_LEVEL >= 150)
										{
											if (num46 != 800000068)
											{
												SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
												break;
											}
										}
										else if (value62.FLD_LEVEL >= 130)
										{
											if (num46 != 800000062)
											{
												SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
												break;
											}
										}
										else if (value62.FLD_LEVEL >= 80 && value62.FLD_LEVEL < 130)
										{
											if (num46 != 800000002 && num46 != 800000024)
											{
												SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
												break;
											}
										}
										else if (num46 != 800000002)
										{
											SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
											break;
										}
										if (value62.FLD_RESIDE2 == 6 && (hcItimesClass112.ThuocTinh2.ThuocTinhSoLuong != 0 || hcItimesClass112.ThuocTinh3.ThuocTinhSoLuong != 0 || hcItimesClass112.ThuocTinh4.ThuocTinhSoLuong != 0))
										{
											break;
										}
										goto IL_891f;
									}
									if (value62.FLD_RESIDE2 == 4)
									{
										int num48 = BitConverter.ToInt32(hcItimesClass114.VatPham_id, 0);
										if (num48 != 800000001 && num48 != 800000023 && num48 != 800000061 && num48 != 800000067)
										{
											SynthesisHint(21, 5, 0, Item_In_Bag[hcItimesClass112.Position]);
											HopThanhVatPham_Table.Clear();
											Form1.WriteLine(6, "Synthesis system WGF BUG3[" + BitConverter.ToInt32(hcItimesClass112.VatPham_id, 0) + "]-[" + BitConverter.ToInt32(hcItimesClass114.VatPham_id, 0) + "]");
											break;
										}
										if (value62.FLD_LEVEL >= 150)
										{
											if (num48 != 800000067)
											{
												SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
												break;
											}
										}
										else if (value62.FLD_LEVEL >= 130)
										{
											if (num48 != 800000061)
											{
												SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
												break;
											}
										}
										else if (value62.FLD_LEVEL >= 80 && value62.FLD_LEVEL < 130)
										{
											if (num48 != 800000001 && num48 != 800000023)
											{
												SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
												break;
											}
										}
										else if (num48 != 800000001)
										{
											SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
											break;
										}
										goto IL_891f;
									}
									SynthesisHint(21, 5, 0, Item_In_Bag[hcItimesClass112.Position]);
									HopThanhVatPham_Table.Clear();
								}
							}
							else
							{
								SynthesisHint(21, 5, 0, Item_In_Bag[hcItimesClass112.Position]);
								HopThanhVatPham_Table.Clear();
							}
						}
					}
					goto end_IL_76ad;
					IL_891f:
					if (hcItimesClass112.CuongHoaSoLuong > 0)
					{
						SynthesisHint(21, 3, 0, Item_In_Bag[hcItimesClass112.Position]);
						HopThanhVatPham_Table.Clear();
					}
					else
					{
						if (Phi_HopThanh <= 0)
						{
							goto IL_8075;
						}
						if (value62.FLD_NJ == 0)
						{
							if (base.Player_Money >= Phi_HopThanh)
							{
								base.Player_Money -= Phi_HopThanh;
								UpdateMoneyAndWeight();
								goto IL_8075;
							}
						}
						else if (base.Player_Money >= Phi_HopThanh)
						{
							hcItimesClass112.FLD_FJ_NJ = 0;
							base.Player_Money -= Phi_HopThanh;
							UpdateMoneyAndWeight();
							Update_Item_In_Bag();
							goto IL_8075;
						}
						SynthesisHint(11, 4, 0, Item_In_Bag[hcItimesClass112.Position]);
						HopThanhVatPham_Table.Clear();
					}
					goto end_IL_76ad;
					IL_8075:
					Random random2 = new Random();
					string[] array5 = World.TongXacSuat_HopThanh.Split(';');
					double num50 = random2.Next(int.Parse(array5[0]), int.Parse(array5[1]));
					double num52 = 100.0;
					if (hcItimesClass112.ThuocTinh1.ThuocTinhSoLuong == 0 && hcItimesClass112.ThuocTinh2.ThuocTinhSoLuong == 0 && hcItimesClass112.ThuocTinh3.ThuocTinhSoLuong == 0 && hcItimesClass112.ThuocTinh4.ThuocTinhSoLuong == 0)
					{
						num52 -= World.HopThanhVatPham_Dong1;
					}
					else if (hcItimesClass112.ThuocTinh1.ThuocTinhSoLuong != 0 && hcItimesClass112.ThuocTinh2.ThuocTinhSoLuong == 0 && hcItimesClass112.ThuocTinh3.ThuocTinhSoLuong == 0 && hcItimesClass112.ThuocTinh4.ThuocTinhSoLuong == 0)
					{
						num52 -= World.HopThanhVatPham_Dong2;
					}
					else if (hcItimesClass112.ThuocTinh1.ThuocTinhSoLuong != 0 && hcItimesClass112.ThuocTinh2.ThuocTinhSoLuong != 0 && hcItimesClass112.ThuocTinh3.ThuocTinhSoLuong == 0 && hcItimesClass112.ThuocTinh4.ThuocTinhSoLuong == 0)
					{
						num52 -= World.HopThanhVatPham_Dong3;
					}
					else if (hcItimesClass112.ThuocTinh1.ThuocTinhSoLuong != 0 && hcItimesClass112.ThuocTinh2.ThuocTinhSoLuong != 0 && hcItimesClass112.ThuocTinh3.ThuocTinhSoLuong != 0 && hcItimesClass112.ThuocTinh4.ThuocTinhSoLuong == 0)
					{
						num52 -= World.HopThanhVatPham_Dong4;
					}
					if (hcItimesClass112.ThuocTinh1.ThuocTinhSoLuong != 0 && hcItimesClass112.ThuocTinh2.ThuocTinhSoLuong != 0 && hcItimesClass112.ThuocTinh3.ThuocTinhSoLuong != 0 && hcItimesClass112.ThuocTinh4.ThuocTinhSoLuong != 0)
					{
						SynthesisHint(21, 3, 0, Item_In_Bag[hcItimesClass112.Position]);
						HopThanhVatPham_Table.Clear();
					}
					else
					{
						if (hcItimesClass116 != null)
						{
							num50 += (double)LuckyCharmBonus(BitConverter.ToInt32(hcItimesClass116.VatPham_id, 0));
						}
						if (NguyenBao_HopThanh_MoRa == 1)
						{
							num50 += 5.0;
						}
						if (base.FLD_VIP == 1)
						{
						}
						if (World.TyLe_HopThanh != 0.0)
						{
							num50 += 100.0 * World.TyLe_HopThanh;
						}
						if (base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
						{
							num50 += 100.0 * base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
						}
						if (base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
						{
							num50 += 100.0 * base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
						}
						if (num50 >= num52)
						{
							if (hcItimesClass112.ThuocTinh1.ThuocTinhLoaiHinh == 0)
							{
								if (hcItimesClass114.GiaiDoanLoaiHinh == 8)
								{
									hcItimesClass112.ThuocTinh1.KhiCongThuocTinhLoaiHinh = hcItimesClass114.KhiCongThuocTinhLoaiHinh;
								}
								hcItimesClass112.ThuocTinh1.ThuocTinhLoaiHinh = hcItimesClass114.GiaiDoanLoaiHinh;
								hcItimesClass112.ThuocTinh1.ThuocTinhSoLuong = hcItimesClass114.GiaiDoanSoLuong;
								hcItimesClass112.ThietLap_ThuocTinh();
							}
							else if (hcItimesClass112.ThuocTinh2.ThuocTinhLoaiHinh == 0)
							{
								if (hcItimesClass114.GiaiDoanLoaiHinh == 8)
								{
									hcItimesClass112.ThuocTinh2.KhiCongThuocTinhLoaiHinh = hcItimesClass114.KhiCongThuocTinhLoaiHinh;
								}
								hcItimesClass112.ThuocTinh2.ThuocTinhLoaiHinh = hcItimesClass114.GiaiDoanLoaiHinh;
								hcItimesClass112.ThuocTinh2.ThuocTinhSoLuong = hcItimesClass114.GiaiDoanSoLuong;
								hcItimesClass112.ThietLap_ThuocTinh();
							}
							else if (hcItimesClass112.ThuocTinh3.ThuocTinhLoaiHinh == 0)
							{
								if (hcItimesClass114.GiaiDoanLoaiHinh == 8)
								{
									hcItimesClass112.ThuocTinh3.KhiCongThuocTinhLoaiHinh = hcItimesClass114.KhiCongThuocTinhLoaiHinh;
								}
								hcItimesClass112.ThuocTinh3.ThuocTinhLoaiHinh = hcItimesClass114.GiaiDoanLoaiHinh;
								hcItimesClass112.ThuocTinh3.ThuocTinhSoLuong = hcItimesClass114.GiaiDoanSoLuong;
								hcItimesClass112.ThietLap_ThuocTinh();
							}
							else if (hcItimesClass112.ThuocTinh4.ThuocTinhLoaiHinh == 0)
							{
								if (hcItimesClass114.GiaiDoanLoaiHinh == 8)
								{
									hcItimesClass112.ThuocTinh4.KhiCongThuocTinhLoaiHinh = hcItimesClass114.KhiCongThuocTinhLoaiHinh;
								}
								hcItimesClass112.ThuocTinh4.ThuocTinhLoaiHinh = hcItimesClass114.GiaiDoanLoaiHinh;
								hcItimesClass112.ThuocTinh4.ThuocTinhSoLuong = hcItimesClass114.GiaiDoanSoLuong;
								hcItimesClass112.ThietLap_ThuocTinh();
							}
							RxjhClass.SyntheticRecord(base.Userid, base.UserName, value62.ItmeNAME, num, "HopThanh", "Thanh Cong", Item_In_Bag[hcItimesClass112.Position]);
							SynthesisHint(num, 1, Phi_HopThanh, Item_In_Bag[hcItimesClass112.Position]);
						}
						else
						{
							RxjhClass.SyntheticRecord(base.Userid, base.UserName, value62.ItmeNAME, num, "HopThanh", "That Bai", Item_In_Bag[hcItimesClass112.Position]);
							SynthesisHint(num, 0, Phi_HopThanh, Item_In_Bag[hcItimesClass112.Position]);
						}
						SubtractItems(hcItimesClass114.Position, 1);
						if (hcItimesClass116 != null)
						{
							SubtractItems(hcItimesClass116.Position, 1);
						}
						if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass112.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass112.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass112.ItemGlobal_ID, 0))
						{
							SubtractItems(hcItimesClass112.Position, 1);
							AddItems(hcItimesClass112.ItemGlobal_ID, hcItimesClass112.VatPham_id, hcItimesClass112.Position, hcItimesClass112.VatPhamSoLuong, hcItimesClass112.VatPham_ThuocTinh);
						}
						Item_In_Bag[hcItimesClass112.Position].Khoa_Chat = false;
						HopThanhVatPham_Table.Clear();
						SynthesisSystemUnlocked();
						NguyenBao_HopThanh_MoRa = 0;
						Phi_HopThanh = 0;
					}
					end_IL_76ad:;
				}
				catch (Exception ex68)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "Synthesis system error![" + base.Userid + "]-[" + base.UserName + "]" + ex68.Message);
				}
				goto default;
			case 21:
				try
				{
					if (base.CurrentOperationType == 6)
					{
						if (Item_In_Bag[fromPos].Khoa_Chat)
						{
							SynthesisHint(num, 3, 0, Item_In_Bag[fromPos]);
						}
						else if (!Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai && Item_In_Bag[fromPos].FLD_CuongHoaSoLuong <= 0)
						{
							ItmeClass value61;
							if (Item_In_Bag[fromPos].FLD_MAGIC1 != 0 && Item_In_Bag[fromPos].FLD_MAGIC2 != 0 && Item_In_Bag[fromPos].FLD_MAGIC3 != 0 && Item_In_Bag[fromPos].FLD_MAGIC4 != 0)
							{
								SynthesisHint(num, 3, 0, Item_In_Bag[fromPos]);
							}
							else if (World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0), out value61))
							{
								if (value61.FLD_RESIDE2 != 1 && value61.FLD_RESIDE2 != 2 && value61.FLD_RESIDE2 != 4 && value61.FLD_RESIDE2 != 5 && value61.FLD_RESIDE2 != 6 && value61.FLD_RESIDE2 != 12)
								{
									SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
								}
								else if (!HopThanhVatPham_Table.ContainsKey(1))
								{
									int SoTien4 = (Phi_HopThanh = CalculateSyntheticEnhancementCost(value61, fromPos, num));
									HcItimesClass hcItimesClass111 = new HcItimesClass();
									hcItimesClass111.Position = fromPos;
									hcItimesClass111.VatPham = Item_In_Bag[fromPos].VatPham_byte;
									Item_In_Bag[fromPos].Khoa_Chat = true;
									HopThanhVatPham_Table.Add(1, hcItimesClass111);
									SynthesisHint(num, 1, SoTien4, Item_In_Bag[fromPos]);
								}
							}
							else
							{
								SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
							}
						}
						else
						{
							SynthesisHint(num, 3, 0, Item_In_Bag[fromPos]);
						}
					}
				}
				catch (Exception ex67)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成1 error![" + base.Userid + "]-[" + base.UserName + "]" + ex67.Message);
				}
				goto default;
			case 22:
				try
				{
					if (base.CurrentOperationType == 6)
					{
						if (HopThanhVatPham_Table != null && HopThanhVatPham_Table.Count != 0 && HopThanhVatPham_Table.ContainsKey(1))
						{
							HcItimesClass hcItimesClass147 = HopThanhVatPham_Table[1];
							if (World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass147.VatPham_id, 0), out var value76) && !HopThanhVatPham_Table.ContainsKey(2))
							{
								if (value76.FLD_RESIDE2 != 1 && value76.FLD_RESIDE2 != 2 && value76.FLD_RESIDE2 != 5 && value76.FLD_RESIDE2 != 6)
								{
									if (value76.FLD_RESIDE2 == 4)
									{
										if (Item_In_Bag[fromPos].GetVatPham_ID != 800000061 && Item_In_Bag[fromPos].GetVatPham_ID != 800000023 && Item_In_Bag[fromPos].GetVatPham_ID != 800000001 && Item_In_Bag[fromPos].GetVatPham_ID != 800000067)
										{
											SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
										}
										else if (value76.FLD_LEVEL < 80)
										{
											if (Item_In_Bag[fromPos].GetVatPham_ID == 800000001)
											{
												goto IL_9427;
											}
											SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
										}
										else if (value76.FLD_LEVEL >= 80 && value76.FLD_LEVEL < 130)
										{
											if (Item_In_Bag[fromPos].GetVatPham_ID == 800000001 || Item_In_Bag[fromPos].GetVatPham_ID == 800000023)
											{
												goto IL_9427;
											}
											SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
										}
										else if (value76.FLD_LEVEL >= 130 && value76.FLD_LEVEL < 150)
										{
											if (Item_In_Bag[fromPos].GetVatPham_ID == 800000061)
											{
												goto IL_9427;
											}
											SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
										}
										else
										{
											if (Item_In_Bag[fromPos].GetVatPham_ID == 800000067 && value76.FLD_LEVEL >= 150)
											{
												goto IL_9427;
											}
											SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
										}
									}
									else if (value76.FLD_RESIDE2 == 12)
									{
										if (Item_In_Bag[fromPos].GetVatPham_ID != 800000013)
										{
											SynthesisHint(num, 5, 0, Item_In_Bag[hcItimesClass147.Position]);
										}
										else
										{
											if (Item_In_Bag[fromPos].FLD_MAGIC0 != 0)
											{
												goto IL_9427;
											}
											Item_In_Bag[fromPos] = new X_Vat_Pham_Loai(new byte[World.Item_Db_Byte_Length]);
											SynthesisHint(num, 5, 0, Item_In_Bag[hcItimesClass147.Position]);
										}
									}
									else
									{
										SynthesisHint(num, 5, 0, Item_In_Bag[hcItimesClass147.Position]);
									}
								}
								else if (Item_In_Bag[fromPos].GetVatPham_ID != 800000062 && Item_In_Bag[fromPos].GetVatPham_ID != 800000024 && Item_In_Bag[fromPos].GetVatPham_ID != 800000002 && Item_In_Bag[fromPos].GetVatPham_ID != 800000068)
								{
									SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
								}
								else if (value76.FLD_LEVEL >= 150)
								{
									if (Item_In_Bag[fromPos].GetVatPham_ID == 800000068)
									{
										goto IL_9427;
									}
									SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
								}
								else if (value76.FLD_LEVEL >= 130 && value76.FLD_LEVEL < 150)
								{
									if (Item_In_Bag[fromPos].GetVatPham_ID == 800000062)
									{
										goto IL_9427;
									}
									SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
								}
								else if (value76.FLD_LEVEL >= 80 && value76.FLD_LEVEL < 130)
								{
									if (Item_In_Bag[fromPos].GetVatPham_ID == 800000002 || Item_In_Bag[fromPos].GetVatPham_ID == 800000024)
									{
										goto IL_9427;
									}
									SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
								}
								else
								{
									if (Item_In_Bag[fromPos].GetVatPham_ID == 800000002)
									{
										goto IL_9427;
									}
									SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
								}
							}
						}
						else
						{
							SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
						}
					}
					goto end_IL_8db2;
					IL_9427:
					HopThanhVatPham_Table.Add(2, new HcItimesClass
					{
						Position = fromPos,
						VatPham = Item_In_Bag[fromPos].VatPham_byte
					});
					Item_In_Bag[fromPos].Khoa_Chat = true;
					SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					end_IL_8db2:;
				}
				catch (Exception ex82)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成2 error![" + base.Userid + "]-[" + base.UserName + "]" + ex82.Message);
				}
				goto default;
			case 23:
				try
				{
					if (base.CurrentOperationType == 6)
					{
						if (HopThanhVatPham_Table != null && HopThanhVatPham_Table.Count != 0 && HopThanhVatPham_Table.ContainsKey(1))
						{
							if (!HopThanhVatPham_Table.ContainsKey(3))
							{
								HopThanhVatPham_Table.Add(3, new HcItimesClass
								{
									Position = fromPos,
									VatPham = Item_In_Bag[fromPos].VatPham_byte
								});
								Item_In_Bag[fromPos].Khoa_Chat = true;
								SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
							}
						}
						else
						{
							SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
						}
					}
				}
				catch (Exception ex64)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成3 error![" + base.Userid + "]-[" + base.UserName + "]" + ex64.Message);
				}
				goto default;
			case 29:
				try
				{
					if (HopThanhVatPham_Table.TryGetValue(1, out var value56))
					{
						Item_In_Bag[value56.Position].Khoa_Chat = false;
						SynthesisHint(num, 1, 0, Item_In_Bag[value56.Position]);
					}
					HopThanhVatPham_Table.Clear();
					SynthesisSystemUnlocked();
					NguyenBao_HopThanh_MoRa = 0;
				}
				catch (Exception ex66)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成4 error![" + base.Userid + "]-[" + base.UserName + "]" + ex66.Message);
				}
				goto default;
			case 30:
				try
				{
					if (base.CurrentOperationType == 14)
					{
						if (HopThanhVatPham_Table != null && HopThanhVatPham_Table.Count == 4)
						{
							if (HopThanhVatPham_Table[1] != null && HopThanhVatPham_Table[2] != null && HopThanhVatPham_Table[3] != null && HopThanhVatPham_Table[4] != null)
							{
								CheckTheNumberOfIngotsInBaibaoge();
								if (World.MoiLanTaiTao_TieuHaoThietLap == 0)
								{
									if (base.Player_Money >= World.MoiLan_TieuHaoSoLuong)
									{
										base.Player_Money -= World.MoiLan_TieuHaoSoLuong;
										UpdateMoneyAndWeight();
										goto IL_98ac;
									}
									SynthesisHint(num, 4, 0, Item_In_Bag[fromPos]);
								}
								else
								{
									if (base.FLD_RXPIONT >= World.MoiLan_TieuHaoSoLuong)
									{
										KiemSoatNguyenBao_SoLuong(World.MoiLan_TieuHaoSoLuong, 0);
										RxjhClass.BachBaoCacRecord(base.Userid, base.UserName, 0.0, "再造合成石消耗", 1, World.MoiLan_TieuHaoSoLuong);
										Save_NguyenBaoData();
										goto IL_98ac;
									}
									SynthesisHint(num, 4, 0, Item_In_Bag[fromPos]);
									HeThongNhacNho("NguyenBao不足,再造合成石需要扣除" + World.MoiLan_TieuHaoSoLuong + "NguyenBao/次.");
								}
							}
						}
						else
						{
							SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
							HeThongNhacNho("石头放少了,再造合成石最少需要4颗同LoaiHinh的石头.");
						}
					}
					goto end_IL_96e7;
					IL_98ac:
					int num58 = RNG.Next(0, 200);
					int num60 = RNG.Next(0, 120);
					string text = "3";
					int num61 = 0;
					int value67 = 0;
					int num63 = BitConverter.ToInt32(HopThanhVatPham_Table[1].VatPham_id, 0);
					HcItimesClass hcItimesClass121 = HopThanhVatPham_Table[1];
					HcItimesClass hcItimesClass122 = HopThanhVatPham_Table[2];
					HcItimesClass hcItimesClass123 = HopThanhVatPham_Table[3];
					HcItimesClass hcItimesClass125 = HopThanhVatPham_Table[4];
					if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass121.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass121.ItemGlobal_ID, 0) && BitConverter.ToInt64(Item_In_Bag[hcItimesClass122.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass122.ItemGlobal_ID, 0) && BitConverter.ToInt64(Item_In_Bag[hcItimesClass123.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass123.ItemGlobal_ID, 0) && BitConverter.ToInt64(Item_In_Bag[hcItimesClass125.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass125.ItemGlobal_ID, 0))
					{
						if (base.FLD_VIP == 1)
						{
						}
						int value68;
						switch (num63)
						{
						default:
							return;
						case 800000002:
							value68 = 800000002;
							if (num58 >= 0 && num58 <= 40)
							{
								text = "3";
							}
							else if (num58 > 40 && num58 <= 80)
							{
								text = "4";
							}
							else if (num58 > 80 && num58 <= 120)
							{
								text = "6";
							}
							else if (num58 > 120 && num58 <= 160)
							{
								text = "2";
							}
							else if (num58 > 160 && num58 <= 200)
							{
								text = "2";
							}
							else if (num58 > 200 && num58 <= 240)
							{
								text = "11";
							}
							switch (text)
							{
							case "11":
							{
								string[] array29 = World.TaiTao_HanNgocThach_VoPhong.Split(';');
								num61 = RNG.Next(int.Parse(array29[0]), int.Parse(array29[1]));
								break;
							}
							case "6":
							{
								string[] array28 = World.TaiTao_HanNgocThach_NeTranh.Split(';');
								num61 = RNG.Next(int.Parse(array28[0]), int.Parse(array28[1]));
								break;
							}
							case "4":
							{
								string[] array27 = World.TaiTao_HanNgocThach_NoiCong.Split(';');
								num61 = RNG.Next(int.Parse(array27[0]), int.Parse(array27[1]));
								break;
							}
							case "3":
							{
								string[] array26 = World.TaiTao_HanNgocThach_SinhMenh.Split(';');
								num61 = RNG.Next(int.Parse(array26[0]), int.Parse(array26[1]));
								break;
							}
							case "2":
							{
								string[] array25 = World.TaiTao_HanNgocThach_PhongNgu.Split(';');
								num61 = RNG.Next(int.Parse(array25[0]), int.Parse(array25[1]));
								break;
							}
							}
							if (num60 > 0 && num60 <= 40)
							{
								value68 = 800000002;
							}
							else if (num60 > 40 && num60 <= 80)
							{
								value68 = 800000024;
							}
							else if (num60 > 80 && num60 <= 135)
							{
								value68 = 800000062;
							}
							break;
						case 800000001:
							value68 = 800000001;
							if (num58 >= 0 && num58 <= 40)
							{
								text = "3";
							}
							else if (num58 > 40 && num58 <= 80)
							{
								text = "5";
							}
							else if (num58 > 80 && num58 <= 120)
							{
								text = "10";
							}
							else if (num58 > 120 && num58 <= 160)
							{
								text = "1";
							}
							else if (num58 > 160 && num58 <= 200)
							{
								text = "1";
							}
							else if (num58 > 200 && num58 <= 240)
							{
								text = "7";
							}
							switch (text)
							{
							case "10":
							{
								string[] array23 = World.TaiTao_DaKimCuong_TruyThuong.Split(';');
								num61 = RNG.Next(int.Parse(array23[0]), int.Parse(array23[1]));
								break;
							}
							case "7":
							{
								string[] array22 = World.TaiTao_DaKimCuong_VoCong.Split(';');
								num61 = RNG.Next(int.Parse(array22[0]), int.Parse(array22[1]));
								break;
							}
							case "5":
							{
								string[] array21 = World.TaiTao_DaKimCuong_TrungDich.Split(';');
								num61 = RNG.Next(int.Parse(array21[0]), int.Parse(array21[1]));
								break;
							}
							case "3":
							{
								string[] array20 = World.TaiTao_DaKimCuong_SinhMenh.Split(';');
								num61 = RNG.Next(int.Parse(array20[0]), int.Parse(array20[1]));
								break;
							}
							case "1":
							{
								string[] array19 = World.TaiTao_DaKimCuong_CongKich.Split(';');
								num61 = RNG.Next(int.Parse(array19[0]), int.Parse(array19[1]));
								break;
							}
							}
							if (num60 > 0 && num60 <= 40)
							{
								value68 = 800000001;
							}
							else if (num60 > 40 && num60 <= 80)
							{
								value68 = 800000023;
							}
							else if (num60 > 80 && num60 <= 135)
							{
								value68 = 800000061;
							}
							break;
						}
						switch (num61.ToString().Length)
						{
						case 1:
							value67 = int.Parse(text + "0000" + num61);
							break;
						case 2:
							value67 = int.Parse(text + "000" + num61);
							break;
						case 3:
							value67 = int.Parse(text + "00" + num61);
							break;
						case 4:
							value67 = int.Parse(text + "0" + num61);
							break;
						case 5:
							value67 = int.Parse(text + num61);
							break;
						}
						SubtractItems(hcItimesClass121.Position, 1);
						SubtractItems(hcItimesClass122.Position, 1);
						SubtractItems(hcItimesClass123.Position, 1);
						SubtractItems(hcItimesClass125.Position, 1);
						AddItems(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(value68), hcItimesClass121.Position, BitConverter.GetBytes(1), BitConverter.GetBytes(value67));
						HopThanhVatPham_Table.Clear();
						SynthesisSystemUnlocked();
						NguyenBao_HopThanh_MoRa = 0;
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
					end_IL_96e7:;
				}
				catch (Exception ex71)
				{
					Form1.WriteLine(1, "合成系统 再造合成石-确认再造 error![" + base.Userid + "]-[" + base.UserName + "]" + ex71.Message);
				}
				goto default;
			case 32:
				try
				{
					if (base.CurrentOperationType == 14 && (Item_In_Bag[fromPos].GetVatPham_ID == 800000001 || Item_In_Bag[fromPos].GetVatPham_ID == 800000002) && HopThanhVatPham_Table.Count < 4)
					{
						if (!HopThanhVatPham_Table.ContainsKey(1))
						{
							Item_In_Bag[fromPos].Khoa_Chat = true;
							HopThanhVatPham_Table.Add(1, new HcItimesClass
							{
								Position = fromPos,
								VatPham = Item_In_Bag[fromPos].VatPham_byte
							});
							SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
						}
						else if (!HopThanhVatPham_Table.ContainsKey(2))
						{
							HcItimesClass value97 = new HcItimesClass();
							if (HopThanhVatPham_Table.TryGetValue(1, out value97))
							{
								if (Item_In_Bag[fromPos].GetVatPham_ID == BitConverter.ToInt32(value97.VatPham_id, 0))
								{
									Item_In_Bag[fromPos].Khoa_Chat = true;
									HopThanhVatPham_Table.Add(2, new HcItimesClass
									{
										Position = fromPos,
										VatPham = Item_In_Bag[fromPos].VatPham_byte
									});
									SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
								}
								else
								{
									SynthesisHint(num, 9, 0, Item_In_Bag[fromPos]);
								}
							}
						}
						else if (!HopThanhVatPham_Table.ContainsKey(3))
						{
							HcItimesClass value98 = new HcItimesClass();
							if (HopThanhVatPham_Table.TryGetValue(1, out value98))
							{
								if (Item_In_Bag[fromPos].GetVatPham_ID == BitConverter.ToInt32(value98.VatPham_id, 0))
								{
									Item_In_Bag[fromPos].Khoa_Chat = true;
									HopThanhVatPham_Table.Add(3, new HcItimesClass
									{
										Position = fromPos,
										VatPham = Item_In_Bag[fromPos].VatPham_byte
									});
									SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
								}
								else
								{
									SynthesisHint(num, 9, 0, Item_In_Bag[fromPos]);
								}
							}
						}
						else if (!HopThanhVatPham_Table.ContainsKey(4))
						{
							HcItimesClass value99 = new HcItimesClass();
							if (HopThanhVatPham_Table.TryGetValue(1, out value99))
							{
								if (Item_In_Bag[fromPos].GetVatPham_ID == BitConverter.ToInt32(value99.VatPham_id, 0))
								{
									Item_In_Bag[fromPos].Khoa_Chat = true;
									HopThanhVatPham_Table.Add(4, new HcItimesClass
									{
										Position = fromPos,
										VatPham = Item_In_Bag[fromPos].VatPham_byte
									});
									SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
								}
								else
								{
									SynthesisHint(num, 9, 0, Item_In_Bag[fromPos]);
								}
							}
						}
					}
				}
				catch (Exception ex108)
				{
					Form1.WriteLine(1, "合成系统 再造合成石-放合成石 error![" + base.Userid + "]-[" + base.UserName + "]" + ex108.Message);
				}
				goto default;
			case 89:
				try
				{
					if (HopThanhVatPham_Table.TryGetValue(1, out var value70))
					{
						Item_In_Bag[value70.Position].Khoa_Chat = false;
						HopThanhVatPham_Table.Clear();
						SynthesisSystemUnlocked();
						NguyenBao_HopThanh_MoRa = 0;
						SynthesisHint(num, 1, 0, Item_In_Bag[value70.Position]);
					}
				}
				catch (Exception ex74)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成4 error![" + base.Userid + "]-[" + base.UserName + "]" + ex74.Message);
				}
				goto default;
			case 79:
				try
				{
					if (HopThanhVatPham_Table.TryGetValue(1, out var value64))
					{
						Item_In_Bag[value64.Position].Khoa_Chat = false;
						SynthesisHint(num, 1, 0, Item_In_Bag[value64.Position]);
					}
					HopThanhVatPham_Table.Clear();
					SynthesisSystemUnlocked();
					NguyenBao_HopThanh_MoRa = 0;
					OpenWarehouse = false;
				}
				catch (Exception ex70)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 八卦回天符 合成4 error![" + base.Userid + "]-[" + base.UserName + "]" + ex70.Message);
				}
				goto default;
			case 81:
				try
				{
					if (!Item_In_Bag[fromPos].Khoa_Chat && !HopThanhVatPham_Table.ContainsKey(1))
					{
						HcItimesClass hcItimesClass150 = new HcItimesClass();
						hcItimesClass150.Position = fromPos;
						hcItimesClass150.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(1, hcItimesClass150);
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex86)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成1 error![" + base.Userid + "]-[" + base.UserName + "]" + ex86.Message);
				}
				goto default;
			case 82:
				try
				{
					if (HopThanhVatPham_Table.Count == 0)
					{
						SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
					}
					else if (!HopThanhVatPham_Table.ContainsKey(2) && !Item_In_Bag[fromPos].Khoa_Chat && !Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
					{
						HcItimesClass hcItimesClass155 = new HcItimesClass();
						hcItimesClass155.Position = fromPos;
						hcItimesClass155.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(2, hcItimesClass155);
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex89)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成1 error![" + base.Userid + "]-[" + base.UserName + "]" + ex89.Message);
				}
				goto default;
			case 83:
				try
				{
					if (HopThanhVatPham_Table.Count != 0 && !HopThanhVatPham_Table.ContainsKey(3))
					{
						HopThanhVatPham_Table.Add(3, new HcItimesClass
						{
							Position = fromPos,
							VatPham = Item_In_Bag[fromPos].VatPham_byte
						});
						Item_In_Bag[fromPos].Khoa_Chat = true;
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex62)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成3 error![" + base.Userid + "]-[" + base.UserName + "]" + ex62.Message);
				}
				goto default;
			case 119:
				try
				{
					if (HopThanhVatPham_Table.TryGetValue(2, out var value89))
					{
						Item_In_Bag[value89.Position].Khoa_Chat = false;
						HopThanhVatPham_Table.Clear();
						SynthesisSystemUnlocked();
						NguyenBao_HopThanh_MoRa = 0;
						SynthesisHint(num, 1, 0, Item_In_Bag[value89.Position]);
					}
				}
				catch (Exception ex101)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成4 error![" + base.Userid + "]-[" + base.UserName + "]" + ex101.Message);
				}
				goto default;
			case 100:
				try
				{
					HcItimesClass hcItimesClass21;
					if (base.CurrentOperationType == 26 && HopThanhVatPham_Table.Count > 0)
					{
						hcItimesClass21 = null;
						HcItimesClass hcItimesClass22 = null;
						HcItimesClass hcItimesClass23 = null;
						if (HopThanhVatPham_Table.ContainsKey(1))
						{
							hcItimesClass21 = HopThanhVatPham_Table[1];
						}
						if (HopThanhVatPham_Table.ContainsKey(2))
						{
							hcItimesClass22 = HopThanhVatPham_Table[2];
						}
						if (HopThanhVatPham_Table.ContainsKey(3))
						{
							hcItimesClass23 = HopThanhVatPham_Table[3];
						}
						if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass21.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass21.ItemGlobal_ID, 0) && BitConverter.ToInt64(Item_In_Bag[hcItimesClass22.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass22.ItemGlobal_ID, 0) && (hcItimesClass23 == null || BitConverter.ToInt64(Item_In_Bag[hcItimesClass23.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass23.ItemGlobal_ID, 0)))
						{
							hcItimesClass21.DatDuocThuocTinh();
							hcItimesClass21.CuongHoaThuocTinhGiaiDoan();
							hcItimesClass22.CuongHoaThuocTinhGiaiDoan();
							ItmeClass value94;
							if (Item_In_Bag[hcItimesClass21.Position].Vat_Pham_Khoa_Lai)
							{
								SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass21.Position]);
							}
							else if (World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass21.VatPham_id, 0), out value94))
							{
								if (value94.FLD_RESIDE2 != 23 && value94.FLD_RESIDE2 != 24 && value94.FLD_RESIDE2 != 25)
								{
									Form1.WriteLine(6, "合成系统 WGF BUG11[" + BitConverter.ToInt32(hcItimesClass21.VatPham_id, 0) + "]-[" + BitConverter.ToInt32(hcItimesClass22.VatPham_id, 0) + "]");
								}
								else
								{
									if (Phi_HopThanh > 0)
									{
										if (value94.FLD_NJ == 0)
										{
											if (base.Player_Money < Phi_HopThanh)
											{
												goto IL_b19d;
											}
											base.Player_Money -= Phi_HopThanh;
											UpdateMoneyAndWeight();
										}
										else
										{
											if (base.Player_VoHoang < Phi_HopThanh)
											{
												goto IL_b19d;
											}
											hcItimesClass21.FLD_FJ_NJ = 0;
											UpdateMoneyAndWeight();
											Init_Item_In_Bag();
											CalculateCharacterEquipmentData();
											UpdateMartialArtsAndStatus();
										}
									}
									if (hcItimesClass21.CuongHoaSoLuong > 0)
									{
										Form1.WriteLine(6, "合成系统 先强后合 BUG1[" + base.Userid + "]-[" + base.UserName + "]");
									}
									else
									{
										double num72 = RNG.Next(0, 110);
										int num74 = hcItimesClass22.GiaiDoanSoLuong;
										if (num74 > 10)
										{
											if (num74 > 10 && num74 <= 20)
											{
												num74 = 10;
											}
											else if (num74 > 20 && num74 <= 30)
											{
												num74 = 15;
											}
											else if (num74 > 30 && num74 <= 40)
											{
												num74 = 20;
											}
											else if (num74 > 40 && num74 <= 50)
											{
												num74 = 20;
											}
											else if (num74 > 50 && num74 <= 60)
											{
												num74 = 25;
											}
											else if (num74 > 60 && num74 <= 70)
											{
												num74 = 30;
											}
											else if (num74 > 70 && num74 <= 80)
											{
												num74 = 35;
											}
											if (num74 > 80 && num74 <= 100)
											{
												num74 = 50;
											}
										}
										double num75 = hcItimesClass21.ThuocTinh1.SoLuong * 20 + hcItimesClass21.ThuocTinh2.SoLuong * 20 + hcItimesClass21.ThuocTinh3.SoLuong * 20 + hcItimesClass21.ThuocTinh4.SoLuong * 20 + num74;
										if (hcItimesClass21.ThuocTinh1.ThuocTinhSoLuong == 0 || hcItimesClass21.ThuocTinh2.ThuocTinhSoLuong == 0 || hcItimesClass21.ThuocTinh3.ThuocTinhSoLuong == 0 || hcItimesClass21.ThuocTinh4.ThuocTinhSoLuong == 0)
										{
											if (hcItimesClass23 != null)
											{
												num72 += (double)LuckyCharmBonus(BitConverter.ToInt32(hcItimesClass23.VatPham_id, 0));
											}
											if (NguyenBao_HopThanh_MoRa == 1)
											{
												num72 += 5.0;
											}
											if (base.FLD_VIP == 1)
											{
											}
											if (World.TyLe_HopThanh != 0.0)
											{
												num72 += 100.0 * World.TyLe_HopThanh;
											}
											if (base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram > 0.0)
											{
												num72 += 100.0 * base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram / 2.0;
											}
											if (base.FLD_VIP == 8)
											{
											}
											if (num72 >= num75)
											{
												if (hcItimesClass21.ThuocTinh1.ThuocTinhLoaiHinh == 0)
												{
													if (hcItimesClass22.GiaiDoanLoaiHinh == 8)
													{
														hcItimesClass21.ThuocTinh1.KhiCongThuocTinhLoaiHinh = hcItimesClass22.KhiCongThuocTinhLoaiHinh;
													}
													hcItimesClass21.ThuocTinh1.ThuocTinhLoaiHinh = hcItimesClass22.GiaiDoanLoaiHinh;
													hcItimesClass21.ThuocTinh1.ThuocTinhSoLuong = hcItimesClass22.GiaiDoanSoLuong;
													hcItimesClass21.ThietLap_ThuocTinh();
												}
												else if (hcItimesClass21.ThuocTinh2.ThuocTinhLoaiHinh == 0)
												{
													if (hcItimesClass22.GiaiDoanLoaiHinh == 8)
													{
														hcItimesClass21.ThuocTinh2.KhiCongThuocTinhLoaiHinh = hcItimesClass22.KhiCongThuocTinhLoaiHinh;
													}
													hcItimesClass21.ThuocTinh2.ThuocTinhLoaiHinh = hcItimesClass22.GiaiDoanLoaiHinh;
													hcItimesClass21.ThuocTinh2.ThuocTinhSoLuong = hcItimesClass22.GiaiDoanSoLuong;
													hcItimesClass21.ThietLap_ThuocTinh();
												}
												else if (hcItimesClass21.ThuocTinh3.ThuocTinhLoaiHinh == 0)
												{
													if (hcItimesClass22.GiaiDoanLoaiHinh == 8)
													{
														hcItimesClass21.ThuocTinh3.KhiCongThuocTinhLoaiHinh = hcItimesClass22.KhiCongThuocTinhLoaiHinh;
													}
													hcItimesClass21.ThuocTinh3.ThuocTinhLoaiHinh = hcItimesClass22.GiaiDoanLoaiHinh;
													hcItimesClass21.ThuocTinh3.ThuocTinhSoLuong = hcItimesClass22.GiaiDoanSoLuong;
													hcItimesClass21.ThietLap_ThuocTinh();
												}
												else if (hcItimesClass21.ThuocTinh4.ThuocTinhLoaiHinh == 0)
												{
													if (hcItimesClass22.GiaiDoanLoaiHinh == 8)
													{
														hcItimesClass21.ThuocTinh4.KhiCongThuocTinhLoaiHinh = hcItimesClass22.KhiCongThuocTinhLoaiHinh;
													}
													hcItimesClass21.ThuocTinh4.ThuocTinhLoaiHinh = hcItimesClass22.GiaiDoanLoaiHinh;
													hcItimesClass21.ThuocTinh4.ThuocTinhSoLuong = hcItimesClass22.GiaiDoanSoLuong;
													hcItimesClass21.ThietLap_ThuocTinh();
												}
												SynthesisHint(num, 1, 0, Item_In_Bag[hcItimesClass21.Position]);
											}
											else
											{
												SynthesisHint(num, 0, 0, Item_In_Bag[hcItimesClass21.Position]);
											}
											SubtractItems(hcItimesClass22.Position, 1);
											if (hcItimesClass23 != null)
											{
												SubtractItems(hcItimesClass23.Position, 1);
											}
											if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass21.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass21.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass21.ItemGlobal_ID, 0))
											{
												SubtractItems(hcItimesClass21.Position, 1);
												AddItems(hcItimesClass21.ItemGlobal_ID, hcItimesClass21.VatPham_id, hcItimesClass21.Position, hcItimesClass21.VatPhamSoLuong, hcItimesClass21.VatPham_ThuocTinh);
											}
											Item_In_Bag[hcItimesClass21.Position].Khoa_Chat = false;
											HopThanhVatPham_Table.Clear();
											SynthesisSystemUnlocked();
											NguyenBao_HopThanh_MoRa = 0;
											Phi_HopThanh = 0;
										}
									}
								}
							}
						}
					}
					goto end_IL_ae05;
					IL_b19d:
					SynthesisHint(11, 4, 0, Item_In_Bag[hcItimesClass21.Position]);
					HopThanhVatPham_Table.Clear();
					end_IL_ae05:;
				}
				catch (Exception ex106)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成 error![" + base.Userid + "]-[" + base.UserName + "]" + ex106.Message);
				}
				goto default;
			case 101:
				try
				{
					if (!HopThanhVatPham_Table.ContainsKey(1) && World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0), out var value93) && (value93.FLD_RESIDE2 == 23 || value93.FLD_RESIDE2 == 24 || value93.FLD_RESIDE2 == 25) && (Item_In_Bag[fromPos].FLD_MAGIC1 == 0 || Item_In_Bag[fromPos].FLD_MAGIC2 == 0 || Item_In_Bag[fromPos].FLD_MAGIC3 == 0 || Item_In_Bag[fromPos].FLD_MAGIC4 == 0))
					{
						int SoTien5 = (Phi_HopThanh = CalculateSyntheticEnhancementCost(value93, fromPos, num));
						HcItimesClass hcItimesClass20 = new HcItimesClass();
						hcItimesClass20.Position = fromPos;
						hcItimesClass20.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(1, hcItimesClass20);
						SynthesisHint(num, 1, SoTien5, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex105)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成1 error![" + base.Userid + "]-[" + base.UserName + "]" + ex105.Message);
				}
				goto default;
			case 102:
				try
				{
					if (HopThanhVatPham_Table.ContainsKey(1))
					{
						HcItimesClass hcItimesClass19 = HopThanhVatPham_Table[1];
						if (!Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai && World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0), out var value90) && (value90.FLD_PID == 800000032 || value90.FLD_PID == 800000033) && World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass19.VatPham_id, 0), out var value92) && (value90.FLD_PID != 800000032 || value92.FLD_JOB_LEVEL < 3) && (value90.FLD_PID != 800000033 || value92.FLD_JOB_LEVEL >= 3) && HopThanhVatPham_Table.Count != 0 && !HopThanhVatPham_Table.ContainsKey(2))
						{
							HopThanhVatPham_Table.Add(2, new HcItimesClass
							{
								Position = fromPos,
								VatPham = Item_In_Bag[fromPos].VatPham_byte
							});
							Item_In_Bag[fromPos].Khoa_Chat = true;
							SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
						}
					}
				}
				catch (Exception ex103)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成2 error![" + base.Userid + "]-[" + base.UserName + "]" + ex103.Message);
				}
				goto default;
			case 103:
				try
				{
					if (HopThanhVatPham_Table.Count != 0 && !HopThanhVatPham_Table.ContainsKey(3))
					{
						HopThanhVatPham_Table.Add(3, new HcItimesClass
						{
							Position = fromPos,
							VatPham = Item_In_Bag[fromPos].VatPham_byte
						});
						Item_In_Bag[fromPos].Khoa_Chat = true;
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex78)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成3 error![" + base.Userid + "]-[" + base.UserName + "]" + ex78.Message);
				}
				goto default;
			case 109:
				try
				{
					if (HopThanhVatPham_Table.TryGetValue(1, out var value86))
					{
						Item_In_Bag[value86.Position].Khoa_Chat = false;
						SynthesisHint(num, 1, 0, Item_In_Bag[value86.Position]);
						HopThanhVatPham_Table.Clear();
						SynthesisSystemUnlocked();
						NguyenBao_HopThanh_MoRa = 0;
					}
				}
				catch (Exception ex98)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成4 error![" + base.Userid + "]-[" + base.UserName + "]" + ex98.Message);
				}
				goto default;
			case 110:
				if (base.CurrentOperationType == 27 && CharacterBeast != null && HopThanhVatPham_Table.Count > 0)
				{
					HcItimesClass hcItimesClass24 = null;
					HcItimesClass hcItimesClass25 = null;
					if (HopThanhVatPham_Table.ContainsKey(2))
					{
						hcItimesClass24 = HopThanhVatPham_Table[2];
					}
					if (HopThanhVatPham_Table.ContainsKey(3))
					{
						hcItimesClass25 = HopThanhVatPham_Table[3];
					}
					if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass24.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass24.ItemGlobal_ID, 0) && (hcItimesClass25 == null || BitConverter.ToInt64(Item_In_Bag[hcItimesClass25.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass25.ItemGlobal_ID, 0)) && World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass24.VatPham_id, 0), out var value95) && (value95.FLD_PID == ********* || value95.FLD_PID == 800000031))
					{
						hcItimesClass24.CuongHoaThuocTinhGiaiDoan();
						if (CharacterBeast.FLD_MAGIC1 == 0 || CharacterBeast.FLD_MAGIC2 == 0 || CharacterBeast.FLD_MAGIC3 == 0 || CharacterBeast.FLD_MAGIC4 == 0 || CharacterBeast.FLD_MAGIC5 == 0)
						{
							int num76 = ((value95.FLD_PID != *********) ? 100000 : 50000);
							if (base.Player_Money < num76)
							{
								try
								{
									if (HopThanhVatPham_Table.TryGetValue(1, out var value96))
									{
										Item_In_Bag[value96.Position].Khoa_Chat = false;
										HopThanhVatPham_Table.Clear();
										SynthesisSystemUnlocked();
										SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
									}
									NguyenBao_HopThanh_MoRa = 0;
									SynthesisHint(29, 0, 0, Item_In_Bag[fromPos]);
									HeThongNhacNho("Ngân lượng không đủ, câ\u0300n thanh toán" + num76 + " Lượng.");
								}
								catch (Exception ex107)
								{
									NguyenBao_HopThanh_MoRa = 0;
									Form1.WriteLine(1, "合成系统 先强后合 合成4 error![" + base.Userid + "]-[" + base.UserName + "]" + ex107.Message);
								}
							}
							else
							{
								base.Player_Money -= num76;
								UpdateMoneyAndWeight();
								double num77 = RNG.Next(0, 110);
								double num78 = hcItimesClass24.GiaiDoanSoLuong;
								if (CharacterBeast.FLD_MAGIC1 != 0)
								{
									num78 += 20.0;
								}
								if (CharacterBeast.FLD_MAGIC2 != 0)
								{
									num78 += 20.0;
								}
								if (CharacterBeast.FLD_MAGIC3 != 0)
								{
									num78 += 20.0;
								}
								if (CharacterBeast.FLD_MAGIC4 != 0)
								{
									num78 += 20.0;
								}
								if (hcItimesClass25 != null)
								{
									num77 += (double)LuckyCharmBonus(BitConverter.ToInt32(hcItimesClass25.VatPham_id, 0));
								}
								if (NguyenBao_HopThanh_MoRa == 1)
								{
									num77 += 5.0;
								}
								if (base.FLD_VIP == 1)
								{
								}
								if (World.TyLe_HopThanh != 0.0)
								{
									num77 += 100.0 * World.TyLe_HopThanh;
								}
								if (base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram > 0.0)
								{
									num77 += 100.0 * base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram / 2.0;
								}
								if (base.FLD_VIP == 8)
								{
								}
								if (num77 >= num78)
								{
									if (CharacterBeast.FLD_MAGIC1 == 0)
									{
										if (hcItimesClass24.GiaiDoanSoLuong.ToString().Length == 1)
										{
											CharacterBeast.FLD_MAGIC1 = int.Parse(hcItimesClass24.GiaiDoanLoaiHinh + "000000" + hcItimesClass24.GiaiDoanSoLuong);
										}
										else if (hcItimesClass24.GiaiDoanSoLuong.ToString().Length == 2)
										{
											CharacterBeast.FLD_MAGIC1 = int.Parse(hcItimesClass24.GiaiDoanLoaiHinh + "00000" + hcItimesClass24.GiaiDoanSoLuong);
										}
									}
									else if (CharacterBeast.FLD_MAGIC2 == 0)
									{
										if (hcItimesClass24.GiaiDoanSoLuong.ToString().Length == 1)
										{
											CharacterBeast.FLD_MAGIC2 = int.Parse(hcItimesClass24.GiaiDoanLoaiHinh + "000000" + hcItimesClass24.GiaiDoanSoLuong);
										}
										else if (hcItimesClass24.GiaiDoanSoLuong.ToString().Length == 2)
										{
											CharacterBeast.FLD_MAGIC2 = int.Parse(hcItimesClass24.GiaiDoanLoaiHinh + "00000" + hcItimesClass24.GiaiDoanSoLuong);
										}
									}
									else if (CharacterBeast.FLD_MAGIC3 == 0)
									{
										if (hcItimesClass24.GiaiDoanSoLuong.ToString().Length == 1)
										{
											CharacterBeast.FLD_MAGIC3 = int.Parse(hcItimesClass24.GiaiDoanLoaiHinh + "000000" + hcItimesClass24.GiaiDoanSoLuong);
										}
										else if (hcItimesClass24.GiaiDoanSoLuong.ToString().Length == 2)
										{
											CharacterBeast.FLD_MAGIC3 = int.Parse(hcItimesClass24.GiaiDoanLoaiHinh + "00000" + hcItimesClass24.GiaiDoanSoLuong);
										}
									}
									else if (CharacterBeast.FLD_MAGIC4 == 0)
									{
										if (hcItimesClass24.GiaiDoanSoLuong.ToString().Length == 1)
										{
											CharacterBeast.FLD_MAGIC4 = int.Parse(hcItimesClass24.GiaiDoanLoaiHinh + "000000" + hcItimesClass24.GiaiDoanSoLuong);
										}
										else if (hcItimesClass24.GiaiDoanSoLuong.ToString().Length == 2)
										{
											CharacterBeast.FLD_MAGIC4 = int.Parse(hcItimesClass24.GiaiDoanLoaiHinh + "00000" + hcItimesClass24.GiaiDoanSoLuong);
										}
									}
									else if (CharacterBeast.FLD_MAGIC5 == 0)
									{
										if (hcItimesClass24.GiaiDoanSoLuong.ToString().Length == 1)
										{
											CharacterBeast.FLD_MAGIC5 = int.Parse(hcItimesClass24.GiaiDoanLoaiHinh + "000000" + hcItimesClass24.GiaiDoanSoLuong);
										}
										else if (hcItimesClass24.GiaiDoanSoLuong.ToString().Length == 2)
										{
											CharacterBeast.FLD_MAGIC5 = int.Parse(hcItimesClass24.GiaiDoanLoaiHinh + "00000" + hcItimesClass24.GiaiDoanSoLuong);
										}
									}
									UpdateSpiritBeastMartialArtsAndStatus();
									SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
								}
								else
								{
									SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
								}
								SubtractItems(hcItimesClass24.Position, 1);
								if (hcItimesClass25 != null)
								{
									SubtractItems(hcItimesClass25.Position, 1);
								}
								HopThanhVatPham_Table.Clear();
								SynthesisSystemUnlocked();
								NguyenBao_HopThanh_MoRa = 0;
							}
						}
					}
				}
				goto default;
			case 112:
				try
				{
					if (!HopThanhVatPham_Table.ContainsKey(2) && CharacterBeast != null && (CharacterBeast.FLD_MAGIC1 == 0 || CharacterBeast.FLD_MAGIC2 == 0 || CharacterBeast.FLD_MAGIC3 == 0 || CharacterBeast.FLD_MAGIC4 == 0 || CharacterBeast.FLD_MAGIC5 == 0))
					{
						int num69 = BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0);
						if (num69 == 0 || (uint)(num69 - *********) > 1u)
						{
							break;
						}
						switch (CharacterBeast.FLD_JOB_LEVEL)
						{
						case 3:
							if (num69 != 800000031)
							{
								return;
							}
							break;
						case 0:
						case 1:
						case 2:
							if (num69 != *********)
							{
								return;
							}
							break;
						}
						HopThanhVatPham_Table.Add(2, new HcItimesClass
						{
							Position = fromPos,
							VatPham = Item_In_Bag[fromPos].VatPham_byte
						});
						Item_In_Bag[fromPos].Khoa_Chat = true;
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
						break;
					}
				}
				catch (Exception ex94)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成2 error![" + base.Userid + "]-[" + base.UserName + "]" + ex94.Message);
				}
				goto default;
			case 113:
				try
				{
					if (HopThanhVatPham_Table.Count != 0 && !HopThanhVatPham_Table.ContainsKey(3))
					{
						HopThanhVatPham_Table.Add(3, new HcItimesClass
						{
							Position = fromPos,
							VatPham = Item_In_Bag[fromPos].VatPham_byte
						});
						Item_In_Bag[fromPos].Khoa_Chat = true;
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex16)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成3 error![" + base.Userid + "]-[" + base.UserName + "]" + ex16.Message);
				}
				goto default;
			case 260:
				try
				{
					HopThanhVatPham_Table.Clear();
					SynthesisSystemUnlocked();
					NguyenBao_HopThanh_MoRa = 0;
					OpenWarehouse = false;
				}
				catch (Exception ex90)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "ChuyenCuongHoa260 error![" + base.Userid + "]-[" + base.UserName + "]" + ex90.Message);
				}
				goto default;
			case 261:
				try
				{
					int num68 = BitConverter.ToUInt16(PacketData, 14);
					if (Item_In_Bag[num68].GetVatPham_ID == 1008001061 || Item_In_Bag[num68].GetVatPham_ID == 1008001062)
					{
						int num88 = BitConverter.ToInt32(PacketData, 18);
						if ((uint)(num88 - 1008001061) > 1u || Item_In_Bag[fromPos].Khoa_Chat || Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai || Item_In_Bag[fromPos].FLD_CuongHoaSoLuong < 11)
						{
							break;
						}
						if (World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0), out var value83))
						{
							if (value83.FLD_LEVEL < 10)
							{
								break;
							}
							if (value83.FLD_RESIDE2 == 4)
							{
								if (Item_In_Bag[num68].GetVatPham_ID != 1008001061)
								{
									break;
								}
							}
							else if (value83.FLD_RESIDE2 != 1 || Item_In_Bag[num68].GetVatPham_ID != 1008001062)
							{
								break;
							}
							if (!HopThanhVatPham_Table.ContainsKey(1))
							{
								HcItimesClass hcItimesClass5 = new HcItimesClass();
								hcItimesClass5.Position = fromPos;
								hcItimesClass5.VatPham = Item_In_Bag[fromPos].VatPham_byte;
								Item_In_Bag[fromPos].Khoa_Chat = true;
								HopThanhVatPham_Table.Add(1, hcItimesClass5);
								SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
							}
						}
						else
						{
							SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
						}
						break;
					}
				}
				catch (Exception ex93)
				{
					Form1.WriteLine(1, "ChuyenCuongHoa1 error![" + base.Userid + "]-[" + base.UserName + "]" + ex93.Message);
				}
				goto default;
			case 262:
				try
				{
					int num67 = BitConverter.ToUInt16(PacketData, 14);
					if (Item_In_Bag[num67].GetVatPham_ID == 1008001061 || Item_In_Bag[num67].GetVatPham_ID == 1008001062)
					{
						int num89 = BitConverter.ToInt32(PacketData, 18);
						if ((uint)(num89 - 1008001061) > 1u || Item_In_Bag[fromPos].Khoa_Chat || Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai || Item_In_Bag[fromPos].FLD_CuongHoaSoLuong > 10)
						{
							break;
						}
						if (World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0), out var value81))
						{
							if (value81.FLD_LEVEL < 10)
							{
								break;
							}
							if (value81.FLD_RESIDE2 == 4)
							{
								if (Item_In_Bag[num67].GetVatPham_ID != 1008001061)
								{
									break;
								}
							}
							else if (value81.FLD_RESIDE2 != 1 || Item_In_Bag[num67].GetVatPham_ID != 1008001062)
							{
								break;
							}
							if (!HopThanhVatPham_Table.ContainsKey(2))
							{
								HcItimesClass hcItimesClass154 = new HcItimesClass();
								hcItimesClass154.Position = fromPos;
								hcItimesClass154.VatPham = Item_In_Bag[fromPos].VatPham_byte;
								Item_In_Bag[fromPos].Khoa_Chat = true;
								HopThanhVatPham_Table.Add(2, hcItimesClass154);
								SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
							}
						}
						else
						{
							SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
						}
						break;
					}
				}
				catch (Exception ex88)
				{
					Form1.WriteLine(1, "ChuyenCuongHoa2 error![" + base.Userid + "]-[" + base.UserName + "]" + ex88.Message);
				}
				goto default;
			case 263:
				if (HopThanhVatPham_Table.Count > 1)
				{
					int num37 = BitConverter.ToUInt16(PacketData, 14);
					if (Item_In_Bag[num37].GetVatPham_ID == 1008001061 || Item_In_Bag[num37].GetVatPham_ID == 1008001062)
					{
						int num90 = BitConverter.ToInt32(PacketData, 18);
						if ((uint)(num90 - 1008001061) <= 1u)
						{
							HcItimesClass hcItimesClass105 = null;
							HcItimesClass hcItimesClass106 = null;
							if (HopThanhVatPham_Table.ContainsKey(1))
							{
								hcItimesClass105 = HopThanhVatPham_Table[1];
							}
							if (HopThanhVatPham_Table.ContainsKey(2))
							{
								hcItimesClass106 = HopThanhVatPham_Table[2];
							}
							hcItimesClass105.DatDuocThuocTinh();
							hcItimesClass105.CuongHoaThuocTinhGiaiDoan();
							hcItimesClass106.DatDuocThuocTinh();
							hcItimesClass106.CuongHoaThuocTinhGiaiDoan();
							double num38 = RNG.Next(20, 110);
							double num39 = hcItimesClass105.CuongHoaSoLuong * 10;
							if (NguyenBao_HopThanh_MoRa == 1)
							{
								num38 += 5.0;
							}
							if (base.FLD_VIP == 1)
							{
							}
							if (World.TyLe_CuongHoa != 0.0)
							{
								num38 += 100.0 * World.TyLe_CuongHoa;
							}
							if (base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
							{
								num38 += 100.0 * base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
							}
							if (base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
							{
								num38 += 100.0 * base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
							}
							if (GMMode == 5)
							{
								num39 = 0.0;
							}
							if (num38 >= num39)
							{
								hcItimesClass106.CuongHoaLoaiHinh = 1;
								hcItimesClass106.CuongHoaSoLuong = hcItimesClass105.CuongHoaSoLuong;
								hcItimesClass106.ThietLap_GiaiDoanThuocTinh();
								SubtractItems(hcItimesClass105.Position, 1);
								SubtractItems(hcItimesClass106.Position, 1);
								AddItems(hcItimesClass106.ItemGlobal_ID, hcItimesClass106.VatPham_id, hcItimesClass106.Position, hcItimesClass106.VatPhamSoLuong, hcItimesClass106.VatPham_ThuocTinh);
								SynthesisHint(num, 1, 0, Item_In_Bag[hcItimesClass106.Position]);
								Item_In_Bag[hcItimesClass106.Position].Khoa_Chat = false;
								RxjhClass.SyntheticRecord(base.Userid, base.UserName, Item_In_Bag[hcItimesClass106.Position].DatDuocVatPhamTen_XungHao(), num, "ChuyenCuongHoa", "Thanh Cong", Item_In_Bag[hcItimesClass106.Position]);
								HeThongNhacNho("Chuyển hóa thành công", 10, "Thanh Cong");
							}
							else
							{
								SynthesisHint(num, 1, 0, Item_In_Bag[hcItimesClass106.Position]);
								RxjhClass.SyntheticRecord(base.Userid, base.UserName, Item_In_Bag[hcItimesClass106.Position].DatDuocVatPhamTen_XungHao(), num, "ChuyenCuongHoa", "That Bai", Item_In_Bag[hcItimesClass106.Position]);
								HeThongNhacNho("Chuyển hóa thất bại", 10, "That Bai");
							}
							SubtractItems(num37, 1);
							HopThanhVatPham_Table.Clear();
							SynthesisSystemUnlocked();
							NguyenBao_HopThanh_MoRa = 0;
							Phi_HopThanh = 0;
							OpenWarehouse = false;
						}
						break;
					}
				}
				goto default;
			case 270:
				try
				{
					if (HopThanhVatPham_Table.TryGetValue(1, out var value53))
					{
						Item_In_Bag[value53.Position].Khoa_Chat = false;
						HopThanhVatPham_Table.Clear();
						System.Buffer.BlockCopy(array24, 0, array, 10, 2);
						SynthesisSystemUnlocked();
					}
					NguyenBao_HopThanh_MoRa = 0;
					SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
					break;
				}
				catch (Exception ex59)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成4 error![" + base.Userid + "]-[" + base.UserName + "]" + ex59.Message);
					break;
				}
			case 271:
				try
				{
					if (!HopThanhVatPham_Table.ContainsKey(1))
					{
						if (Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
							break;
						}
						HcItimesClass hcItimesClass103 = new HcItimesClass();
						hcItimesClass103.Position = fromPos;
						hcItimesClass103.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(1, hcItimesClass103);
						SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
					}
					break;
				}
				catch (Exception ex57)
				{
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成1 error![" + base.Userid + "]-[" + base.UserName + "]" + ex57.Message);
					break;
				}
			case 272:
				try
				{
					if (!HopThanhVatPham_Table.ContainsKey(1))
					{
						break;
					}
					if (HopThanhVatPham_Table.ContainsKey(1))
					{
						HcItimesClass hcItimesClass127 = HopThanhVatPham_Table[1];
						if (HopThanhVatPham_Table.Count == 0)
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
						}
						else
						{
							if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) < 1 || (World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass127.VatPham_id, 0), out var value69) && value69.FLD_PID != 1000001170 && value69.FLD_PID != 1000001171 && value69.FLD_PID != 1000001172 && value69.FLD_PID != 1000001173 && value69.FLD_PID != 1000001174 && value69.FLD_PID != 1000001175))
							{
								break;
							}
							if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 800000060 && BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 1008001988)
							{
								SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
								break;
							}
							HcItimesClass hcItimesClass129 = new HcItimesClass();
							hcItimesClass129.Position = fromPos;
							hcItimesClass129.VatPham = Item_In_Bag[fromPos].VatPham_byte;
							Item_In_Bag[fromPos].Khoa_Chat = true;
							if (!HopThanhVatPham_Table.ContainsKey(2))
							{
								HopThanhVatPham_Table.Add(2, hcItimesClass129);
								SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
							}
						}
					}
					else
					{
						SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
					}
					break;
				}
				catch (Exception ex72)
				{
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成2 error![" + base.Userid + "]-[" + base.UserName + "]" + ex72.Message);
					break;
				}
			case 273:
				try
				{
					if (HopThanhVatPham_Table.Count == 0 || HopThanhVatPham_Table.ContainsKey(5))
					{
						break;
					}
					HcItimesClass hcItimesClass107 = new HcItimesClass();
					hcItimesClass107.Position = fromPos;
					hcItimesClass107.VatPham = Item_In_Bag[fromPos].VatPham_byte;
					Item_In_Bag[fromPos].Khoa_Chat = true;
					HopThanhVatPham_Table.Add(5, hcItimesClass107);
					SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
				}
				catch (Exception ex60)
				{
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成3 error![" + base.Userid + "]-[" + base.UserName + "]" + ex60.Message);
					break;
				}
				goto default;
			case 274:
				RemindersForStrengtheningSpiritPets(0, num34);
				break;
			case 275:
				try
				{
					if (HopThanhVatPham_Table.Count <= 0)
					{
						break;
					}
					HcItimesClass hcItimesClass99 = null;
					HcItimesClass hcItimesClass101 = null;
					HcItimesClass hcItimesClass102 = null;
					if (HopThanhVatPham_Table.ContainsKey(1))
					{
						hcItimesClass99 = HopThanhVatPham_Table[1];
					}
					if (HopThanhVatPham_Table.ContainsKey(2))
					{
						hcItimesClass101 = HopThanhVatPham_Table[2];
					}
					if (HopThanhVatPham_Table.ContainsKey(5))
					{
						hcItimesClass102 = HopThanhVatPham_Table[5];
					}
					if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass99.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass99.ItemGlobal_ID, 0) || hcItimesClass101 == null || (hcItimesClass101 != null && (BitConverter.ToInt64(Item_In_Bag[hcItimesClass101.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass101.ItemGlobal_ID, 0) || (BitConverter.ToInt32(Item_In_Bag[hcItimesClass101.Position].VatPham_ID, 0) != 1008001988 && BitConverter.ToInt32(Item_In_Bag[hcItimesClass101.Position].VatPham_ID, 0) != 800000060))))
					{
						break;
					}
					hcItimesClass99.DatDuocThuocTinh();
					hcItimesClass99.CuongHoaThuocTinhGiaiDoan();
					if (!World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass99.VatPham_id, 0), out var value51))
					{
						break;
					}
					if (value51.FLD_RESIDE2 != 16)
					{
						Form1.WriteLine(6, "合成系统 CuongHoa BUG1[" + BitConverter.ToInt32(hcItimesClass99.VatPham_id, 0) + "]-[" + BitConverter.ToInt32(hcItimesClass101.VatPham_id, 0) + "]");
						break;
					}
					if (Item_In_Bag[hcItimesClass99.Position].Vat_Pham_Khoa_Lai)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass99.Position]);
						break;
					}
					Random random7 = new Random();
					string[] array8 = World.TongXacSuat_CuongHoaThanThu.Split(';');
					double num35 = random7.Next(int.Parse(array8[0]), int.Parse(array8[1]));
					int CuongHoaSoLuong = hcItimesClass99.CuongHoaSoLuong;
					double num36 = 10.0;
					if (CuongHoaSoLuong < 10)
					{
						num36 = World.CuongHoaThanThu_1GianDoan_XacSuat;
					}
					else if (CuongHoaSoLuong < 20)
					{
						num36 = World.CuongHoaThanThu_2GianDoan_XacSuat;
					}
					else if (CuongHoaSoLuong < 30)
					{
						num36 = World.CuongHoaThanThu_3GianDoan_XacSuat;
					}
					else if (CuongHoaSoLuong < 40)
					{
						num36 = World.CuongHoaThanThu_4GianDoan_XacSuat;
					}
					else if (CuongHoaSoLuong < 50)
					{
						num36 = World.CuongHoaThanThu_5GianDoan_XacSuat;
					}
					else if (CuongHoaSoLuong < 60)
					{
						num36 = World.CuongHoaThanThu_6GianDoan_XacSuat;
					}
					else if (CuongHoaSoLuong < 70)
					{
						num36 = World.CuongHoaThanThu_7GianDoan_XacSuat;
					}
					else if (CuongHoaSoLuong < 80)
					{
						num36 = World.CuongHoaThanThu_8GianDoan_XacSuat;
					}
					else if (CuongHoaSoLuong < 90)
					{
						num36 = World.CuongHoaThanThu_9GianDoan_XacSuat;
					}
					else if (CuongHoaSoLuong < 100)
					{
						num36 = World.CuongHoaThanThu_10GianDoan_XacSuat;
					}
					if (hcItimesClass99 != null)
					{
						num35 += (double)LuckyCharmBonus(BitConverter.ToInt32(hcItimesClass99.VatPham_id, 0));
					}
					if (NguyenBao_HopThanh_MoRa == 1)
					{
						num35 += 5.0;
					}
					if (base.FLD_VIP == 1)
					{
					}
					if (base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
					{
						num35 += 100.0 * base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
					}
					if (base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
					{
						num35 += 100.0 * base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
					}
					if (hcItimesClass99.CuongHoaSoLuong >= 99)
					{
						break;
					}
					if (num35 > num36)
					{
						if (value51.FLD_RESIDE2 == 16)
						{
							hcItimesClass99.CuongHoaLoaiHinh = 19;
						}
						HcItimesClass hcItimesClass156 = hcItimesClass99;
						int giaiDoanSoLuong = hcItimesClass156.CuongHoaSoLuong + 1;
						hcItimesClass156.CuongHoaSoLuong = giaiDoanSoLuong;
						if (hcItimesClass99.CuongHoaSoLuong > 99)
						{
							hcItimesClass99.CuongHoaSoLuong = 99;
						}
						hcItimesClass99.ThietLap_GiaiDoanThuocTinh();
						if (hcItimesClass99.CuongHoaSoLuong >= 8)
						{
							SendNewsletter(BitConverter.ToInt32(hcItimesClass99.VatPham_id, 0), base.UserName, hcItimesClass99.CuongHoaSoLuong, base.Player_Zx);
						}
						if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass99.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass99.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass99.ItemGlobal_ID, 0))
						{
							SubtractItems(hcItimesClass99.Position, 1);
							AddItems(hcItimesClass99.ItemGlobal_ID, hcItimesClass99.VatPham_id, hcItimesClass99.Position, hcItimesClass99.VatPhamSoLuong, hcItimesClass99.VatPham_ThuocTinh);
						}
						Item_In_Bag[hcItimesClass99.Position].Khoa_Chat = false;
						if (base.FLD_VIP == 1)
						{
						}
						SpiritPetSynthesisHint(num, 0, 0, Item_In_Bag[fromBag], num34);
						RxjhClass.SyntheticRecord(base.Userid, base.UserName, value51.ItmeNAME, num, "TangCuongPet", "Thanh Cong", Item_In_Bag[hcItimesClass99.Position]);
					}
					else
					{
						if (hcItimesClass99.CuongHoaSoLuong >= 3)
						{
							if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass101.Position].VatPham_ID, 0) == 1008001988)
							{
								HcItimesClass hcItimesClass156 = hcItimesClass99;
								int giaiDoanSoLuong = hcItimesClass156.CuongHoaSoLuong - 1;
								hcItimesClass156.CuongHoaSoLuong = giaiDoanSoLuong;
							}
							else
							{
								hcItimesClass99.CuongHoaSoLuong -= 3;
							}
						}
						hcItimesClass99.ThietLap_GiaiDoanThuocTinh();
						if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass99.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass99.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass99.ItemGlobal_ID, 0))
						{
							SubtractItems(hcItimesClass99.Position, 1);
							AddItems(hcItimesClass99.ItemGlobal_ID, hcItimesClass99.VatPham_id, hcItimesClass99.Position, hcItimesClass99.VatPhamSoLuong, hcItimesClass99.VatPham_ThuocTinh);
						}
						SpiritPetSynthesisHint(num, -1, 0, Item_In_Bag[fromBag], num34);
						RxjhClass.SyntheticRecord(base.Userid, base.UserName, value51.ItmeNAME, num, "TangCuongPet", "That Bai", Item_In_Bag[hcItimesClass99.Position]);
					}
					SubtractItems(hcItimesClass101.Position, 1);
					if (hcItimesClass102 != null)
					{
						SubtractItems(hcItimesClass102.Position, 1);
					}
					HopThanhVatPham_Table.Clear();
					SynthesisSystemUnlocked();
					NguyenBao_HopThanh_MoRa = 0;
					break;
				}
				catch (Exception ex55)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成 error![" + base.Userid + "]-[" + base.UserName + "]" + ex55.Message);
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成 error![" + base.Userid + "]-[" + base.UserName + "]" + ex55.StackTrace);
					break;
				}
			case 280:
				try
				{
					if (HopThanhVatPham_Table.TryGetValue(1, out var value52))
					{
						Item_In_Bag[value52.Position].Khoa_Chat = false;
						HopThanhVatPham_Table.Clear();
						System.Buffer.BlockCopy(array24, 0, array, 11, 2);
						SynthesisSystemUnlocked();
					}
					NguyenBao_HopThanh_MoRa = 0;
					SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
					break;
				}
				catch (Exception ex56)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成4 error![" + base.Userid + "]-[" + base.UserName + "]" + ex56.Message);
					break;
				}
			case 281:
				try
				{
					if (!HopThanhVatPham_Table.ContainsKey(1))
					{
						if (Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
							break;
						}
						HcItimesClass hcItimesClass104 = new HcItimesClass();
						hcItimesClass104.Position = fromPos;
						hcItimesClass104.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(1, hcItimesClass104);
						SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
					}
					break;
				}
				catch (Exception ex58)
				{
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成1 error![" + base.Userid + "]-[" + base.UserName + "]" + ex58.Message);
					break;
				}
			case 282:
				try
				{
					if (HopThanhVatPham_Table.Count == 0)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
						break;
					}
					HcItimesClass hcItimesClass97 = null;
					if (HopThanhVatPham_Table.ContainsKey(1))
					{
						hcItimesClass97 = HopThanhVatPham_Table[1];
					}
					hcItimesClass97.DatDuocThuocTinh();
					hcItimesClass97.CuongHoaThuocTinhGiaiDoan();
					if (!HopThanhVatPham_Table.ContainsKey(2) && Item_In_Bag[fromPos].ThuocTinh1.ThuocTinhSoLuong == hcItimesClass97.ThuocTinh1.ThuocTinhSoLuong)
					{
						if (Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
							break;
						}
						HcItimesClass hcItimesClass98 = new HcItimesClass();
						hcItimesClass98.Position = fromPos;
						hcItimesClass98.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(2, hcItimesClass98);
						SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
					}
					break;
				}
				catch (Exception ex53)
				{
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成1 error![" + base.Userid + "]-[" + base.UserName + "]" + ex53.Message);
					break;
				}
			case 283:
				try
				{
					if (HopThanhVatPham_Table.Count >= 2 && !HopThanhVatPham_Table.ContainsKey(3))
					{
						if (Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
						}
						else if (Item_In_Bag[fromPos].GetVatPham_ID == **********)
						{
							HcItimesClass hcItimesClass95 = new HcItimesClass();
							hcItimesClass95.Position = fromPos;
							hcItimesClass95.VatPham = Item_In_Bag[fromPos].VatPham_byte;
							Item_In_Bag[fromPos].Khoa_Chat = true;
							HopThanhVatPham_Table.Add(3, hcItimesClass95);
							SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
						}
					}
					break;
				}
				catch (Exception ex51)
				{
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成1 error![" + base.Userid + "]-[" + base.UserName + "]" + ex51.Message);
					break;
				}
			case 284:
				try
				{
					if (HopThanhVatPham_Table.Count >= 2 && !HopThanhVatPham_Table.ContainsKey(4))
					{
						if (Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
						}
						else if (Item_In_Bag[fromPos].GetVatPham_ID == 1008001942)
						{
							HcItimesClass hcItimesClass131 = new HcItimesClass();
							hcItimesClass131.Position = fromPos;
							hcItimesClass131.VatPham = Item_In_Bag[fromPos].VatPham_byte;
							Item_In_Bag[fromPos].Khoa_Chat = true;
							HopThanhVatPham_Table.Add(4, hcItimesClass131);
							SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
						}
					}
					break;
				}
				catch (Exception ex73)
				{
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成1 error![" + base.Userid + "]-[" + base.UserName + "]" + ex73.Message);
					break;
				}
			case 285:
				try
				{
					if (HopThanhVatPham_Table.Count <= 0)
					{
						break;
					}
					HcItimesClass hcItimesClass65 = null;
					HcItimesClass hcItimesClass66 = null;
					HcItimesClass hcItimesClass67 = null;
					HcItimesClass hcItimesClass68 = null;
					if (HopThanhVatPham_Table.ContainsKey(1))
					{
						hcItimesClass65 = HopThanhVatPham_Table[1];
					}
					if (HopThanhVatPham_Table.ContainsKey(2))
					{
						hcItimesClass66 = HopThanhVatPham_Table[2];
					}
					if (HopThanhVatPham_Table.ContainsKey(3))
					{
						hcItimesClass67 = HopThanhVatPham_Table[3];
					}
					if (HopThanhVatPham_Table.ContainsKey(4))
					{
						hcItimesClass68 = HopThanhVatPham_Table[4];
					}
					if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass65.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass65.ItemGlobal_ID, 0) || BitConverter.ToInt64(Item_In_Bag[hcItimesClass66.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass66.ItemGlobal_ID, 0) || (hcItimesClass67 != null && BitConverter.ToInt64(Item_In_Bag[hcItimesClass67.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass67.ItemGlobal_ID, 0)) || (hcItimesClass68 != null && BitConverter.ToInt64(Item_In_Bag[hcItimesClass68.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass68.ItemGlobal_ID, 0)))
					{
						break;
					}
					hcItimesClass65.DatDuocThuocTinh();
					hcItimesClass65.CuongHoaThuocTinhGiaiDoan();
					hcItimesClass66.DatDuocThuocTinh();
					hcItimesClass66.CuongHoaThuocTinhGiaiDoan();
					if (Item_In_Bag[hcItimesClass65.Position].Vat_Pham_Khoa_Lai)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass65.Position]);
					}
					else
					{
						if (!World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass65.VatPham_id, 0), out var value32))
						{
							break;
						}
						if (value32.FLD_RESIDE2 != 16)
						{
							Form1.WriteLine(6, "合成系统 WGF BUG11[" + BitConverter.ToInt32(hcItimesClass65.VatPham_id, 0) + "]-[" + BitConverter.ToInt32(hcItimesClass66.VatPham_id, 0) + "]");
							break;
						}
						Random random8 = new Random();
						string[] array9 = World.TongXacSuat_TienHoaThanThu.Split(';');
						double num24 = random8.Next(int.Parse(array9[0]), int.Parse(array9[1]));
						int num25 = 0;
						byte[] data = Item_In_Bag[hcItimesClass65.Position].VatPham_byte;

						num25 = data[20] switch
						{
							0 => 40,
							1 => 60,
							_ => 80
						};
						if (hcItimesClass67 != null && BitConverter.ToInt32(hcItimesClass67.VatPham_id, 0) == **********)
						{
							num24 *= 2.0;
						}
                        // if (true)
                        HeThongNhacNho("Data[20]" + data[20]);
                        HeThongNhacNho("Rate "+ num24 + " / " + num25 );
						if (num24 > (double)num25 || GMMode== 8)
						{
							if (hcItimesClass68 == null)
							{
								hcItimesClass65.CuongHoaSoLuong = 0;
								hcItimesClass65.ThietLap_GiaiDoanThuocTinh();
							}
							hcItimesClass65.ThuocTinh1.ThuocTinhLoaiHinh = 22;
							hcItimesClass65.ThuocTinh1.ThuocTinhSoLuong++;
							hcItimesClass65.ThuocTinh2.ThuocTinhSoLuong = hcItimesClass65.ThuocTinh1.ThuocTinhSoLuong;
							hcItimesClass65.ThietLap_ThuocTinh();
							if (base.FLD_VIP == 1)
							{
							}
							SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
							RxjhClass.SyntheticRecord(base.Userid, base.UserName, value32.ItmeNAME, num, "TienHoaPet", "Thanh Cong", Item_In_Bag[hcItimesClass65.Position]);
						}
						else
						{
							SynthesisHint(num, -1, 0, Item_In_Bag[fromPos]);
							RxjhClass.SyntheticRecord(base.Userid, base.UserName, value32.ItmeNAME, num, "TienHoaPet", "That Bai", Item_In_Bag[hcItimesClass65.Position]);
						}

						if (GMMode != 8)
						{
						SubtractItems(hcItimesClass66.Position, 1);
						}
						if (hcItimesClass67 != null)
						{
							SubtractItems(hcItimesClass67.Position, 1);
						}
						if (hcItimesClass68 != null)
						{
							SubtractItems(hcItimesClass68.Position, 1);
						}
						if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass65.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass65.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass65.ItemGlobal_ID, 0))
						{
							if (GMMode != 8)
							{
								SubtractItems(hcItimesClass65.Position, 1);
							}
							AddItems(hcItimesClass65.ItemGlobal_ID, hcItimesClass65.VatPham_id, hcItimesClass65.Position, hcItimesClass65.VatPhamSoLuong, hcItimesClass65.VatPham_ThuocTinh);
						}
						Item_In_Bag[hcItimesClass65.Position].Khoa_Chat = false;
						HopThanhVatPham_Table.Clear();
						SynthesisSystemUnlocked();
						NguyenBao_HopThanh_MoRa = 0;
					}
					break;
				}
				catch (Exception ex35)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成 error![" + base.Userid + "]-[" + base.UserName + "]" + ex35.Message);
					break;
				}
			case 291:
				try
				{
					if (!TitleDrug.ContainsKey(1000001150) && !HopThanhVatPham_Table.ContainsKey(1) && World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0), out var value50) && (value50.FLD_PID == 1000001160 || value50.FLD_PID == 1000001151) && !Item_In_Bag[fromPos].Khoa_Chat)
					{
						HcItimesClass hcItimesClass96 = new HcItimesClass();
						hcItimesClass96.Position = fromPos;
						hcItimesClass96.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						if (!HopThanhVatPham_Table.ContainsKey(1))
						{
							HopThanhVatPham_Table.Add(1, hcItimesClass96);
						}
						SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
					}
					break;
				}
				catch (Exception ex52)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "EggSystem 291![" + base.Userid + "]-[" + base.UserName + "]" + ex52.Message);
					break;
				}
			case 295:
				if (HopThanhVatPham_Table.Count == 1)
				{
					byte[] array34 = new byte[4];
					byte[] dst3 = new byte[4];
					System.Buffer.BlockCopy(PacketData, 14, array34, 0, 4);
					System.Buffer.BlockCopy(PacketData, 18, dst3, 0, 4);
					HcItimesClass class133 = null;
					if (HopThanhVatPham_Table.ContainsKey(1))
					{
						class133 = HopThanhVatPham_Table[1];
						if (!World.Itme.TryGetValue(BitConverter.ToInt32(class133.VatPham_id, 0), out var _))
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[HopThanhVatPham_Table[1].Position]);
							break;
						}
						if (GetParcelVacancyPosition() < 1)
						{
							SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
							break;
						}
						int emptyIndex = GetParcelVacancy(this);
						X_Vat_Pham_Loai VatPhamLoai2 = DatDuocVatPhamLoaiHinh(1000001150L);
						if (emptyIndex != -1 && VatPhamLoai2 == null)
						{
							VatPhamLoai2 = DatDuocVatPhamLoaiHinh(1000001150L);
							if (VatPhamLoai2 != null)
							{
								SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
								break;
							}
							SubtractItems(BitConverter.ToInt32(array34, 0), 1);
							SubtractItems(class133.Position, 1);
							DateTime time = new DateTime(1970, 1, 1, 7, 0, 0);
							IncreaseItemWithAttributes(1000001150, emptyIndex, 1, 0, 0, 0, BitConverter.ToInt32(class133.VatPham, 0), (int)DateTime.Now.AddDays(3.0).Subtract(time).TotalSeconds, 0, 0, 0, 0, 0);
							DateTime dateTime = DateTime.Now.AddDays(3.0);
							double numtime = Convert.ToDouble(dateTime.ToString("yyMMddHHmm"));
							TimeSpan timeSpan = dateTime - DateTime.Now;
							X_Xung_Hao_Duoc_Pham_Loai X_Xung_Hao_Duoc_Pham = new X_Xung_Hao_Duoc_Pham_Loai();
							X_Xung_Hao_Duoc_Pham.ThoiGian = (long)numtime;
							X_Xung_Hao_Duoc_Pham.DuocPhamID = 1000001150;
							TitleDrug.Add(X_Xung_Hao_Duoc_Pham.DuocPhamID, X_Xung_Hao_Duoc_Pham);
							NewDrugEffects(1000001150, 1, X_Xung_Hao_Duoc_Pham.ThoiGian, (int)timeSpan.TotalMinutes);
							SynthesisHint(num, 0, 0, Item_In_Bag[class133.Position]);
							HopThanhVatPham_Table.Clear();
							SynthesisSystemUnlocked();
							SetTitleItems();
						}
						else
						{
							SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
						}
					}
					else
					{
						HeThongNhacNho("Vui lòng huỷ bỏ rồi thử lại!");
					}
				}
				else
				{
					HeThongNhacNho("Vui lòng đặt trứng vào lồng ấp!");
				}
				break;
			case 140111:
				try
				{
					byte[] array39 = new byte[4];
					byte[] array40 = new byte[4];
					byte[] array41 = new byte[2];
					System.Buffer.BlockCopy(PacketData, 14, array39, 0, 4);
					System.Buffer.BlockCopy(PacketData, 18, array40, 0, 4);
					System.Buffer.BlockCopy(PacketData, 110, array41, 0, 2);
					int num19 = BitConverter.ToInt16(array41, 0);
					int num87 = BitConverter.ToInt32(array40, 0);
					if ((num87 != 1008000041 && num87 != 1008001078) || (BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array39, 0)].VatPham_ID, 0) != 1008000041 && BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array39, 0)].VatPham_ID, 0) != 1008001078))
					{
						break;
					}
					HcItimesClass hcItimesClass62 = null;
					HcItimesClass hcItimesClass63 = null;
					HcItimesClass hcItimesClass64 = null;
					if (HopThanhVatPham_Table.ContainsKey(1))
					{
						hcItimesClass62 = HopThanhVatPham_Table[1];
					}
					if (HopThanhVatPham_Table.ContainsKey(2))
					{
						hcItimesClass63 = HopThanhVatPham_Table[2];
					}
					if (HopThanhVatPham_Table.ContainsKey(3))
					{
						hcItimesClass64 = HopThanhVatPham_Table[3];
					}
					if (hcItimesClass63 == null)
					{
						break;
					}
					hcItimesClass62.DatDuocThuocTinh();
					hcItimesClass62.CuongHoaThuocTinhGiaiDoan();
					hcItimesClass63.CuongHoaThuocTinhGiaiDoan();
					ItmeClass value31;
					if (Item_In_Bag[hcItimesClass62.Position].Vat_Pham_Khoa_Lai)
					{
						SynthesisHint(21, 3, 0, Item_In_Bag[hcItimesClass62.Position]);
						HopThanhVatPham_Table.Clear();
					}
					else if (World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass62.VatPham_id, 0), out value31))
					{
						if (value31.FLD_RESIDE2 == 4)
						{
							if (value31.FLD_RESIDE1 != 8 && value31.FLD_RESIDE1 != 9 && value31.FLD_RESIDE1 != 11 && value31.FLD_RESIDE1 != 12 && value31.FLD_RESIDE1 != 13)
							{
								SynthesisHint(21, 5, 0, Item_In_Bag[hcItimesClass62.Position]);
								HopThanhVatPham_Table.Clear();
								break;
							}
							if (BitConverter.ToInt32(hcItimesClass63.VatPham_id, 0) != 800000001 && BitConverter.ToInt32(hcItimesClass63.VatPham_id, 0) != 800000023 && BitConverter.ToInt32(hcItimesClass63.VatPham_id, 0) != 800000025 && BitConverter.ToInt32(hcItimesClass63.VatPham_id, 0) != 800000026 && BitConverter.ToInt32(hcItimesClass63.VatPham_id, 0) != 800000061)
							{
								SynthesisHint(21, 5, 0, Item_In_Bag[hcItimesClass62.Position]);
								HopThanhVatPham_Table.Clear();
								break;
							}
						}
						else
						{
							if (value31.FLD_RESIDE2 != 12)
							{
								SynthesisHint(21, 5, 0, Item_In_Bag[hcItimesClass62.Position]);
								HopThanhVatPham_Table.Clear();
								break;
							}
							if (BitConverter.ToInt32(hcItimesClass63.VatPham_id, 0) != 800000013)
							{
								SynthesisHint(21, 5, 0, Item_In_Bag[hcItimesClass62.Position]);
								HopThanhVatPham_Table.Clear();
								break;
							}
						}
						if (Phi_HopThanh > 0)
						{
							if (value31.FLD_NJ == 0)
							{
								if (base.Player_Money < Phi_HopThanh)
								{
									SynthesisHint(11, 4, 0, Item_In_Bag[hcItimesClass62.Position]);
									HopThanhVatPham_Table.Clear();
									break;
								}
								base.Player_Money -= Phi_HopThanh;
								UpdateMoneyAndWeight();
							}
							else
							{
								if (base.Player_Money < Phi_HopThanh)
								{
									SynthesisHint(11, 4, 0, Item_In_Bag[hcItimesClass62.Position]);
									HopThanhVatPham_Table.Clear();
									break;
								}
								hcItimesClass62.FLD_FJ_NJ = 0;
								base.Player_Money -= Phi_HopThanh;
								UpdateMoneyAndWeight();
								Init_Item_In_Bag();
								CalculateCharacterEquipmentData();
								UpdateMartialArtsAndStatus();
							}
						}
						Random random3 = new Random();
						string[] array6 = World.TongXacSuat_HoaLongThach.Split(';');
						double num21 = random3.Next(int.Parse(array6[0]), int.Parse(array6[1]));
						double num22 = hcItimesClass62.ThuocTinh1.SoLuong * 20 + hcItimesClass62.ThuocTinh2.SoLuong * 20 + hcItimesClass62.ThuocTinh3.SoLuong * 20 + hcItimesClass62.ThuocTinh4.SoLuong * 20;
						if (hcItimesClass62.ThuocTinh1.ThuocTinhSoLuong != 0)
						{
							if (hcItimesClass64 != null)
							{
								num21 += (double)LuckyCharmBonus(BitConverter.ToInt32(hcItimesClass64.VatPham_id, 0));
							}
							if (NguyenBao_HopThanh_MoRa == 1)
							{
								num21 += 5.0;
							}
							if (base.FLD_VIP == 1)
							{
							}
							if (base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
							{
								num21 += 100.0 * base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
							}
							if (base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
							{
								num21 += 100.0 * base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
							}
							if (num21 >= num22)
							{
								switch (num19)
								{
								case 0:
									if (hcItimesClass62.ThuocTinh1.ThuocTinhLoaiHinh != 0)
									{
										if (hcItimesClass63.GiaiDoanLoaiHinh == 8)
										{
											hcItimesClass62.ThuocTinh1.KhiCongThuocTinhLoaiHinh = hcItimesClass63.KhiCongThuocTinhLoaiHinh;
										}
										hcItimesClass62.ThuocTinh1.ThuocTinhLoaiHinh = hcItimesClass63.GiaiDoanLoaiHinh;
										hcItimesClass62.ThuocTinh1.ThuocTinhSoLuong = hcItimesClass63.GiaiDoanSoLuong;
										hcItimesClass62.ThietLap_ThuocTinh();
									}
									break;
								case 1:
									if (hcItimesClass62.ThuocTinh2.ThuocTinhLoaiHinh != 0)
									{
										if (hcItimesClass63.GiaiDoanLoaiHinh == 8)
										{
											hcItimesClass62.ThuocTinh2.KhiCongThuocTinhLoaiHinh = hcItimesClass63.KhiCongThuocTinhLoaiHinh;
										}
										hcItimesClass62.ThuocTinh2.ThuocTinhLoaiHinh = hcItimesClass63.GiaiDoanLoaiHinh;
										hcItimesClass62.ThuocTinh2.ThuocTinhSoLuong = hcItimesClass63.GiaiDoanSoLuong;
										hcItimesClass62.ThietLap_ThuocTinh();
									}
									break;
								case 2:
									if (hcItimesClass62.ThuocTinh3.ThuocTinhLoaiHinh != 0)
									{
										if (hcItimesClass63.GiaiDoanLoaiHinh == 8)
										{
											hcItimesClass62.ThuocTinh3.KhiCongThuocTinhLoaiHinh = hcItimesClass63.KhiCongThuocTinhLoaiHinh;
										}
										hcItimesClass62.ThuocTinh3.ThuocTinhLoaiHinh = hcItimesClass63.GiaiDoanLoaiHinh;
										hcItimesClass62.ThuocTinh3.ThuocTinhSoLuong = hcItimesClass63.GiaiDoanSoLuong;
										hcItimesClass62.ThietLap_ThuocTinh();
									}
									break;
								case 3:
									if (hcItimesClass62.ThuocTinh4.ThuocTinhLoaiHinh != 0)
									{
										if (hcItimesClass63.GiaiDoanLoaiHinh == 8)
										{
											hcItimesClass62.ThuocTinh4.KhiCongThuocTinhLoaiHinh = hcItimesClass63.KhiCongThuocTinhLoaiHinh;
										}
										hcItimesClass62.ThuocTinh4.ThuocTinhLoaiHinh = hcItimesClass63.GiaiDoanLoaiHinh;
										hcItimesClass62.ThuocTinh4.ThuocTinhSoLuong = hcItimesClass63.GiaiDoanSoLuong;
										hcItimesClass62.ThietLap_ThuocTinh();
									}
									break;
								}
								RxjhClass.SyntheticRecord(base.Userid, base.UserName, value31.ItmeNAME, num, "HoaLongThach", "Thanh Cong", Item_In_Bag[hcItimesClass62.Position]);
								SynthesisHint(num, 1, Phi_HopThanh, Item_In_Bag[hcItimesClass62.Position]);
							}
							else
							{
								RxjhClass.SyntheticRecord(base.Userid, base.UserName, value31.ItmeNAME, num, "HoaLongThach", "That Bai", Item_In_Bag[hcItimesClass62.Position]);
								SynthesisHint(num, 0, Phi_HopThanh, Item_In_Bag[hcItimesClass62.Position]);
							}
							SubtractItems(hcItimesClass63.Position, 1);
							SubtractItems(BitConverter.ToInt32(array39, 0), 1);
							if (hcItimesClass64 != null)
							{
								SubtractItems(hcItimesClass64.Position, 1);
							}
							if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass62.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass62.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass62.ItemGlobal_ID, 0))
							{
								SubtractItems(hcItimesClass62.Position, 1);
								AddItems(hcItimesClass62.ItemGlobal_ID, hcItimesClass62.VatPham_id, hcItimesClass62.Position, hcItimesClass62.VatPhamSoLuong, hcItimesClass62.VatPham_ThuocTinh);
							}
							Item_In_Bag[hcItimesClass62.Position].Khoa_Chat = false;
							HopThanhVatPham_Table.Clear();
							SynthesisSystemUnlocked();
							NguyenBao_HopThanh_MoRa = 0;
							Phi_HopThanh = 0;
							OpenWarehouse = false;
						}
						else
						{
							SynthesisHint(21, 5, 0, Item_In_Bag[hcItimesClass62.Position]);
							HopThanhVatPham_Table.Clear();
						}
					}
					else
					{
						SynthesisHint(21, 5, 0, Item_In_Bag[hcItimesClass62.Position]);
						HopThanhVatPham_Table.Clear();
					}
					break;
				}
				catch (Exception ex34)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成 error![" + base.Userid + "]-[" + base.UserName + "]" + ex34.Message);
				}
				goto default;
			case 14111:
				try
				{
					int num13 = PacketData[14];
					ItmeClass value25;
					if (Item_In_Bag[fromPos].Khoa_Chat)
					{
						SynthesisHint(num, 3, 0, Item_In_Bag[fromPos]);
					}
					else if (Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
					{
						SynthesisHint(num, 3, 0, Item_In_Bag[fromPos]);
					}
					else if (Item_In_Bag[fromPos].FLD_MAGIC1 != 0)
					{
						if (Item_In_Bag[num13].GetVatPham_ID != 1008000041)
						{
							SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
						}
						else if (World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0), out value25))
						{
							if (Item_In_Bag[num13].GetVatPham_ID == 1008000041)
							{
								if (value25.FLD_RESIDE2 != 4)
								{
									SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
								}
								else
								{
									if (value25.FLD_RESIDE1 == 8 || value25.FLD_RESIDE1 == 9 || value25.FLD_RESIDE1 == 11 || value25.FLD_RESIDE1 == 12 || value25.FLD_RESIDE1 == 13)
									{
										goto IL_10930;
									}
									SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
								}
							}
							else
							{
								if (Item_In_Bag[num13].GetVatPham_ID == 1008001078 && value25.FLD_RESIDE2 == 12)
								{
									goto IL_10930;
								}
								SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
							}
						}
						else
						{
							SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
						}
					}
					else
					{
						SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
					}
					goto end_IL_10845;
					IL_10930:
					if (!HopThanhVatPham_Table.ContainsKey(1))
					{
						Phi_HopThanh = CalculateSyntheticEnhancementCost(value25, fromPos, num);
						if (Item_In_Bag[num13].GetVatPham_ID == 1008001078)
						{
							Phi_HopThanh = 10000000;
						}
						HcItimesClass hcItimesClass58 = new HcItimesClass();
						hcItimesClass58.Position = fromPos;
						hcItimesClass58.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(1, hcItimesClass58);
						SynthesisHint(num, 1, Phi_HopThanh, Item_In_Bag[fromPos]);
					}
					end_IL_10845:;
				}
				catch (Exception ex30)
				{
					Form1.WriteLine(1, "Hoa Long Thach 141 Error![" + base.Userid + "]-[" + base.UserName + "]" + ex30.Message);
				}
				goto default;
			case 1421111:
				try
				{
					if (HopThanhVatPham_Table.Count == 0)
					{
						SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
					}
					else
					{
						int num11 = PacketData[14];
						HcItimesClass hcItimesClass57 = null;
						if (HopThanhVatPham_Table.ContainsKey(1))
						{
							hcItimesClass57 = HopThanhVatPham_Table[1];
						}
						if (!World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass57.VatPham_id, 0), out var value24))
						{
							SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
						}
						else if (Item_In_Bag[num11].GetVatPham_ID == 1008000041)
						{
							if (Item_In_Bag[fromPos].GetVatPham_ID != 800000061 && Item_In_Bag[fromPos].GetVatPham_ID != 800000023 && Item_In_Bag[fromPos].GetVatPham_ID != 800000001)
							{
								SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
							}
							else if (value24.FLD_LEVEL < 80)
							{
								if (Item_In_Bag[fromPos].GetVatPham_ID == 800000001)
								{
									goto IL_10ef4;
								}
								SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
							}
							else if (value24.FLD_LEVEL >= 80 && value24.FLD_LEVEL < 130)
							{
								if (Item_In_Bag[fromPos].GetVatPham_ID == 800000001 || Item_In_Bag[fromPos].GetVatPham_ID == 800000023)
								{
									goto IL_10ef4;
								}
								SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
							}
							else
							{
								if (value24.FLD_LEVEL < 130 || Item_In_Bag[fromPos].GetVatPham_ID == 800000061)
								{
									goto IL_10ef4;
								}
								SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
							}
						}
						else if (Item_In_Bag[num11].GetVatPham_ID == 1008001078)
						{
							if (Item_In_Bag[fromPos].GetVatPham_ID == 800000013)
							{
								goto IL_10ef4;
							}
							SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
						}
						else
						{
							SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
						}
					}
					goto end_IL_10be3;
					IL_10ef4:
					if (!HopThanhVatPham_Table.ContainsKey(2))
					{
						HopThanhVatPham_Table.Add(2, new HcItimesClass
						{
							Position = fromPos,
							VatPham = Item_In_Bag[fromPos].VatPham_byte
						});
						Item_In_Bag[fromPos].Khoa_Chat = true;
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
					end_IL_10be3:;
				}
				catch (Exception ex29)
				{
					Form1.WriteLine(1, "Hoa Long Thach 142 Error![" + base.Userid + "]-[" + base.UserName + "]" + ex29.Message);
				}
				goto default;
			case 1431111:
				try
				{
					if (HopThanhVatPham_Table.Count == 0)
					{
						SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
					}
					else if (!HopThanhVatPham_Table.ContainsKey(3))
					{
						HopThanhVatPham_Table.Add(3, new HcItimesClass
						{
							Position = fromPos,
							VatPham = Item_In_Bag[fromPos].VatPham_byte
						});
						Item_In_Bag[fromPos].Khoa_Chat = true;
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex27)
				{
					Form1.WriteLine(1, "List 143: Error![" + base.Userid + "]-[" + base.UserName + "]" + ex27.Message);
				}
				goto default;
			case 148:
			case 149:
				try
				{
					if (HopThanhVatPham_Table.TryGetValue(1, out var value23))
					{
						Item_In_Bag[value23.Position].Khoa_Chat = false;
						SynthesisHint(num, 1, 0, Item_In_Bag[value23.Position]);
					}
					HopThanhVatPham_Table.Clear();
					SynthesisSystemUnlocked();
					NguyenBao_HopThanh_MoRa = 0;
					OpenWarehouse = false;
				}
				catch (Exception ex26)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成4 error![" + base.Userid + "]-[" + base.UserName + "]" + ex26.Message);
				}
				goto default;
			case 150:
				try
				{
					if (HopThanhVatPham_Table.Count > 0)
					{
						HcItimesClass hcItimesClass27 = null;
						HcItimesClass hcItimesClass41 = null;
						HcItimesClass hcItimesClass49 = null;
						if (HopThanhVatPham_Table.ContainsKey(1))
						{
							hcItimesClass27 = HopThanhVatPham_Table[1];
						}
						if (HopThanhVatPham_Table.ContainsKey(2))
						{
							hcItimesClass41 = HopThanhVatPham_Table[2];
						}
						if (HopThanhVatPham_Table.ContainsKey(3))
						{
							hcItimesClass49 = HopThanhVatPham_Table[3];
						}
						if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass27.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass27.ItemGlobal_ID, 0) && BitConverter.ToInt64(Item_In_Bag[hcItimesClass41.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass41.ItemGlobal_ID, 0) && (hcItimesClass49 == null || BitConverter.ToInt64(Item_In_Bag[hcItimesClass49.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass49.ItemGlobal_ID, 0)) && World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass27.VatPham_id, 0), out var value12))
						{
							if (value12.FLD_RESIDE2 != 1 && value12.FLD_RESIDE2 != 4)
							{
								Form1.WriteLine(6, "合成系统 CuongHoa BUG1[" + BitConverter.ToInt32(hcItimesClass27.VatPham_id, 0) + "]-[" + BitConverter.ToInt32(hcItimesClass41.VatPham_id, 0) + "]");
							}
							else if (Item_In_Bag[hcItimesClass27.Position].Vat_Pham_Khoa_Lai)
							{
								SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass27.Position]);
							}
							else
							{
								Random random13 = new Random();
								string[] array14 = World.TongXacSuat_Soul.Split(';');
								double num8 = random13.Next(int.Parse(array14[0]), int.Parse(array14[1]));
								double num9 = hcItimesClass27.FLD_FJ_ThucTinh * 10;
								int MaxSoul = 5;
								if (Item_In_Bag[HopThanhVatPham_Table[2].Position].GetVatPham_ID == 1000000365)
								{
									MaxSoul *= 2;
								}
								else if (Item_In_Bag[HopThanhVatPham_Table[2].Position].GetVatPham_ID == 1000000367)
								{
									MaxSoul = 5;
								}
								if (hcItimesClass27.FLD_FJ_ThucTinh >= MaxSoul)
								{
									HopThanhVatPham_Table.Clear();
									SynthesisHint(num, 6, 0, Item_In_Bag[fromPos]);
									SynthesisSystemUnlocked();
									NguyenBao_HopThanh_MoRa = 0;
								}
								else
								{
									if (hcItimesClass49 != null)
									{
										num8 += (double)LuckyCharmBonus(BitConverter.ToInt32(hcItimesClass49.VatPham_id, 0));
									}
									if (NguyenBao_HopThanh_MoRa == 1)
									{
										num8 += 5.0;
									}
									if (base.FLD_VIP == 1)
									{
									}
									if (World.TyLe_PhuHon != 0.0)
									{
										num8 += 100.0 * World.TyLe_PhuHon;
									}
									if (base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
									{
										num8 += 100.0 * base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
									}
									if (base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
									{
										num8 += 100.0 * base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
									}
									if (GMMode == 5)
									{
										num9 = 0.0;
									}
									if (num8 >= num9)
									{
										HcItimesClass hcItimesClass156 = hcItimesClass27;
										int giaiDoanSoLuong = hcItimesClass156.FLD_FJ_ThucTinh + 1;
										hcItimesClass156.FLD_FJ_ThucTinh = giaiDoanSoLuong;
										SynthesisHint(num, 1, 0, Item_In_Bag[hcItimesClass27.Position]);
										RxjhClass.SyntheticRecord(base.Userid, base.UserName, value12.ItmeNAME, num, "PhuHon", "Thanh Cong", Item_In_Bag[hcItimesClass27.Position]);
									}
									else
									{
										RxjhClass.SyntheticRecord(base.Userid, base.UserName, value12.ItmeNAME, num, "PhuHon", "That Bai", Item_In_Bag[hcItimesClass27.Position]);
										SynthesisHint(num, 0, 0, Item_In_Bag[hcItimesClass27.Position]);
									}
									SubtractItems(hcItimesClass41.Position, 1);
									if (hcItimesClass49 != null)
									{
										SubtractItems(hcItimesClass49.Position, 1);
									}
									if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass27.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass27.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass27.ItemGlobal_ID, 0))
									{
										SubtractItems(hcItimesClass27.Position, 1);
										AddItems(hcItimesClass27.ItemGlobal_ID, hcItimesClass27.VatPham_id, hcItimesClass27.Position, hcItimesClass27.VatPhamSoLuong, hcItimesClass27.VatPham_ThuocTinh);
									}
									Item_In_Bag[hcItimesClass27.Position].Khoa_Chat = false;
									HopThanhVatPham_Table.Clear();
									SynthesisSystemUnlocked();
									NguyenBao_HopThanh_MoRa = 0;
									OpenWarehouse = false;
								}
							}
						}
					}
				}
				catch (Exception ex18)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 合成ThuocTinhGiaiDoan 合成 error![" + base.Userid + "]-[" + base.UserName + "]" + ex18.Message);
				}
				goto default;
			case 151:
				try
				{
					if (base.CurrentOperationType == 122 && HopThanhVatPham_Table.Count != 0 && HopThanhVatPham_Table.ContainsKey(2) && !HopThanhVatPham_Table.ContainsKey(1) && !Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
					{
						int MaxSoul2 = 5;
						if (Item_In_Bag[HopThanhVatPham_Table[2].Position].GetVatPham_ID == 1000000365)
						{
							MaxSoul2 *= 2;
						}
						else if (Item_In_Bag[HopThanhVatPham_Table[2].Position].GetVatPham_ID == 1000000367)
						{
							MaxSoul2 = 5;
						}
						if (Item_In_Bag[fromPos].FLD_FJ_LowSoul >= MaxSoul2)
						{
							HopThanhVatPham_Table.Clear();
							SynthesisSystemUnlocked();
							NguyenBao_HopThanh_MoRa = 0;
							OpenWarehouse = false;
							SynthesisHint(num, 6, 0, Item_In_Bag[fromPos]);
						}
						else
						{
							HcItimesClass hcItimesClass16 = new HcItimesClass();
							hcItimesClass16.Position = fromPos;
							hcItimesClass16.VatPham = Item_In_Bag[fromPos].VatPham_byte;
							Item_In_Bag[fromPos].Khoa_Chat = true;
							HopThanhVatPham_Table.Add(1, hcItimesClass16);
							SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
						}
					}
				}
				catch (Exception ex17)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinhGiaiDoan 合成1 error![" + base.Userid + "]-[" + base.UserName + "]" + ex17.Message);
				}
				goto default;
			case 152:
				try
				{
					if (base.CurrentOperationType == 122)
					{
						if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 1000000365 && BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 1000000367)
						{
							SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
						}
						else
						{
							if (HopThanhVatPham_Table.Count != 0)
							{
								HopThanhVatPham_Table.Clear();
							}
							if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) == 1000000365 || BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) == 1000000367)
							{
								HcItimesClass hcItimesClass71 = new HcItimesClass();
								hcItimesClass71.Position = fromPos;
								hcItimesClass71.VatPham = Item_In_Bag[fromPos].VatPham_byte;
								Item_In_Bag[fromPos].Khoa_Chat = true;
								HopThanhVatPham_Table.Add(2, hcItimesClass71);
								OpenWarehouse = true;
								SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
							}
						}
					}
				}
				catch (Exception ex36)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinhGiaiDoan 合成2 error![" + base.Userid + "]-[" + base.UserName + "]" + ex36.Message);
				}
				goto default;
			case 153:
				try
				{
					if (HopThanhVatPham_Table.Count != 0 && !HopThanhVatPham_Table.ContainsKey(3))
					{
						HcItimesClass hcItimesClass55 = new HcItimesClass();
						hcItimesClass55.Position = fromPos;
						hcItimesClass55.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(3, hcItimesClass55);
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex28)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinhGiaiDoan 合成3 error![" + base.Userid + "]-[" + base.UserName + "]" + ex28.Message);
				}
				goto default;
			case 159:
				try
				{
					if (HopThanhVatPham_Table.TryGetValue(1, out var value11))
					{
						Item_In_Bag[value11.Position].Khoa_Chat = false;
						SynthesisHint(num, 1, 0, Item_In_Bag[value11.Position]);
					}
					HopThanhVatPham_Table.Clear();
					SynthesisSystemUnlocked();
					NguyenBao_HopThanh_MoRa = 0;
					OpenWarehouse = false;
				}
				catch (Exception ex15)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 合成ThuocTinhGiaiDoan 合成4 error![" + base.Userid + "]-[" + base.UserName + "]" + ex15.Message);
				}
				goto default;
			case 170:
				try
				{
					if (HopThanhVatPham_Table.Count > 0)
					{
						HcItimesClass hcItimesClass42 = null;
						if (HopThanhVatPham_Table.ContainsKey(1))
						{
							HcItimesClass hcItimesClass43 = HopThanhVatPham_Table[1];
							if (HopThanhVatPham_Table.ContainsKey(2))
							{
								HcItimesClass hcItimesClass44 = HopThanhVatPham_Table[2];
								if (HopThanhVatPham_Table.ContainsKey(3))
								{
									hcItimesClass42 = HopThanhVatPham_Table[3];
								}
								if (HopThanhVatPham_Table.ContainsKey(4))
								{
									HcItimesClass hcItimesClass45 = HopThanhVatPham_Table[4];
									if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass43.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass43.ItemGlobal_ID, 0) && BitConverter.ToInt64(Item_In_Bag[hcItimesClass44.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass44.ItemGlobal_ID, 0) && BitConverter.ToInt64(Item_In_Bag[hcItimesClass45.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass45.ItemGlobal_ID, 0) && (hcItimesClass42 == null || BitConverter.ToInt64(Item_In_Bag[hcItimesClass42.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass42.ItemGlobal_ID, 0)) && World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass43.VatPham_id, 0), out var value8))
									{
										if (value8.FLD_RESIDE2 != 1 && value8.FLD_RESIDE2 != 4)
										{
											Form1.WriteLine(6, "合成系统 TrungCapPhuHon BUG1[" + BitConverter.ToInt32(hcItimesClass43.VatPham_id, 0) + "]-[" + BitConverter.ToInt32(hcItimesClass44.VatPham_id, 0) + "]");
										}
										else if (value8.FLD_LEVEL >= 80)
										{
											if (Item_In_Bag[hcItimesClass43.Position].Vat_Pham_Khoa_Lai)
											{
												SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass43.Position]);
											}
											else
											{
												hcItimesClass44.CuongHoaThuocTinhGiaiDoan();
												Random random14 = new Random();
												string[] array15 = World.TongXacSuat_SoulTrung.Split(';');
												double num85 = random14.Next(int.Parse(array15[0]), int.Parse(array15[1]));
												double num86 = hcItimesClass43.FLD_FJ_ThucTinh * 10;
												if (hcItimesClass42 != null)
												{
													num85 += (double)LuckyCharmBonus(BitConverter.ToInt32(hcItimesClass42.VatPham_id, 0));
												}
												if (NguyenBao_HopThanh_MoRa == 1)
												{
													num85 += 5.0;
												}
												if (base.FLD_VIP == 1)
												{
												}
												if (World.TyLe_HopThanh != 0.0)
												{
													num85 += 100.0 * World.TyLe_HopThanh;
												}
												if (base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
												{
													num85 += 100.0 * base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
												}
												if (base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
												{
													num85 += 100.0 * base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
												}
												if (num85 >= num86)
												{
													hcItimesClass43.FLD_FJ_TrungCapPhuHon = hcItimesClass44.GiaiDoanSoLuong;
													SubtractItems(hcItimesClass43.Position, 1);
													AddItems(hcItimesClass43.ItemGlobal_ID, hcItimesClass43.VatPham_id, hcItimesClass43.Position, hcItimesClass43.VatPhamSoLuong, hcItimesClass43.VatPham_ThuocTinh);
													if (hcItimesClass44 != null)
													{
														SubtractItems(hcItimesClass44.Position, 1);
													}
													if (hcItimesClass45 != null)
													{
														SubtractItems(hcItimesClass45.Position, 1);
													}
													if (hcItimesClass42 != null)
													{
														SubtractItems(hcItimesClass42.Position, 1);
													}
													Item_In_Bag[hcItimesClass43.Position].Khoa_Chat = false;
													HopThanhVatPham_Table.Clear();
													SynthesisSystemUnlocked();
													NguyenBao_HopThanh_MoRa = 0;
													RxjhClass.SyntheticRecord(base.Userid, base.UserName, value8.ItmeNAME, num, "TrungCapPhuHon", "Thanh Cong", Item_In_Bag[hcItimesClass43.Position]);
													SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
												}
												else
												{
													if (hcItimesClass44 != null)
													{
														SubtractItems(hcItimesClass44.Position, 1);
													}
													if (hcItimesClass45 != null)
													{
														SubtractItems(hcItimesClass45.Position, 1);
													}
													if (hcItimesClass42 != null)
													{
														SubtractItems(hcItimesClass42.Position, 1);
													}
													Item_In_Bag[hcItimesClass43.Position].Khoa_Chat = false;
													HopThanhVatPham_Table.Clear();
													SynthesisSystemUnlocked();
													NguyenBao_HopThanh_MoRa = 0;
													RxjhClass.SyntheticRecord(base.Userid, base.UserName, value8.ItmeNAME, num, "TrungCapPhuHon", "That Bai", Item_In_Bag[hcItimesClass43.Position]);
													SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
												}
											}
										}
									}
								}
							}
						}
					}
				}
				catch (Exception ex5)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 TrungCapPhuHon 合成 error![" + base.Userid + "]-[" + base.UserName + "]" + ex5.Message);
				}
				goto default;
			case 171:
				try
				{
					if (HopThanhVatPham_Table.Count != 0 && HopThanhVatPham_Table.ContainsKey(4) && !HopThanhVatPham_Table.ContainsKey(1) && World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0), out var value54) && (value54.FLD_RESIDE2 == 1 || value54.FLD_RESIDE2 == 4) && value54.FLD_LEVEL >= 80)
					{
						HcItimesClass hcItimesClass100 = new HcItimesClass();
						hcItimesClass100.Position = fromPos;
						hcItimesClass100.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(1, hcItimesClass100);
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex104)
				{
					Form1.WriteLine(1, "合成系统 TrungCapPhuHon 合成1 error![" + base.Userid + "]-[" + base.UserName + "]" + ex104.Message);
				}
				goto default;
			case 172:
				try
				{
					HcItimesClass hcItimesClass28;
					if (HopThanhVatPham_Table.Count != 0 && !HopThanhVatPham_Table.ContainsKey(2))
					{
						hcItimesClass28 = new HcItimesClass();
						hcItimesClass28.Position = fromPos;
						hcItimesClass28.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						hcItimesClass28.CuongHoaThuocTinhGiaiDoan();
						if (HopThanhVatPham_Table.ContainsKey(1))
						{
							HcItimesClass hcItimesClass30 = HopThanhVatPham_Table[1];
							if (!World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass30.VatPham_id, 0), out var value102))
							{
								goto IL_129c6;
							}
							if (!KiemTra_KiNgocThach(value102.FLD_RESIDE2, hcItimesClass28.GiaiDoanSoLuong))
							{
								SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
								HopThanhVatPham_Table.Remove(1);
							}
							else if (value102.FLD_RESIDE2 == 4)
							{
								if (value102.FLD_LEVEL >= 80)
								{
									goto IL_129c6;
								}
								SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
								HopThanhVatPham_Table.Remove(1);
							}
							else
							{
								if (value102.FLD_RESIDE2 != 1 || value102.FLD_LEVEL >= 80)
								{
									goto IL_129c6;
								}
								SynthesisHint(num, 3, 0, Item_In_Bag[fromPos]);
								HopThanhVatPham_Table.Remove(1);
							}
						}
					}
					goto end_IL_127e2;
					IL_129c6:
					Item_In_Bag[fromPos].Khoa_Chat = true;
					HopThanhVatPham_Table.Add(2, hcItimesClass28);
					SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					end_IL_127e2:;
				}
				catch (Exception ex111)
				{
					Form1.WriteLine(1, "合成系统 TrungCapPhuHon 合成2 error![" + base.Userid + "]-[" + base.UserName + "]" + ex111.Message);
				}
				goto default;
			case 173:
				try
				{
					if (HopThanhVatPham_Table.Count != 0 && !HopThanhVatPham_Table.ContainsKey(3))
					{
						HcItimesClass hcItimesClass31 = new HcItimesClass();
						hcItimesClass31.Position = fromPos;
						hcItimesClass31.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(3, hcItimesClass31);
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex112)
				{
					Form1.WriteLine(1, "合成系统 TrungCapPhuHon 合成3 error![" + base.Userid + "]-[" + base.UserName + "]" + ex112.Message);
				}
				goto default;
			case 174:
				try
				{
					if (!HopThanhVatPham_Table.ContainsKey(4) && BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) == 1000000330)
					{
						HcItimesClass hcItimesClass34 = new HcItimesClass();
						hcItimesClass34.Position = fromPos;
						hcItimesClass34.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(4, hcItimesClass34);
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex3)
				{
					Form1.WriteLine(1, "合成系统 TrungCapPhuHon 合成4 error![" + base.Userid + "]-[" + base.UserName + "]" + ex3.Message);
				}
				goto default;
			case 179:
				try
				{
					HopThanhVatPham_Table.Clear();
					SynthesisSystemUnlocked();
					NguyenBao_HopThanh_MoRa = 0;
					OpenWarehouse = false;
					SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
				}
				catch (Exception ex119)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 合成ThuocTinhGiaiDoan 合成4 error![" + base.Userid + "]-[" + base.UserName + "]" + ex119.Message);
				}
				goto default;
			case 180:
				if (HopThanhVatPham_Table.Count > 0 && HopThanhVatPham_Table.ContainsKey(1))
				{
					HcItimesClass hcItimesClass60 = null;
					HcItimesClass hcItimesClass61 = null;
					if (HopThanhVatPham_Table.ContainsKey(1))
					{
						hcItimesClass60 = HopThanhVatPham_Table[1];
					}
					if (HopThanhVatPham_Table.ContainsKey(2))
					{
						hcItimesClass61 = HopThanhVatPham_Table[2];
					}
					if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass60.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass60.ItemGlobal_ID, 0) && (hcItimesClass61 == null || BitConverter.ToInt64(Item_In_Bag[hcItimesClass61.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass61.ItemGlobal_ID, 0)))
					{
						if (World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass60.VatPham_id, 0), out var value26))
						{
							if (value26.FLD_RESIDE2 != 7 && value26.FLD_RESIDE2 != 8 && value26.FLD_RESIDE2 != 10)
							{
								SynthesisHint(num, 19, 0, Item_In_Bag[hcItimesClass60.Position]);
								HopThanhVatPham_Table.Clear();
							}
							else if (value26.FLD_UP_LEVEL == 0)
							{
								SynthesisHint(num, 19, 0, Item_In_Bag[fromPos]);
								HopThanhVatPham_Table.Clear();
							}
							else if (value26.FLD_PID != 100026 && (value26.FLD_PID != 18 || value26.FLD_PID != 700911))
							{
								if (base.Player_Money < 10000000)
								{
									SynthesisHint(11, 4, 10000000, Item_In_Bag[hcItimesClass60.Position]);
									HopThanhVatPham_Table.Clear();
								}
								else
								{
									base.Player_Money -= 10000000L;
									UpdateMoneyAndWeight();
									double num14 = RNG.Next(0, 101 + value26.FLD_LEVEL * 2);
									double num15 = 0.0;
									if (hcItimesClass61 != null)
									{
										num15 += (double)LuckyCharmBonus(BitConverter.ToInt32(hcItimesClass61.VatPham_id, 0));
									}
									if (NguyenBao_HopThanh_MoRa == 1)
									{
										num15 += 5.0;
									}
									if (base.FLD_VIP == 1)
									{
									}
									if (World.TyLe_NangCap_TrangSuc != 0.0)
									{
										num15 += 100.0 * World.TyLe_NangCap_TrangSuc;
									}
									if (base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
									{
										num15 += 100.0 * base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
									}
									if (base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
									{
										num15 += 100.0 * base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
									}
									if (num15 >= num14)
									{
										int num16 = NangCap_DoTrangSuc_NhanDoTrangSuc(value26.FLD_RESIDE2, value26.FLD_PID, 0);
										if (num16 == 0)
										{
											SynthesisHint(num, 19, Phi_HopThanh, Item_In_Bag[hcItimesClass60.Position]);
											RxjhClass.SyntheticRecord(base.Userid, base.UserName, value26.ItmeNAME, num, "Jw-Upgrade", "That Bai", Item_In_Bag[hcItimesClass60.Position]);
											HopThanhVatPham_Table.Clear();
											goto default;
										}
										byte[] bytes = BitConverter.GetBytes(num16);
										if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass60.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass60.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass60.ItemGlobal_ID, 0))
										{
											SubtractItems(hcItimesClass60.Position, 1);
											if (World.Itme.TryGetValue(num16, out var value27))
											{
												byte[] array37 = new byte[World.VatPham_ThuocTinh_KichThuoc];
												System.Buffer.BlockCopy(hcItimesClass60.VatPham_ThuocTinh, 0, array37, 0, World.VatPham_ThuocTinh_KichThuoc);
												System.Buffer.BlockCopy(BitConverter.GetBytes(value27.FLD_MAGIC1), 0, array37, 4, 4);
												System.Buffer.BlockCopy(BitConverter.GetBytes(value27.FLD_MAGIC2), 0, array37, 8, 4);
												System.Buffer.BlockCopy(BitConverter.GetBytes(value27.FLD_MAGIC3), 0, array37, 12, 4);
												System.Buffer.BlockCopy(BitConverter.GetBytes(value27.FLD_MAGIC4), 0, array37, 16, 4);
												AddItems(hcItimesClass60.ItemGlobal_ID, bytes, hcItimesClass60.Position, hcItimesClass60.VatPhamSoLuong, array37);
											}
										}
										if (hcItimesClass61 != null)
										{
											SubtractItems(hcItimesClass61.Position, 1);
										}
										RxjhClass.SyntheticRecord(base.Userid, base.UserName, value26.ItmeNAME, num, "Jw-Upgrade", "Thanh Cong", Item_In_Bag[hcItimesClass60.Position]);
										SynthesisHint(num, 11, Phi_HopThanh, Item_In_Bag[hcItimesClass60.Position]);
									}
									else
									{
										int num17 = NangCap_DoTrangSuc_NhanDoTrangSuc(value26.FLD_RESIDE2, value26.FLD_PID, 1);
										if (num17 != 0)
										{
											double num18 = RNG.Next(1, 100);
											byte[] bytes2 = BitConverter.GetBytes(num17);
											if (num18 >= 0.0 && num18 < 10.0)
											{
												if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass60.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass60.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass60.ItemGlobal_ID, 0))
												{
													SubtractItems(hcItimesClass60.Position, 1);
													if (World.Itme.TryGetValue(num17, out var value28))
													{
														byte[] array38 = new byte[World.VatPham_ThuocTinh_KichThuoc];
														System.Buffer.BlockCopy(hcItimesClass60.VatPham_ThuocTinh, 0, array38, 0, World.VatPham_ThuocTinh_KichThuoc);
														System.Buffer.BlockCopy(BitConverter.GetBytes(value28.FLD_MAGIC1), 0, array38, 4, 4);
														System.Buffer.BlockCopy(BitConverter.GetBytes(value28.FLD_MAGIC2), 0, array38, 8, 4);
														System.Buffer.BlockCopy(BitConverter.GetBytes(value28.FLD_MAGIC3), 0, array38, 12, 4);
														System.Buffer.BlockCopy(BitConverter.GetBytes(value28.FLD_MAGIC4), 0, array38, 16, 4);
														AddItems(hcItimesClass60.ItemGlobal_ID, bytes2, hcItimesClass60.Position, hcItimesClass60.VatPhamSoLuong, array38);
													}
												}
											}
											else
											{
												SubtractItems(hcItimesClass60.Position, 1);
											}
										}
										else
										{
											SubtractItems(hcItimesClass60.Position, 1);
										}
										if (hcItimesClass61 != null)
										{
											SubtractItems(hcItimesClass61.Position, 1);
										}
										SynthesisHint(num, 10, Phi_HopThanh, Item_In_Bag[hcItimesClass60.Position]);
									}
									Item_In_Bag[hcItimesClass60.Position].Khoa_Chat = false;
									HopThanhVatPham_Table.Clear();
									SynthesisSystemUnlocked();
									NguyenBao_HopThanh_MoRa = 0;
									Phi_HopThanh = 0;
								}
							}
							else
							{
								SynthesisHint(num, 19, 0, Item_In_Bag[fromPos]);
								HopThanhVatPham_Table.Clear();
							}
						}
						else
						{
							SynthesisHint(num, 19, 0, Item_In_Bag[hcItimesClass60.Position]);
							HopThanhVatPham_Table.Clear();
						}
					}
				}
				goto default;
			case 181:
				if (HopThanhVatPham_Table.Count > 0 && HopThanhVatPham_Table.ContainsKey(1))
				{
					HcItimesClass hcItimesClass70 = null;
					HcItimesClass hcItimesClass109 = null;
					if (HopThanhVatPham_Table.ContainsKey(1))
					{
						hcItimesClass70 = HopThanhVatPham_Table[1];
					}
					if (HopThanhVatPham_Table.ContainsKey(2))
					{
						hcItimesClass109 = HopThanhVatPham_Table[2];
					}
					if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass70.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass70.ItemGlobal_ID, 0) && (hcItimesClass109 == null || BitConverter.ToInt64(Item_In_Bag[hcItimesClass109.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass109.ItemGlobal_ID, 0)))
					{
						if (World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass70.VatPham_id, 0), out var value57))
						{
							if (value57.FLD_RESIDE2 != 7 && value57.FLD_RESIDE2 != 8 && value57.FLD_RESIDE2 != 10)
							{
								SynthesisHint(num, 29, 0, Item_In_Bag[hcItimesClass70.Position]);
								HopThanhVatPham_Table.Clear();
							}
							else if (value57.FLD_UP_LEVEL == 0)
							{
								SynthesisHint(num, 19, 0, Item_In_Bag[fromPos]);
								HopThanhVatPham_Table.Clear();
							}
							else if (value57.FLD_PID != 100001 && (value57.FLD_PID != 1 || value57.FLD_PID != 700001))
							{
								if (base.Player_Money < 10000000)
								{
									SynthesisHint(11, 4, 10000000, Item_In_Bag[hcItimesClass70.Position]);
									HopThanhVatPham_Table.Clear();
								}
								else
								{
									base.Player_Money -= 10000000L;
									UpdateMoneyAndWeight();
									double num40 = RNG.Next(0, 101 + value57.FLD_LEVEL);
									double num41 = 0.0;
									if (hcItimesClass109 != null)
									{
										switch (BitConverter.ToInt32(hcItimesClass109.VatPham_id, 0))
										{
										case *********:
											num41 += 5.0;
											break;
										case *********:
											num41 += 10.0;
											break;
										case 800000005:
											num41 += 15.0;
											break;
										case 1008000136:
											num41 += 25.0;
											break;
										case 800000029:
										case 1008000071:
											num41 += 20.0;
											break;
										}
									}
									if (NguyenBao_HopThanh_MoRa == 1)
									{
										num41 += 5.0;
									}
									if (base.FLD_VIP == 1)
									{
									}
									if (base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
									{
										num41 += 100.0 * base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
									}
									if (base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
									{
										num41 += 100.0 * base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
									}
									if (num41 >= num40)
									{
										int num42 = NangCap_DoTrangSuc_NhanDoTrangSuc(value57.FLD_RESIDE2, value57.FLD_PID, 1);
										if (num42 == 0)
										{
											SynthesisHint(num, 29, Phi_HopThanh, Item_In_Bag[hcItimesClass70.Position]);
											HopThanhVatPham_Table.Clear();
											goto default;
										}
										double num43 = RNG.Next(1, 100);
										int value58 = 1;
										if (num43 >= 0.0 && num43 < 10.0)
										{
											value58 = RNG.Next(2, 5);
										}
										byte[] bytes3 = BitConverter.GetBytes(num42);
										if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass70.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass70.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass70.ItemGlobal_ID, 0))
										{
											SubtractItems(hcItimesClass70.Position, 1);
											if (World.Itme.TryGetValue(num42, out var value59))
											{
												byte[] array17 = new byte[World.VatPham_ThuocTinh_KichThuoc];
												System.Buffer.BlockCopy(hcItimesClass70.VatPham_ThuocTinh, 0, array17, 0, World.VatPham_ThuocTinh_KichThuoc);
												System.Buffer.BlockCopy(BitConverter.GetBytes(value59.FLD_MAGIC1), 0, array17, 4, 4);
												System.Buffer.BlockCopy(BitConverter.GetBytes(value59.FLD_MAGIC2), 0, array17, 8, 4);
												System.Buffer.BlockCopy(BitConverter.GetBytes(value59.FLD_MAGIC3), 0, array17, 12, 4);
												System.Buffer.BlockCopy(BitConverter.GetBytes(value59.FLD_MAGIC4), 0, array17, 16, 4);
												AddItems(hcItimesClass70.ItemGlobal_ID, bytes3, hcItimesClass70.Position, BitConverter.GetBytes(value58), array17);
											}
										}
										if (hcItimesClass109 != null)
										{
											SubtractItems(hcItimesClass109.Position, 1);
										}
										SynthesisHint(num, 21, Phi_HopThanh, Item_In_Bag[hcItimesClass70.Position]);
									}
									else
									{
										int num44 = NangCap_DoTrangSuc_NhanDoTrangSuc(value57.FLD_RESIDE2, value57.FLD_PID, 1);
										if (num44 == 0)
										{
											SynthesisHint(num, 29, Phi_HopThanh, Item_In_Bag[hcItimesClass70.Position]);
											HopThanhVatPham_Table.Clear();
											goto default;
										}
										byte[] bytes4 = BitConverter.GetBytes(num44);
										if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass70.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass70.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass70.ItemGlobal_ID, 0))
										{
											SubtractItems(hcItimesClass70.Position, 1);
											if (World.Itme.TryGetValue(num44, out var value60))
											{
												byte[] array18 = new byte[World.VatPham_ThuocTinh_KichThuoc];
												System.Buffer.BlockCopy(hcItimesClass70.VatPham_ThuocTinh, 0, array18, 0, World.VatPham_ThuocTinh_KichThuoc);
												System.Buffer.BlockCopy(BitConverter.GetBytes(value60.FLD_MAGIC1), 0, array18, 4, 4);
												System.Buffer.BlockCopy(BitConverter.GetBytes(value60.FLD_MAGIC2), 0, array18, 8, 4);
												System.Buffer.BlockCopy(BitConverter.GetBytes(value60.FLD_MAGIC3), 0, array18, 12, 4);
												System.Buffer.BlockCopy(BitConverter.GetBytes(value60.FLD_MAGIC4), 0, array18, 16, 4);
												AddItems(hcItimesClass70.ItemGlobal_ID, bytes4, hcItimesClass70.Position, hcItimesClass70.VatPhamSoLuong, array18);
											}
										}
										if (hcItimesClass109 != null)
										{
											SubtractItems(hcItimesClass109.Position, 1);
										}
										SynthesisHint(num, 21, Phi_HopThanh, Item_In_Bag[hcItimesClass70.Position]);
									}
									Item_In_Bag[hcItimesClass70.Position].Khoa_Chat = false;
									HopThanhVatPham_Table.Clear();
									SynthesisSystemUnlocked();
									NguyenBao_HopThanh_MoRa = 0;
									Phi_HopThanh = 0;
								}
							}
							else
							{
								SynthesisHint(num, 29, 0, Item_In_Bag[fromPos]);
								HopThanhVatPham_Table.Clear();
							}
						}
						else
						{
							SynthesisHint(num, 29, 0, Item_In_Bag[hcItimesClass70.Position]);
							HopThanhVatPham_Table.Clear();
						}
					}
				}
				goto default;
			case 182:
				try
				{
					if (!HopThanhVatPham_Table.ContainsKey(1))
					{
						ItmeClass value55;
						if (Item_In_Bag[fromPos].Khoa_Chat)
						{
							SynthesisHint(num, 19, 0, Item_In_Bag[fromPos]);
						}
						else if (Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
						{
							SynthesisHint(num, 19, 0, Item_In_Bag[fromPos]);
						}
						else if (World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0), out value55))
						{
							if (value55.FLD_RESIDE2 != 7 && value55.FLD_RESIDE2 != 8 && value55.FLD_RESIDE2 != 10)
							{
								SynthesisHint(num, 19, 0, Item_In_Bag[fromPos]);
							}
							else if (value55.FLD_UP_LEVEL == 0)
							{
								SynthesisHint(num, 19, 0, Item_In_Bag[fromPos]);
							}
							else
							{
								int SoTien3 = (Phi_HopThanh = CalculateSyntheticEnhancementCost(value55, fromPos, num));
								HcItimesClass hcItimesClass108 = new HcItimesClass();
								hcItimesClass108.Position = fromPos;
								hcItimesClass108.VatPham = Item_In_Bag[fromPos].VatPham_byte;
								Item_In_Bag[fromPos].Khoa_Chat = true;
								HopThanhVatPham_Table.Add(1, hcItimesClass108);
								SynthesisHint(num, 1, SoTien3, Item_In_Bag[fromPos]);
							}
						}
						else
						{
							SynthesisHint(num, 19, 0, Item_In_Bag[fromPos]);
						}
					}
				}
				catch (Exception ex61)
				{
					Form1.WriteLine(1, "合成系统 首饰升级 error![" + base.Userid + "]-[" + base.UserName + "]" + ex61.Message);
				}
				goto default;
			case 183:
				try
				{
					if (HopThanhVatPham_Table.Count == 0)
					{
						SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
					}
					else if (HopThanhVatPham_Table.ContainsKey(1) && !HopThanhVatPham_Table.ContainsKey(2))
					{
						HopThanhVatPham_Table.Add(2, new HcItimesClass
						{
							Position = fromPos,
							VatPham = Item_In_Bag[fromPos].VatPham_byte
						});
						Item_In_Bag[fromPos].Khoa_Chat = true;
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex63)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成3 error![" + base.Userid + "]-[" + base.UserName + "]" + ex63.Message);
				}
				goto default;
			case 190:
				if (HopThanhVatPham_Table.Count > 0)
				{
					HcItimesClass hcItimesClass38 = null;
					if (HopThanhVatPham_Table.ContainsKey(1))
					{
						HcItimesClass hcItimesClass39 = HopThanhVatPham_Table[1];
						if (HopThanhVatPham_Table.ContainsKey(2))
						{
							HcItimesClass hcItimesClass40 = HopThanhVatPham_Table[2];
							if (HopThanhVatPham_Table.ContainsKey(3))
							{
								hcItimesClass38 = HopThanhVatPham_Table[3];
							}
							if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass39.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass39.ItemGlobal_ID, 0) && BitConverter.ToInt64(Item_In_Bag[hcItimesClass40.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass40.ItemGlobal_ID, 0) && (hcItimesClass38 == null || BitConverter.ToInt64(Item_In_Bag[hcItimesClass38.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass38.ItemGlobal_ID, 0)))
							{
								if (Item_In_Bag[hcItimesClass39.Position].Vat_Pham_Khoa_Lai)
								{
									SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass39.Position]);
								}
								else
								{
									double num82 = RNG.Next(1, 100);
									double num84 = RNG.Next(40, 90);
									if (hcItimesClass38 != null)
									{
										num82 += (double)LuckyCharmBonus(BitConverter.ToInt32(hcItimesClass38.VatPham_id, 0));
									}
									if (NguyenBao_HopThanh_MoRa == 1)
									{
										num82 += 5.0;
									}
									if (base.FLD_VIP == 1)
									{
									}
									if (World.TyLe_HopThanh != 0.0)
									{
										num82 += 100.0 * World.TyLe_HopThanh;
									}
									if (base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
									{
										num82 += 100.0 * base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
									}
									if (base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
									{
										num82 += 100.0 * base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
									}
									if (num82 >= num84)
									{
										hcItimesClass39.FLD_FJ_TrungCapPhuHon = 0;
										SubtractItems(hcItimesClass39.Position, 1);
										AddItems(hcItimesClass39.ItemGlobal_ID, hcItimesClass39.VatPham_id, hcItimesClass39.Position, hcItimesClass39.VatPhamSoLuong, hcItimesClass39.VatPham_ThuocTinh);
										if (hcItimesClass40 != null)
										{
											SubtractItems(hcItimesClass40.Position, 1);
										}
										if (hcItimesClass38 != null)
										{
											SubtractItems(hcItimesClass38.Position, 1);
										}
										Item_In_Bag[hcItimesClass39.Position].Khoa_Chat = false;
										HopThanhVatPham_Table.Clear();
										SynthesisSystemUnlocked();
										NguyenBao_HopThanh_MoRa = 0;
										RxjhClass.SyntheticRecord(base.Userid, base.UserName, Item_In_Bag[hcItimesClass39.Position].DatDuocVatPhamTen_XungHao(), num, "Soul Decomposition", "Thanh Cong", Item_In_Bag[hcItimesClass39.Position]);
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
									}
									else
									{
										if (hcItimesClass40 != null)
										{
											SubtractItems(hcItimesClass40.Position, 1);
										}
										if (hcItimesClass38 != null)
										{
											SubtractItems(hcItimesClass38.Position, 1);
										}
										Item_In_Bag[hcItimesClass39.Position].Khoa_Chat = false;
										HopThanhVatPham_Table.Clear();
										SynthesisSystemUnlocked();
										NguyenBao_HopThanh_MoRa = 0;
										RxjhClass.SyntheticRecord(base.Userid, base.UserName, Item_In_Bag[hcItimesClass39.Position].DatDuocVatPhamTen_XungHao(), num, "Soul Decomposition", "That Bai", Item_In_Bag[hcItimesClass39.Position]);
										SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
									}
								}
							}
						}
					}
				}
				goto default;
			case 191:
				try
				{
					if (!HopThanhVatPham_Table.ContainsKey(1) && !Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai && Item_In_Bag[fromPos].FLD_FJ_TrungCapPhuHon != 0)
					{
						HcItimesClass hcItimesClass46 = new HcItimesClass();
						hcItimesClass46.Position = fromPos;
						hcItimesClass46.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(1, hcItimesClass46);
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex7)
				{
					Form1.WriteLine(1, "合成系统 附魂分解 合成1 error![" + base.Userid + "]-[" + base.UserName + "]" + ex7.Message);
				}
				goto default;
			case 192:
				try
				{
					if (!HopThanhVatPham_Table.ContainsKey(2) && BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) == 1000000780)
					{
						HcItimesClass hcItimesClass36 = new HcItimesClass();
						hcItimesClass36.Position = fromPos;
						hcItimesClass36.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(2, hcItimesClass36);
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex4)
				{
					Form1.WriteLine(1, "合成系统 附魂分解 合成2 error![" + base.Userid + "]-[" + base.UserName + "]" + ex4.Message);
				}
				goto default;
			case 193:
				try
				{
					if (HopThanhVatPham_Table.Count != 0 && !HopThanhVatPham_Table.ContainsKey(3))
					{
						HcItimesClass hcItimesClass48 = new HcItimesClass();
						hcItimesClass48.Position = fromPos;
						hcItimesClass48.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(3, hcItimesClass48);
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex8)
				{
					Form1.WriteLine(1, "合成系统 附魂分解 合成3 error![" + base.Userid + "]-[" + base.UserName + "]" + ex8.Message);
				}
				goto default;
			case 199:
				try
				{
					HopThanhVatPham_Table.Clear();
					SynthesisSystemUnlocked();
					NguyenBao_HopThanh_MoRa = 0;
					OpenWarehouse = false;
					SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
				}
				catch (Exception ex117)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 附魂分解 合成4 error![" + base.Userid + "]-[" + base.UserName + "]" + ex117.Message);
				}
				goto default;
			case 200:
			{
				int num80 = 0;
				int numx4 = 0;
				try
				{
					HcItimesClass hcItimesClass32;
					HcItimesClass value4;
					HcItimesClass value6;
					HcItimesClass hcItimesClass33;
					ItmeClass value7;
					if (base.CurrentOperationType == 148)
					{
						if (HopThanhVatPham_Table.ContainsKey(1))
						{
							hcItimesClass32 = HopThanhVatPham_Table[1];
							hcItimesClass32.DatDuocThuocTinh();
							hcItimesClass32.CuongHoaThuocTinhGiaiDoan();
							num80 = 1;
							value4 = null;
							value6 = null;
							hcItimesClass33 = null;
							int num_value4 = Item_In_Bag[hcItimesClass32.Position].FLD_MAGIC0;
							numx4 = num_value4;
							if (HopThanhVatPham_Table.ContainsKey(4))
							{
								hcItimesClass33 = HopThanhVatPham_Table[4];
							}
							num80 = 2;
							if (HopThanhVatPham_Table.TryGetValue(3, out value6))
							{
								if (BitConverter.ToInt32(value6.VatPhamSoLuong, 0) < 1)
								{
									SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
								}
								else
								{
									num80 = 3;
									if (World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass32.VatPham_id, 0), out value7))
									{
										if (value7.FLD_RESIDE2 == 7 || value7.FLD_RESIDE2 == 8 || value7.FLD_RESIDE2 == 10)
										{
											if (value7.FLD_LEVEL >= 110)
											{
												if (HopThanhVatPham_Table.TryGetValue(2, out value4))
												{
													if (BitConverter.ToInt32(value4.VatPhamSoLuong, 0) >= 4)
													{
														goto IL_151d1;
													}
													SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
												}
												else
												{
													SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
												}
											}
											else if (value7.FLD_LEVEL >= 100)
											{
												if (HopThanhVatPham_Table.TryGetValue(2, out value4))
												{
													if (BitConverter.ToInt32(value4.VatPhamSoLuong, 0) >= 3)
													{
														goto IL_151d1;
													}
													SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
												}
												else
												{
													SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
												}
											}
											else if (value7.FLD_LEVEL >= 80)
											{
												if (HopThanhVatPham_Table.TryGetValue(2, out value4))
												{
													if (BitConverter.ToInt32(value4.VatPhamSoLuong, 0) >= 2)
													{
														goto IL_151d1;
													}
													SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
												}
												else
												{
													SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
												}
											}
											else if (value7.FLD_LEVEL >= 60)
											{
												if (HopThanhVatPham_Table.TryGetValue(2, out value4))
												{
													if (BitConverter.ToInt32(value4.VatPhamSoLuong, 0) >= 1)
													{
														goto IL_151d1;
													}
													SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
												}
												else
												{
													SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
												}
											}
										}
									}
									else
									{
										SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
									}
								}
							}
							else
							{
								SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
							}
						}
						else
						{
							SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
						}
					}
					goto end_IL_14d60;
					IL_151d1:
					num80 = 4;
					if (Item_In_Bag[hcItimesClass32.Position].Vat_Pham_Khoa_Lai)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass32.Position]);
					}
					else
					{
						num80 = 5;
						Random random15 = new Random();
						string[] array16 = World.TongXacSuat_TrangSuc.Split(';');
						double num81 = random15.Next(int.Parse(array16[0]), int.Parse(array16[1]));
						if (hcItimesClass32.CuongHoaSoLuong >= 10)
						{
							HopThanhVatPham_Table.Clear();
							SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
						}
						else
						{
							num80 = 6;
							string phuchts = "";
							if (World.TyLe_GiaCong_TrangSuc != 0.0)
							{
								num81 += 100.0 * World.TyLe_GiaCong_TrangSuc;
							}
							if ((hcItimesClass32.CuongHoaSoLuong == 0 && num81 >= 100.0 - World.CuongHoaTrangSuc_1) || (hcItimesClass32.CuongHoaSoLuong == 1 && num81 >= 100.0 - World.CuongHoaTrangSuc_2) || (hcItimesClass32.CuongHoaSoLuong == 2 && num81 >= 100.0 - World.CuongHoaTrangSuc_3) || (hcItimesClass32.CuongHoaSoLuong == 3 && num81 >= 100.0 - World.CuongHoaTrangSuc_4) || (hcItimesClass32.CuongHoaSoLuong == 4 && num81 >= 100.0 - World.CuongHoaTrangSuc_5) || (hcItimesClass32.CuongHoaSoLuong == 5 && num81 >= 100.0 - World.CuongHoaTrangSuc_6) || (hcItimesClass32.CuongHoaSoLuong == 6 && num81 >= 100.0 - World.CuongHoaTrangSuc_7) || (hcItimesClass32.CuongHoaSoLuong == 7 && num81 >= 100.0 - World.CuongHoaTrangSuc_8) || (hcItimesClass32.CuongHoaSoLuong == 8 && num81 >= 100.0 - World.CuongHoaTrangSuc_9) || (hcItimesClass32.CuongHoaSoLuong == 9 && num81 >= 100.0 - World.CuongHoaTrangSuc_10) || (hcItimesClass32.CuongHoaSoLuong == 10 && num81 >= 100.0 - World.CuongHoaTrangSuc_10))
							{
								if (value7.FLD_RESIDE2 == 7)
								{
									hcItimesClass32.CuongHoaLoaiHinh = 2;
								}
								else if (value7.FLD_RESIDE2 == 8)
								{
									hcItimesClass32.CuongHoaLoaiHinh = 3;
								}
								else if (value7.FLD_RESIDE2 == 10)
								{
									hcItimesClass32.CuongHoaLoaiHinh = 1;
								}
								if (hcItimesClass33 == null)
								{
									HcItimesClass hcItimesClass156 = hcItimesClass32;
									int giaiDoanSoLuong = hcItimesClass156.CuongHoaSoLuong + 1;
									hcItimesClass156.CuongHoaSoLuong = giaiDoanSoLuong;
								}
								else if (BitConverter.ToInt32(hcItimesClass33.VatPham_id, 0) == 1008000072)
								{
									phuchts = "1008000072";
									Random randomz = new Random(DateTime.Now.Millisecond);
									int random19 = randomz.Next(1, 100);
									if (random19 > 50)
									{
										hcItimesClass32.CuongHoaSoLuong += 2;
									}
									else if (random19 > 80)
									{
										hcItimesClass32.CuongHoaSoLuong += 3;
									}
									else
									{
										hcItimesClass32.CuongHoaSoLuong++;
									}
									if (hcItimesClass32.CuongHoaSoLuong > 10)
									{
										hcItimesClass32.CuongHoaSoLuong = 10;
									}
								}
								else if (BitConverter.ToInt32(hcItimesClass33.VatPham_id, 0) == 1000000619)
								{
									phuchts = "1000000619";
									HcItimesClass hcItimesClass156 = hcItimesClass32;
									int giaiDoanSoLuong = hcItimesClass156.CuongHoaSoLuong + 1;
									hcItimesClass156.CuongHoaSoLuong = giaiDoanSoLuong;
								}
								if (hcItimesClass32.CuongHoaSoLuong > 10)
								{
									hcItimesClass32.CuongHoaSoLuong = 10;
								}
								if (hcItimesClass32.CuongHoaSoLuong >= 5)
								{
									SendNewsletter(BitConverter.ToInt32(hcItimesClass32.VatPham_id, 0), base.UserName, hcItimesClass32.CuongHoaSoLuong, base.Player_Zx);
								}
								num80 = 7;
								hcItimesClass32.ThietLap_GiaiDoanThuocTinh();
								num80 = 8;
								if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass32.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass32.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass32.ItemGlobal_ID, 0))
								{
									SubtractItems(hcItimesClass32.Position, 1);
									AddItems(hcItimesClass32.ItemGlobal_ID, hcItimesClass32.VatPham_id, hcItimesClass32.Position, hcItimesClass32.VatPhamSoLuong, hcItimesClass32.VatPham_ThuocTinh);
								}
								num80 = 9;
								Item_In_Bag[hcItimesClass32.Position].Khoa_Chat = false;
								RxjhClass.SyntheticRecord(base.Userid, base.UserName, value7.ItmeNAME, num, "CH-TrangSuc" + phuchts, "Thanh Cong", Item_In_Bag[hcItimesClass32.Position], Item_In_Bag[hcItimesClass32.Position].FLD_CuongHoaSoLuong, numx4);
								SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
							}
							else if (hcItimesClass33 != null)
							{
								if (BitConverter.ToInt32(hcItimesClass33.VatPham_id, 0) == 1000000619)
								{
									if (hcItimesClass32.CuongHoaSoLuong <= 2)
									{
										if (value7.FLD_RESIDE2 == 7)
										{
											hcItimesClass32.CuongHoaLoaiHinh = 2;
										}
										else if (value7.FLD_RESIDE2 == 8)
										{
											hcItimesClass32.CuongHoaLoaiHinh = 3;
										}
										else if (value7.FLD_RESIDE2 == 10)
										{
											hcItimesClass32.CuongHoaLoaiHinh = 1;
										}
										HcItimesClass hcItimesClass156 = hcItimesClass32;
										int giaiDoanSoLuong = hcItimesClass156.CuongHoaSoLuong + 1;
										hcItimesClass156.CuongHoaSoLuong = giaiDoanSoLuong;
										if (hcItimesClass32.CuongHoaSoLuong > 10)
										{
											hcItimesClass32.CuongHoaSoLuong = 10;
										}
										hcItimesClass32.ThietLap_GiaiDoanThuocTinh();
										RxjhClass.SyntheticRecord(base.Userid, base.UserName, value7.ItmeNAME, num, "CH-TrangSuc 1000000619", "Thanh Cong", Item_In_Bag[hcItimesClass32.Position], Item_In_Bag[hcItimesClass32.Position].FLD_CuongHoaSoLuong, numx4);
										num80 = 10;
										if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass32.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass32.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass32.ItemGlobal_ID, 0))
										{
											SubtractItems(hcItimesClass32.Position, 1);
											AddItems(hcItimesClass32.ItemGlobal_ID, hcItimesClass32.VatPham_id, hcItimesClass32.Position, hcItimesClass32.VatPhamSoLuong, hcItimesClass32.VatPham_ThuocTinh);
										}
										num80 = 11;
										Item_In_Bag[hcItimesClass32.Position].Khoa_Chat = false;
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
									}
									else
									{
										num80 = 12;
										RxjhClass.SyntheticRecord(base.Userid, base.UserName, value7.ItmeNAME, num, "CH-TrangSuc 1000000619", "That Bai", Item_In_Bag[hcItimesClass32.Position], -1, numx4);
										SubtractItems(hcItimesClass32.Position, 1);
										SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
									}
								}
								else if (BitConverter.ToInt32(hcItimesClass33.VatPham_id, 0) == 1008000072)
								{
									hcItimesClass32.CuongHoaSoLuong = 0;
									hcItimesClass32.ThietLap_GiaiDoanThuocTinh();
									RxjhClass.SyntheticRecord(base.Userid, base.UserName, value7.ItmeNAME, num, "CH-TrangSuc 1008000072", "That Bai", Item_In_Bag[hcItimesClass32.Position], Item_In_Bag[hcItimesClass32.Position].FLD_CuongHoaSoLuong, numx4);
									SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
									Init_Item_In_Bag();
								}
								else
								{
									num80 = 13;
									RxjhClass.SyntheticRecord(base.Userid, base.UserName, value7.ItmeNAME, num, "CH-TrangSuc", "That Bai", Item_In_Bag[hcItimesClass32.Position], -1, numx4);
									SubtractItems(hcItimesClass32.Position, 1);
									SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
								}
							}
							else
							{
								num80 = 14;
								RxjhClass.SyntheticRecord(base.Userid, base.UserName, value7.ItmeNAME, num, "CH-TrangSuc", "That Bai", Item_In_Bag[hcItimesClass32.Position], -1, numx4);
								SubtractItems(hcItimesClass32.Position, 1);
								SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
							}
							num80 = 15;
							if (value4 != null)
							{
								num80 = 16;
								if (value7.FLD_LEVEL >= 110)
								{
									SubtractItems(value4.Position, 4);
								}
								else if (value7.FLD_LEVEL >= 100)
								{
									SubtractItems(value4.Position, 3);
								}
								else if (value7.FLD_LEVEL >= 80)
								{
									SubtractItems(value4.Position, 2);
								}
								else if (value7.FLD_LEVEL >= 60)
								{
									SubtractItems(value4.Position, 1);
								}
								num80 = 17;
							}
							num80 = 18;
							if (value6 != null)
							{
								num80 = 19;
								if (value7.FLD_LEVEL >= 110)
								{
									SubtractItems(value6.Position, 4);
								}
								else if (value7.FLD_LEVEL >= 100)
								{
									SubtractItems(value6.Position, 3);
								}
								else if (value7.FLD_LEVEL >= 80)
								{
									SubtractItems(value6.Position, 2);
								}
								else if (value7.FLD_LEVEL >= 60)
								{
									SubtractItems(value6.Position, 1);
								}
							}
							num80 = 20;
							if (hcItimesClass33 != null)
							{
								num80 = 21;
								SubtractItems(hcItimesClass33.Position, 1);
							}
							num80 = 22;
							HopThanhVatPham_Table.Clear();
							SynthesisSystemUnlocked();
							NguyenBao_HopThanh_MoRa = 0;
						}
					}
					end_IL_14d60:;
				}
				catch (Exception ex113)
				{
					string[] obj5 = new string[8]
					{
						"200首饰加工 error ",
						base.Client.WorldId.ToString(),
						"|",
						base.Client.ToString(),
						" ",
						num80.ToString(),
						" ",
						null
					};
					obj5[7] = ex113?.ToString();
					Form1.WriteLine(1, string.Concat(obj5));
				}
				goto default;
			}
			case 201:
				if (base.CurrentOperationType == 148 && !HopThanhVatPham_Table.ContainsKey(1))
				{
					ItmeClass value85;
					if (Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
					{
						SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
					}
					else if (Item_In_Bag[fromPos].FLD_CuongHoaSoLuong >= 10)
					{
						SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
					}
					else if (World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0), out value85) && (value85.FLD_RESIDE2 == 7 || value85.FLD_RESIDE2 == 8 || value85.FLD_RESIDE2 == 10) && value85.FLD_LEVEL >= 60)
					{
						if (value85.FLD_LEVEL >= 100)
						{
							SynthesisHint(num, 121, 0, Item_In_Bag[fromPos]);
							HcItimesClass hcItimesClass11 = new HcItimesClass();
							hcItimesClass11.Position = fromPos;
							hcItimesClass11.VatPham = Item_In_Bag[fromPos].VatPham_byte;
							Item_In_Bag[fromPos].Khoa_Chat = true;
							HopThanhVatPham_Table.Add(1, hcItimesClass11);
						}
						else if (value85.FLD_LEVEL >= 80)
						{
							SynthesisHint(num, 111, 0, Item_In_Bag[fromPos]);
							HcItimesClass hcItimesClass12 = new HcItimesClass();
							hcItimesClass12.Position = fromPos;
							hcItimesClass12.VatPham = Item_In_Bag[fromPos].VatPham_byte;
							Item_In_Bag[fromPos].Khoa_Chat = true;
							HopThanhVatPham_Table.Add(1, hcItimesClass12);
						}
						else if (value85.FLD_LEVEL >= 60)
						{
							SynthesisHint(num, 101, 0, Item_In_Bag[fromPos]);
							HcItimesClass hcItimesClass13 = new HcItimesClass();
							hcItimesClass13.Position = fromPos;
							hcItimesClass13.VatPham = Item_In_Bag[fromPos].VatPham_byte;
							Item_In_Bag[fromPos].Khoa_Chat = true;
							HopThanhVatPham_Table.Add(1, hcItimesClass13);
						}
					}
				}
				goto default;
			case 202:
				if (base.CurrentOperationType == 148 && !HopThanhVatPham_Table.ContainsKey(2))
				{
					if (HopThanhVatPham_Table.ContainsKey(1))
					{
						HcItimesClass hcItimesClass132 = HopThanhVatPham_Table[1];
						if (World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass132.VatPham_id, 0), out var value71))
						{
							if (value71.FLD_LEVEL >= 110)
							{
								if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 1000000640)
								{
									SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
								}
								else if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) < 4)
								{
									SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
								}
								else
								{
									SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
									SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
									SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
									SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
									HcItimesClass hcItimesClass133 = new HcItimesClass();
									hcItimesClass133.Position = fromPos;
									hcItimesClass133.VatPham = Item_In_Bag[fromPos].VatPham_byte;
									Item_In_Bag[fromPos].Khoa_Chat = true;
									if (!HopThanhVatPham_Table.ContainsKey(2))
									{
										HopThanhVatPham_Table.Add(2, hcItimesClass133);
									}
								}
							}
							else if (value71.FLD_LEVEL >= 100)
							{
								if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 1000000640)
								{
									SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
								}
								else if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) < 3)
								{
									SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
								}
								else
								{
									SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
									SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
									SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
									HcItimesClass hcItimesClass134 = new HcItimesClass();
									hcItimesClass134.Position = fromPos;
									hcItimesClass134.VatPham = Item_In_Bag[fromPos].VatPham_byte;
									Item_In_Bag[fromPos].Khoa_Chat = true;
									if (!HopThanhVatPham_Table.ContainsKey(2))
									{
										HopThanhVatPham_Table.Add(2, hcItimesClass134);
									}
								}
							}
							else if (value71.FLD_LEVEL >= 80)
							{
								if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 1000000640)
								{
									SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
								}
								else if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) < 2)
								{
									SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
								}
								else
								{
									SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
									SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
									HcItimesClass hcItimesClass135 = new HcItimesClass();
									hcItimesClass135.Position = fromPos;
									hcItimesClass135.VatPham = Item_In_Bag[fromPos].VatPham_byte;
									Item_In_Bag[fromPos].Khoa_Chat = true;
									if (!HopThanhVatPham_Table.ContainsKey(2))
									{
										HopThanhVatPham_Table.Add(2, hcItimesClass135);
									}
								}
							}
							else if (value71.FLD_LEVEL >= 60)
							{
								if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 1000000640)
								{
									SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
								}
								else if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) < 1)
								{
									SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
								}
								else
								{
									SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
									HcItimesClass hcItimesClass136 = new HcItimesClass();
									hcItimesClass136.Position = fromPos;
									hcItimesClass136.VatPham = Item_In_Bag[fromPos].VatPham_byte;
									Item_In_Bag[fromPos].Khoa_Chat = true;
									if (!HopThanhVatPham_Table.ContainsKey(2))
									{
										HopThanhVatPham_Table.Add(2, hcItimesClass136);
									}
								}
							}
						}
						else
						{
							SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
						}
					}
					else
					{
						SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
					}
				}
				goto default;
			case 203:
				if (base.CurrentOperationType == 148 && !HopThanhVatPham_Table.ContainsKey(3))
				{
					if (HopThanhVatPham_Table.ContainsKey(1))
					{
						HcItimesClass hcItimesClass94 = HopThanhVatPham_Table[1];
						if (World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass94.VatPham_id, 0), out var value49))
						{
							if (value49.FLD_RESIDE2 == 7)
							{
								if (value49.FLD_LEVEL >= 110)
								{
									if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 1000000638)
									{
										SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
									}
									else if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) < 4)
									{
										SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
									}
									else
									{
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										HcItimesClass hcItimesClass54x = new HcItimesClass();
										hcItimesClass54x.Position = fromPos;
										hcItimesClass54x.VatPham = Item_In_Bag[fromPos].VatPham_byte;
										Item_In_Bag[fromPos].Khoa_Chat = true;
										if (!HopThanhVatPham_Table.ContainsKey(3))
										{
											HopThanhVatPham_Table.Add(3, hcItimesClass54x);
										}
									}
								}
								else if (value49.FLD_LEVEL >= 100)
								{
									if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 1000000638)
									{
										SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
									}
									else if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) < 3)
									{
										SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
									}
									else
									{
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										HcItimesClass hcItimesClass54x4 = new HcItimesClass();
										hcItimesClass54x4.Position = fromPos;
										hcItimesClass54x4.VatPham = Item_In_Bag[fromPos].VatPham_byte;
										Item_In_Bag[fromPos].Khoa_Chat = true;
										if (!HopThanhVatPham_Table.ContainsKey(3))
										{
											HopThanhVatPham_Table.Add(3, hcItimesClass54x4);
										}
									}
								}
								else if (value49.FLD_LEVEL >= 80)
								{
									if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 1000000638)
									{
										SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
									}
									else if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) < 2)
									{
										SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
									}
									else
									{
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										HcItimesClass hcItimesClass54x7 = new HcItimesClass();
										hcItimesClass54x7.Position = fromPos;
										hcItimesClass54x7.VatPham = Item_In_Bag[fromPos].VatPham_byte;
										Item_In_Bag[fromPos].Khoa_Chat = true;
										if (!HopThanhVatPham_Table.ContainsKey(3))
										{
											HopThanhVatPham_Table.Add(3, hcItimesClass54x7);
										}
									}
								}
								else if (value49.FLD_LEVEL >= 60)
								{
									if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 1000000638)
									{
										SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
									}
									else if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) < 1)
									{
										SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
									}
									else
									{
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										HcItimesClass hcItimesClass54x10 = new HcItimesClass();
										hcItimesClass54x10.Position = fromPos;
										hcItimesClass54x10.VatPham = Item_In_Bag[fromPos].VatPham_byte;
										Item_In_Bag[fromPos].Khoa_Chat = true;
										if (!HopThanhVatPham_Table.ContainsKey(3))
										{
											HopThanhVatPham_Table.Add(3, hcItimesClass54x10);
										}
									}
								}
							}
							else if (value49.FLD_RESIDE2 == 8)
							{
								if (value49.FLD_LEVEL >= 110)
								{
									if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 1000000637)
									{
										SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
									}
									else if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) < 4)
									{
										SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
									}
									else
									{
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										HcItimesClass hcItimesClass54x2 = new HcItimesClass();
										hcItimesClass54x2.Position = fromPos;
										hcItimesClass54x2.VatPham = Item_In_Bag[fromPos].VatPham_byte;
										Item_In_Bag[fromPos].Khoa_Chat = true;
										if (!HopThanhVatPham_Table.ContainsKey(3))
										{
											HopThanhVatPham_Table.Add(3, hcItimesClass54x2);
										}
									}
								}
								else if (value49.FLD_LEVEL >= 100)
								{
									if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 1000000637)
									{
										SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
									}
									else if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) < 3)
									{
										SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
									}
									else
									{
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										HcItimesClass hcItimesClass54x5 = new HcItimesClass();
										hcItimesClass54x5.Position = fromPos;
										hcItimesClass54x5.VatPham = Item_In_Bag[fromPos].VatPham_byte;
										Item_In_Bag[fromPos].Khoa_Chat = true;
										if (!HopThanhVatPham_Table.ContainsKey(3))
										{
											HopThanhVatPham_Table.Add(3, hcItimesClass54x5);
										}
									}
								}
								else if (value49.FLD_LEVEL >= 80)
								{
									if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 1000000637)
									{
										SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
									}
									else if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) < 2)
									{
										SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
									}
									else
									{
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										HcItimesClass hcItimesClass54x8 = new HcItimesClass();
										hcItimesClass54x8.Position = fromPos;
										hcItimesClass54x8.VatPham = Item_In_Bag[fromPos].VatPham_byte;
										Item_In_Bag[fromPos].Khoa_Chat = true;
										if (!HopThanhVatPham_Table.ContainsKey(3))
										{
											HopThanhVatPham_Table.Add(3, hcItimesClass54x8);
										}
									}
								}
								else if (value49.FLD_LEVEL >= 60)
								{
									if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 1000000637)
									{
										SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
									}
									else if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) < 1)
									{
										SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
									}
									else
									{
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										HcItimesClass hcItimesClass54x11 = new HcItimesClass();
										hcItimesClass54x11.Position = fromPos;
										hcItimesClass54x11.VatPham = Item_In_Bag[fromPos].VatPham_byte;
										Item_In_Bag[fromPos].Khoa_Chat = true;
										if (!HopThanhVatPham_Table.ContainsKey(3))
										{
											HopThanhVatPham_Table.Add(3, hcItimesClass54x11);
										}
									}
								}
							}
							else if (value49.FLD_RESIDE2 == 10)
							{
								if (value49.FLD_LEVEL >= 110)
								{
									if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 1000000639)
									{
										SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
									}
									else if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) < 4)
									{
										SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
									}
									else
									{
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										HcItimesClass hcItimesClass54x3 = new HcItimesClass();
										hcItimesClass54x3.Position = fromPos;
										hcItimesClass54x3.VatPham = Item_In_Bag[fromPos].VatPham_byte;
										Item_In_Bag[fromPos].Khoa_Chat = true;
										if (!HopThanhVatPham_Table.ContainsKey(3))
										{
											HopThanhVatPham_Table.Add(3, hcItimesClass54x3);
										}
									}
								}
								else if (value49.FLD_LEVEL >= 100)
								{
									if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 1000000639)
									{
										SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
									}
									else if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) < 3)
									{
										SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
									}
									else
									{
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										HcItimesClass hcItimesClass54x6 = new HcItimesClass();
										hcItimesClass54x6.Position = fromPos;
										hcItimesClass54x6.VatPham = Item_In_Bag[fromPos].VatPham_byte;
										Item_In_Bag[fromPos].Khoa_Chat = true;
										if (!HopThanhVatPham_Table.ContainsKey(3))
										{
											HopThanhVatPham_Table.Add(3, hcItimesClass54x6);
										}
									}
								}
								else if (value49.FLD_LEVEL >= 80)
								{
									if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 1000000639)
									{
										SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
									}
									else if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) < 2)
									{
										SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
									}
									else
									{
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										HcItimesClass hcItimesClass54x9 = new HcItimesClass();
										hcItimesClass54x9.Position = fromPos;
										hcItimesClass54x9.VatPham = Item_In_Bag[fromPos].VatPham_byte;
										Item_In_Bag[fromPos].Khoa_Chat = true;
										if (!HopThanhVatPham_Table.ContainsKey(3))
										{
											HopThanhVatPham_Table.Add(3, hcItimesClass54x9);
										}
									}
								}
								else if (value49.FLD_LEVEL >= 60)
								{
									if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 1000000639)
									{
										SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
									}
									else if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) < 1)
									{
										SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
									}
									else
									{
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										HcItimesClass hcItimesClass54x12 = new HcItimesClass();
										hcItimesClass54x12.Position = fromPos;
										hcItimesClass54x12.VatPham = Item_In_Bag[fromPos].VatPham_byte;
										Item_In_Bag[fromPos].Khoa_Chat = true;
										if (!HopThanhVatPham_Table.ContainsKey(3))
										{
											HopThanhVatPham_Table.Add(3, hcItimesClass54x12);
										}
									}
								}
							}
						}
						else
						{
							SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
						}
					}
					else
					{
						SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
					}
				}
				goto default;
			case 204:
				if (base.CurrentOperationType == 148 && !HopThanhVatPham_Table.ContainsKey(4))
				{
					if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 1008000072 && BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 1000000619)
					{
						SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
					}
					else if (HopThanhVatPham_Table.ContainsKey(1))
					{
						HcItimesClass hcItimesClass53 = HopThanhVatPham_Table[1];
						hcItimesClass53.DatDuocThuocTinh();
						hcItimesClass53.CuongHoaThuocTinhGiaiDoan();
						if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) == 1000000619 && hcItimesClass53.CuongHoaSoLuong > 2)
						{
							SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
						}
						else
						{
							HcItimesClass hcItimesClass54 = new HcItimesClass();
							hcItimesClass54.Position = fromPos;
							hcItimesClass54.VatPham = Item_In_Bag[fromPos].VatPham_byte;
							Item_In_Bag[fromPos].Khoa_Chat = true;
							if (!HopThanhVatPham_Table.ContainsKey(4))
							{
								HopThanhVatPham_Table.Add(4, hcItimesClass54);
							}
							SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
						}
					}
					else
					{
						SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
					}
				}
				goto default;
			case 205:
				try
				{
					if (HopThanhVatPham_Table.TryGetValue(1, out var value22))
					{
						Item_In_Bag[value22.Position].Khoa_Chat = false;
						HopThanhVatPham_Table.Clear();
						SynthesisSystemUnlocked();
					}
					NguyenBao_HopThanh_MoRa = 0;
					SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
				}
				catch (Exception ex25)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 首饰加工 205 error![" + base.Userid + "]-[" + base.UserName + "]" + ex25.Message);
				}
				goto default;
			case 206:
				try
				{
					if (base.CurrentOperationType == 149 && !HopThanhVatPham_Table.ContainsKey(1))
					{
						ItmeClass value33;
						if (Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
						}
						else if (World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0), out value33))
						{
							if (value33.FLD_RESIDE2 != 7 && value33.FLD_RESIDE2 != 8 && value33.FLD_RESIDE2 != 10)
							{
								SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
							}
							else if (value33.FLD_LEVEL < 60)
							{
								SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
							}
							else
							{
								HcItimesClass hcItimesClass72 = new HcItimesClass();
								hcItimesClass72.Position = fromPos;
								hcItimesClass72.VatPham = Item_In_Bag[fromPos].VatPham_byte;
								Item_In_Bag[fromPos].Khoa_Chat = true;
								HopThanhVatPham_Table.Add(1, hcItimesClass72);
								SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
							}
						}
						else
						{
							SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
						}
					}
				}
				catch (Exception ex37)
				{
					string[] obj4 = new string[6]
					{
						"206首饰分解 error",
						base.Client.WorldId.ToString(),
						"|",
						base.Client.ToString(),
						" ",
						null
					};
					obj4[5] = ex37?.ToString();
					Form1.WriteLine(1, string.Concat(obj4));
				}
				goto default;
			case 207:
				try
				{
					if (base.CurrentOperationType == 149 && HopThanhVatPham_Table.Count > 0)
					{
						if (HopThanhVatPham_Table.ContainsKey(1))
						{
							HcItimesClass hcItimesClass52 = HopThanhVatPham_Table[1];
							if (World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass52.VatPham_id, 0), out var value17))
							{
								int value18 = 1;
								int value19z = 1;
								int value20 = 1000000640;
								int value21 = 0;
								if (GetParcelVacancyNumber() < 2)
								{
									SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
								}
								else
								{
									if (value17.FLD_LEVEL >= 115)
									{
										value18 = RNG.Next(16, 20);
									}
									else if (value17.FLD_LEVEL >= 100)
									{
										value18 = RNG.Next(11, 15);
									}
									else if (value17.FLD_LEVEL >= 80)
									{
										value18 = RNG.Next(6, 10);
									}
									else if (value17.FLD_LEVEL >= 60)
									{
										value18 = RNG.Next(1, 5);
									}
									int num10 = GetParcelVacancy(this);
									if (num10 != -1)
									{
										if (value17.FLD_RESIDE2 == 7)
										{
											value21 = 1000000638;
										}
										else if (value17.FLD_RESIDE2 == 8)
										{
											value21 = 1000000637;
										}
										else if (value17.FLD_RESIDE2 == 10)
										{
											value21 = 1000000639;
										}
										if (value17.FLD_LEVEL >= 115)
										{
											value19z = RNG.Next(5, 6);
										}
										else if (value17.FLD_LEVEL >= 100)
										{
											value19z = RNG.Next(4, 5);
										}
										else if (value17.FLD_LEVEL >= 80)
										{
											value19z = RNG.Next(2, 3);
										}
										else if (value17.FLD_LEVEL >= 60)
										{
											value19z = RNG.Next(1, 1);
										}
										SubtractItems(hcItimesClass52.Position, 1);
										IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(value20), num10, BitConverter.GetBytes(value18), new byte[56]);
										int Position2 = GetParcelVacancy(this);
										IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(value21), Position2, BitConverter.GetBytes(value19z), new byte[56]);
										SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
										HopThanhVatPham_Table.Clear();
										SynthesisSystemUnlocked();
										NguyenBao_HopThanh_MoRa = 0;
									}
									else
									{
										SynthesisHint(num, 8, 0, Item_In_Bag[fromPos]);
									}
								}
							}
						}
						else
						{
							SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
						}
					}
				}
				catch (Exception ex24)
				{
					string[] obj3 = new string[6]
					{
						"206首饰分解 error",
						base.Client.WorldId.ToString(),
						"|",
						base.Client.ToString(),
						" ",
						null
					};
					obj3[5] = ex24?.ToString();
					Form1.WriteLine(1, string.Concat(obj3));
				}
				goto default;
			case 208:
				try
				{
					if (HopThanhVatPham_Table.TryGetValue(1, out var value16))
					{
						Item_In_Bag[value16.Position].Khoa_Chat = false;
						HopThanhVatPham_Table.Clear();
						SynthesisSystemUnlocked();
					}
					NguyenBao_HopThanh_MoRa = 0;
					SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
				}
				catch (Exception ex23)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 首饰分解 208 error![" + base.Userid + "]-[" + base.UserName + "]" + ex23.Message);
				}
				goto default;
			case 210:
				try
				{
					if (HopThanhVatPham_Table.TryGetValue(1, out var value15) || HopThanhVatPham_Table.TryGetValue(0, out value15))
					{
						Item_In_Bag[value15.Position].Khoa_Chat = false;
						SynthesisHint(num, 1, 0, Item_In_Bag[value15.Position]);
						HopThanhVatPham_Table.Clear();
						SynthesisSystemUnlocked();
						NguyenBao_HopThanh_MoRa = 0;
					}
				}
				catch (Exception ex21)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成4 error![" + base.Userid + "]-[" + base.UserName + "]" + ex21.Message);
				}
				goto default;
			case 211:
				try
				{
					if (!Item_In_Bag[fromPos].Khoa_Chat && !Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai && (base.CurrentOperationType == 179 || base.CurrentOperationType == 180) && (base.CurrentOperationType != 179 || Item_In_Bag[fromPos].FLD_MAGIC0 < *********) && (!World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0), out var value14) || value14.FLD_RESIDE2 == 12) && !HopThanhVatPham_Table.ContainsKey(1))
					{
						HcItimesClass hcItimesClass51 = new HcItimesClass();
						hcItimesClass51.Position = fromPos;
						hcItimesClass51.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(1, hcItimesClass51);
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex20)
				{
					Form1.WriteLine(1, "披风CuongHoa 放入披风 error![" + base.Userid + "]-[" + base.UserName + "]" + ex20.Message);
				}
				goto default;
			case 212:
				if (HopThanhVatPham_Table.Count != 0 && (HopThanhVatPham_Table.ContainsKey(0) || HopThanhVatPham_Table.ContainsKey(1)) && Item_In_Bag[fromPos].GetVatPham_ID == 1000000545 && !HopThanhVatPham_Table.ContainsKey(2))
				{
					Item_In_Bag[fromPos].Khoa_Chat = true;
					HopThanhVatPham_Table.Add(2, new HcItimesClass
					{
						Position = fromPos,
						VatPham = Item_In_Bag[fromPos].VatPham_byte
					});
					SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
				}
				goto default;
			case 213:
				try
				{
					if (HopThanhVatPham_Table.Count != 0 && !HopThanhVatPham_Table.ContainsKey(3))
					{
						HopThanhVatPham_Table.Add(3, new HcItimesClass
						{
							Position = fromPos,
							VatPham = Item_In_Bag[fromPos].VatPham_byte
						});
						Item_In_Bag[fromPos].Khoa_Chat = true;
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex14)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成3 error![" + base.Userid + "]-[" + base.UserName + "]" + ex14.Message);
				}
				goto default;
			case 214:
				try
				{
					if (base.CurrentOperationType == 181 && !Item_In_Bag[fromPos].Khoa_Chat && !Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
					{
						if (!HopThanhVatPham_Table.ContainsKey(0) && (!World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0), out var value34) || value34.FLD_RESIDE2 == 12))
						{
							HcItimesClass hcItimesClass73 = new HcItimesClass();
							hcItimesClass73.Position = fromPos;
							hcItimesClass73.VatPham = Item_In_Bag[fromPos].VatPham_byte;
							Item_In_Bag[fromPos].Khoa_Chat = true;
							HopThanhVatPham_Table.Add(0, hcItimesClass73);
							SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
						}
						else
						{
							HeThongNhacNho("asdasd");
						}
					}
				}
				catch (Exception ex38)
				{
					Form1.WriteLine(1, "Trang bị chuyển đổi error![" + base.Userid + "]-[" + base.UserName + "]" + ex38.Message);
				}
				goto default;
			case 215:
				try
				{
					if (HopThanhVatPham_Table.Count != 0 && !HopThanhVatPham_Table.ContainsKey(1) && (!World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0), out var value10) || value10.FLD_RESIDE2 == 12))
					{
						HcItimesClass hcItimesClass4 = new HcItimesClass();
						hcItimesClass4.Position = fromPos;
						hcItimesClass4.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(1, hcItimesClass4);
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex13)
				{
					Form1.WriteLine(1, "合成系统 披风组合放ThuocTinh披风 error![" + base.Userid + "]-[" + base.UserName + "]" + ex13.Message);
				}
				goto default;
			case 216:
				try
				{
					X_Vat_Pham_Loai VatPhamCLass = Item_In_Bag[fromPos];
					if (!World.Itme.TryGetValue(BitConverter.ToInt32(VatPhamCLass.VatPham_ID, 0), out var value2))
					{
						break;
					}
					if (VatPhamCLass.FLD_DAY1 != 0)
					{
						HeThongNhacNho("Vật phẩm có thời hạn không thể phân giải");
						break;
					}
					if (value2.FLD_LOCK == 1)
					{
						HeThongNhacNho("Vật phẩm khoá không thể phân giải");
						break;
					}
					if (!Item_In_Bag[fromPos].Khoa_Chat && !Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai && (base.CurrentOperationType == 179 || base.CurrentOperationType == 180) && (base.CurrentOperationType != 179 || Item_In_Bag[fromPos].FLD_MAGIC0 < *********) && (!World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0), out var value43) || value43.FLD_RESIDE2 == 12) && !HopThanhVatPham_Table.ContainsKey(1))
					{
						HcItimesClass hcItimesClass89 = new HcItimesClass();
						hcItimesClass89.Position = fromPos;
						hcItimesClass89.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						HopThanhVatPham_Table.Add(1, hcItimesClass89);
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex92)
				{
					Form1.WriteLine(1, "披风CuongHoa 放入披风 error![" + base.Userid + "]-[" + base.UserName + "]" + ex92.Message);
				}
				goto default;
			case 217:
				if (base.CurrentOperationType == 179 && HopThanhVatPham_Table.Count > 0 && base.Player_Money >= 2000000)
				{
					Phi_HopThanh = 2000000;
					base.Player_Money -= Phi_HopThanh;
					HcItimesClass hcItimesClass118 = null;
					HcItimesClass hcItimesClass119 = null;
					HcItimesClass hcItimesClass120 = null;
					if (HopThanhVatPham_Table.ContainsKey(1))
					{
						hcItimesClass118 = HopThanhVatPham_Table[1];
					}
					if (HopThanhVatPham_Table.ContainsKey(2))
					{
						hcItimesClass119 = HopThanhVatPham_Table[2];
					}
					if (HopThanhVatPham_Table.ContainsKey(3))
					{
						hcItimesClass120 = HopThanhVatPham_Table[3];
					}
					if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass118.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass118.ItemGlobal_ID, 0) && hcItimesClass119 != null && (hcItimesClass119 == null || BitConverter.ToInt64(Item_In_Bag[hcItimesClass119.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass119.ItemGlobal_ID, 0)) && (hcItimesClass120 == null || BitConverter.ToInt64(Item_In_Bag[hcItimesClass120.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass120.ItemGlobal_ID, 0)))
					{
						int numx3 = 0;
						hcItimesClass118.DatDuocThuocTinh();
						hcItimesClass118.CuongHoaThuocTinhGiaiDoan();
						if (World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass118.VatPham_id, 0), out var value65))
						{
							if (value65.FLD_RESIDE2 != 12)
							{
								Form1.WriteLine(6, "合成系统 披风CuongHoa BUG1[" + BitConverter.ToInt32(hcItimesClass118.VatPham_id, 0) + "]-[" + BitConverter.ToInt32(hcItimesClass119.VatPham_id, 0) + "]");
							}
							else if (Item_In_Bag[hcItimesClass118.Position].Vat_Pham_Khoa_Lai)
							{
								SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass118.Position]);
							}
							else
							{
								int num_value3 = Item_In_Bag[hcItimesClass118.Position].FLD_MAGIC0;
								numx3 = num_value3;
								Random random12 = new Random();
								string[] array13 = World.TongXacSuat_CuongHoaAoChoang.Split(';');
								double num55 = random12.Next(int.Parse(array13[0]), int.Parse(array13[1]));
								int CuongHoaSoLuong2 = hcItimesClass118.CuongHoaSoLuong;
								double num57 = 0.0;
								if (CuongHoaSoLuong2 < 10)
								{
									num57 = World.CuongHoaAoChoang_1GianDoan_XacSuat;
								}
								else if (CuongHoaSoLuong2 < 20)
								{
									num57 = World.CuongHoaAoChoang_2GianDoan_XacSuat;
								}
								else if (CuongHoaSoLuong2 < 30)
								{
									num57 = World.CuongHoaAoChoang_3GianDoan_XacSuat;
								}
								else if (CuongHoaSoLuong2 < 40)
								{
									num57 = World.CuongHoaAoChoang_4GianDoan_XacSuat;
								}
								else if (CuongHoaSoLuong2 < 50)
								{
									num57 = World.CuongHoaAoChoang_5GianDoan_XacSuat;
								}
								else if (CuongHoaSoLuong2 < 60)
								{
									num57 = World.CuongHoaAoChoang_6GianDoan_XacSuat;
								}
								else if (CuongHoaSoLuong2 < 70)
								{
									num57 = World.CuongHoaAoChoang_7GianDoan_XacSuat;
								}
								else if (CuongHoaSoLuong2 < 80)
								{
									num57 = World.CuongHoaAoChoang_8GianDoan_XacSuat;
								}
								else if (CuongHoaSoLuong2 < 90)
								{
									num57 = World.CuongHoaAoChoang_9GianDoan_XacSuat;
								}
								else if (CuongHoaSoLuong2 < 100)
								{
									num57 = World.CuongHoaAoChoang_10GianDoan_XacSuat;
								}
								if (hcItimesClass118.CuongHoaSoLuong >= 100)
								{
									HopThanhVatPham_Table.Clear();
								}
								else
								{
									if (hcItimesClass120 != null)
									{
										num55 += (double)LuckyCharmBonus(BitConverter.ToInt32(hcItimesClass120.VatPham_id, 0));
									}
									if (NguyenBao_HopThanh_MoRa == 1)
									{
										num55 += 5.0;
									}
									if (base.FLD_VIP == 1)
									{
									}
									if (World.TyLe_TangCuong_AoChoang != 0.0)
									{
										num55 += 100.0 * World.TyLe_TangCuong_AoChoang;
									}
									if (base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
									{
										num55 += 100.0 * base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
									}
									if (base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
									{
										num55 += 100.0 * base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
									}
									if (num55 >= num57)
									{
										hcItimesClass118.CuongHoaLoaiHinh = 4;
										HcItimesClass hcItimesClass156 = hcItimesClass118;
										int giaiDoanSoLuong = hcItimesClass156.CuongHoaSoLuong + 1;
										hcItimesClass156.CuongHoaSoLuong = giaiDoanSoLuong;
										if (hcItimesClass118.CuongHoaSoLuong > 100)
										{
											hcItimesClass118.CuongHoaSoLuong = 100;
										}
										hcItimesClass118.ThietLap_GiaiDoanThuocTinh();
										if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass118.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass118.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass118.ItemGlobal_ID, 0))
										{
											SubtractItems(hcItimesClass118.Position, 1);
											AddItems(hcItimesClass118.ItemGlobal_ID, hcItimesClass118.VatPham_id, hcItimesClass118.Position, hcItimesClass118.VatPhamSoLuong, hcItimesClass118.VatPham_ThuocTinh);
										}
										Item_In_Bag[hcItimesClass118.Position].Khoa_Chat = false;
										RxjhClass.SyntheticRecord(base.Userid, base.UserName, value65.ItmeNAME, num, "AoChoangCH", "Thanh Cong", Item_In_Bag[hcItimesClass118.Position], Item_In_Bag[hcItimesClass118.Position].FLD_CuongHoaSoLuong, numx3);
										SynthesisHint(num, 1, Phi_HopThanh, Item_In_Bag[hcItimesClass118.Position]);
									}
									else
									{
										int chsl = 0;
										if (hcItimesClass118.CuongHoaSoLuong >= 4)
										{
											hcItimesClass118.CuongHoaLoaiHinh = 4;
											hcItimesClass118.CuongHoaSoLuong -= 3;
											hcItimesClass118.ThietLap_GiaiDoanThuocTinh();
											chsl = hcItimesClass118.CuongHoaSoLuong;
											if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass118.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass118.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass118.ItemGlobal_ID, 0))
											{
												SubtractItems(hcItimesClass118.Position, 1);
												AddItems(hcItimesClass118.ItemGlobal_ID, hcItimesClass118.VatPham_id, hcItimesClass118.Position, hcItimesClass118.VatPhamSoLuong, hcItimesClass118.VatPham_ThuocTinh);
											}
											Item_In_Bag[hcItimesClass118.Position].Khoa_Chat = false;
										}
										RxjhClass.SyntheticRecord(base.Userid, base.UserName, value65.ItmeNAME, num, "AoChoangCH", "That Bai", Item_In_Bag[hcItimesClass118.Position], chsl, numx3);
										SynthesisHint(num, 65513, Phi_HopThanh, Item_In_Bag[hcItimesClass118.Position]);
									}
									UpdateMoneyAndWeight();
									SubtractItems(hcItimesClass119.Position, 1);
									if (hcItimesClass120 != null)
									{
										SubtractItems(hcItimesClass120.Position, 1);
									}
									HopThanhVatPham_Table.Clear();
									SynthesisSystemUnlocked();
									NguyenBao_HopThanh_MoRa = 0;
									OpenWarehouse = true;
									Phi_HopThanh = 0;
								}
							}
						}
					}
				}
				goto default;
			case 218:
				if (base.CurrentOperationType == 180)
				{
					int num31 = GetParcelVacancyPosition();
					if (num31 == -1)
					{
						HeThongNhacNho("Không còn chỗ trống", 10, "Túi đồ");
					}
					else if (HopThanhVatPham_Table.Count != 0)
					{
						HcItimesClass hcItimesClass93 = null;
						if (HopThanhVatPham_Table.ContainsKey(1))
						{
							hcItimesClass93 = HopThanhVatPham_Table[1];
						}
						if (World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass93.VatPham_id, 0), out var value48))
						{
							if (value48.FLD_RESIDE2 != 12)
							{
								Form1.WriteLine(6, "合成系统 披风CuongHoa BUG1[" + BitConverter.ToInt32(hcItimesClass93.VatPham_id, 0) + "]-[" + base.UserName + "]");
							}
							else
							{
								CheckTheNumberOfIngotsInBaibaoge();
								if (base.Player_Money < World.SoLuong_Vang_MoiLanTieuThu)
								{
									HeThongNhacNho("Không đủ: " + World.SoLuong_Vang_MoiLanTieuThu + " lượng");
								}
								else
								{
									base.Player_Money -= World.SoLuong_Vang_MoiLanTieuThu;
									HeThongNhacNho("Đã trả: " + World.SoLuong_Vang_MoiLanTieuThu + " lượng");
									UpdateMoneyAndWeight();
									int num32 = RNG.Next(1, 100);
									int num33 = ((num32 > 0 && num32 <= 50) ? 3 : ((num32 > 50 && num32 <= 70) ? 4 : ((num32 <= 70 || num32 > 90) ? 5 : 3)));
									IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1000000545), num31, BitConverter.GetBytes(num33), new byte[56]);
									SynthesisHint(num, 1, num33, Item_In_Bag[hcItimesClass93.Position]);
									SubtractItems(hcItimesClass93.Position, 1);
									HopThanhVatPham_Table.Clear();
									SynthesisSystemUnlocked();
								}
							}
						}
					}
				}
				goto default;
			case 219:
				try
				{
					if (HopThanhVatPham_Table.Count > 0)
					{
						HcItimesClass hcItimesClass90 = null;
						HcItimesClass hcItimesClass91 = null;
						HcItimesClass hcItimesClass92 = null;
						if (HopThanhVatPham_Table.Count < 3)
						{
							HeThongNhacNho("Thiếu nguyên liệu chuyển đổi áo choàng");
						}
						else
						{
							if (HopThanhVatPham_Table.ContainsKey(0))
							{
								hcItimesClass90 = HopThanhVatPham_Table[0];
							}
							if (HopThanhVatPham_Table.ContainsKey(1))
							{
								hcItimesClass91 = HopThanhVatPham_Table[1];
							}
							if (HopThanhVatPham_Table.ContainsKey(2))
							{
								hcItimesClass92 = HopThanhVatPham_Table[2];
							}
							if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass90.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass90.ItemGlobal_ID, 0) && BitConverter.ToInt64(Item_In_Bag[hcItimesClass91.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass91.ItemGlobal_ID, 0) && BitConverter.ToInt64(Item_In_Bag[hcItimesClass92.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass92.ItemGlobal_ID, 0))
							{
								hcItimesClass90.DatDuocThuocTinh();
								hcItimesClass90.CuongHoaThuocTinhGiaiDoan();
								hcItimesClass91.CuongHoaThuocTinhGiaiDoan();
								ItmeClass value46;
								if (Item_In_Bag[hcItimesClass90.Position].Vat_Pham_Khoa_Lai)
								{
									SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass90.Position]);
								}
								else if (Item_In_Bag[hcItimesClass91.Position].Vat_Pham_Khoa_Lai)
								{
									SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass91.Position]);
								}
								else if (hcItimesClass91.CuongHoaSoLuong <= hcItimesClass90.CuongHoaSoLuong)
								{
									HeThongNhacNho("Trang bị phụ phải có cường hóa lớn hơn");
								}
								else if (World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass90.VatPham_id, 0), out value46))
								{
									ItmeClass value47;
									if (value46.FLD_RESIDE2 != 12)
									{
										Form1.WriteLine(6, "合成系统 披风CuongHoaBUG[" + BitConverter.ToInt32(hcItimesClass90.VatPham_id, 0) + "]-[" + BitConverter.ToInt32(hcItimesClass92.VatPham_id, 0) + "]");
									}
									else if (BitConverter.ToInt32(hcItimesClass92.VatPham_id, 0) != 1000000545)
									{
										Form1.WriteLine(6, "合成系统 披风CuongHoaBUG[" + BitConverter.ToInt32(hcItimesClass90.VatPham_id, 0) + "]-[" + BitConverter.ToInt32(hcItimesClass92.VatPham_id, 0) + "]");
									}
									else if (World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass90.VatPham_id, 0), out value47))
									{
										if (value47.FLD_RESIDE2 != 12)
										{
											Form1.WriteLine(6, "合成系统 披风CuongHoaBUG[" + BitConverter.ToInt32(hcItimesClass91.VatPham_id, 0) + "]");
										}
										else
										{
											hcItimesClass90.CuongHoaLoaiHinh = 4;
											hcItimesClass90.CuongHoaSoLuong = hcItimesClass91.CuongHoaSoLuong;
											if (hcItimesClass90.CuongHoaSoLuong > 100)
											{
												hcItimesClass90.CuongHoaSoLuong = 100;
											}
											hcItimesClass90.ThietLap_GiaiDoanThuocTinh();
											if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass90.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass90.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass90.ItemGlobal_ID, 0))
											{
												SubtractItems(hcItimesClass90.Position, 1);
												AddItems(hcItimesClass90.ItemGlobal_ID, hcItimesClass90.VatPham_id, hcItimesClass90.Position, hcItimesClass90.VatPhamSoLuong, hcItimesClass90.VatPham_ThuocTinh);
											}
											Item_In_Bag[hcItimesClass90.Position].Khoa_Chat = false;
											SubtractItems(hcItimesClass91.Position, 1);
											SubtractItems(hcItimesClass92.Position, 1);
											HopThanhVatPham_Table.Clear();
											SynthesisSystemUnlocked();
											NguyenBao_HopThanh_MoRa = 0;
											SynthesisHint(num, 1, 1000000, Item_In_Bag[hcItimesClass90.Position]);
										}
									}
								}
							}
						}
					}
				}
				catch (Exception ex50)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 披风合成 error![" + base.Userid + "]-[" + base.UserName + "]" + ex50.Message);
				}
				goto default;
			case 220:
				try
				{
					if (HopThanhVatPham_Table.TryGetValue(1, out var value45))
					{
						Item_In_Bag[value45.Position].Khoa_Chat = false;
						SynthesisHint(num, 1, 0, Item_In_Bag[value45.Position]);
					}
					HopThanhVatPham_Table.Clear();
					SynthesisSystemUnlocked();
					NguyenBao_HopThanh_MoRa = 0;
				}
				catch (Exception ex49)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成4 error![" + base.Userid + "]-[" + base.UserName + "]" + ex49.Message);
				}
				goto default;
			case 221:
				try
				{
					if (!Item_In_Bag[fromPos].Khoa_Chat && !Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai && World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0), out var value44))
					{
						if (value44.FLD_LEVEL <= 10)
						{
							HeThongNhacNho("Trang bi cấp 10 trở lên.");
						}
						else if (value44.FLD_RESIDE2 != 4 && value44.FLD_RESIDE2 != 1 && value44.FLD_RESIDE2 != 2 && value44.FLD_RESIDE2 != 5 && value44.FLD_RESIDE2 != 6)
						{
							HeThongNhacNho("Vũ khí, Y phục, Hộ thủ, Giày, Áo giáp");
						}
						else if (Item_In_Bag[fromPos].FLD_FJ_TienHoa == 2)
						{
							HeThongNhacNho("Vật phẩm cao cấp không thể nâng cấp thêm.");
						}
						else if (!HopThanhVatPham_Table.ContainsKey(1))
						{
							int SoTien2 = (Phi_HopThanh = CalculateSyntheticEnhancementCost(value44, fromPos, num));
							HcItimesClass hcItimesClass88 = new HcItimesClass();
							hcItimesClass88.Position = fromPos;
							hcItimesClass88.VatPham = Item_In_Bag[fromPos].VatPham_byte;
							Item_In_Bag[fromPos].Khoa_Chat = true;
							HopThanhVatPham_Table.Add(1, hcItimesClass88);
							SynthesisHint(num, 1, SoTien2, Item_In_Bag[fromPos]);
						}
					}
				}
				catch (Exception ex48)
				{
					Form1.WriteLine(1, "合成系统 装备升真 error![" + base.Userid + "]-[" + base.UserName + "]" + ex48.Message);
				}
				goto default;
			case 222:
				try
				{
					if (HopThanhVatPham_Table.Count != 0 && HopThanhVatPham_Table.ContainsKey(1))
					{
						HcItimesClass hcItimesClass87 = HopThanhVatPham_Table[1];
						if (World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0), out var value41) && (value41.FLD_PID == 1008001137 || value41.FLD_PID == 1008001138 || value41.FLD_PID == 1000000853 || value41.FLD_PID == 1000000854) && World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[hcItimesClass87.Position].VatPham_ID, 0), out var value42) && (value42.FLD_RESIDE2 != 4 || value41.FLD_PID == 1000000853 || value41.FLD_PID == 1008001137) && ((value42.FLD_RESIDE2 != 1 && value42.FLD_RESIDE2 != 2 && value42.FLD_RESIDE2 != 5 && value42.FLD_RESIDE2 != 6) || value41.FLD_PID == 1000000854 || value41.FLD_PID == 1008001138) && !HopThanhVatPham_Table.ContainsKey(2))
						{
							HopThanhVatPham_Table.Add(2, new HcItimesClass
							{
								Position = fromPos,
								VatPham = Item_In_Bag[fromPos].VatPham_byte
							});
							Item_In_Bag[fromPos].Khoa_Chat = true;
							SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
						}
					}
				}
				catch (Exception ex47)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成2 error![" + base.Userid + "]-[" + base.UserName + "]" + ex47.Message);
				}
				goto default;
			case 223:
				try
				{
					ItmeClass value40;
					if (HopThanhVatPham_Table.Count == 0)
					{
						SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
					}
					else if (!HopThanhVatPham_Table.ContainsKey(3) && World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0), out value40) && value40.FLD_PID == 1008001610)
					{
						HopThanhVatPham_Table.Add(3, new HcItimesClass
						{
							Position = fromPos,
							VatPham = Item_In_Bag[fromPos].VatPham_byte
						});
						Item_In_Bag[fromPos].Khoa_Chat = true;
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex46)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成3 error![" + base.Userid + "]-[" + base.UserName + "]" + ex46.Message);
				}
				goto default;
			case 224:
				try
				{
					int PhiGold = 1000000;
					if (HopThanhVatPham_Table.Count > 0)
					{
						HcItimesClass hcItimesClass84 = null;
						HcItimesClass hcItimesClass85 = null;
						HcItimesClass hcItimesClass86 = null;
						if (HopThanhVatPham_Table.ContainsKey(1))
						{
							hcItimesClass84 = HopThanhVatPham_Table[1];
						}
						if (HopThanhVatPham_Table.ContainsKey(2))
						{
							hcItimesClass85 = HopThanhVatPham_Table[2];
						}
						if (HopThanhVatPham_Table.ContainsKey(3))
						{
							hcItimesClass86 = HopThanhVatPham_Table[3];
						}
						if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass84.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass84.ItemGlobal_ID, 0) && BitConverter.ToInt64(Item_In_Bag[hcItimesClass85.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass85.ItemGlobal_ID, 0) && (hcItimesClass86 == null || BitConverter.ToInt64(Item_In_Bag[hcItimesClass86.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass86.ItemGlobal_ID, 0)))
						{
							hcItimesClass84.DatDuocThuocTinh();
							hcItimesClass84.CuongHoaThuocTinhGiaiDoan();
							hcItimesClass85.CuongHoaThuocTinhGiaiDoan();
							ItmeClass value39;
							if (Item_In_Bag[hcItimesClass84.Position].Vat_Pham_Khoa_Lai)
							{
								HopThanhVatPham_Table.Clear();
							}
							else if (World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass84.VatPham_id, 0), out value39))
							{
								if (value39.FLD_RESIDE2 != 1 && value39.FLD_RESIDE2 != 2 && value39.FLD_RESIDE2 != 4 && value39.FLD_RESIDE2 != 5 && value39.FLD_RESIDE2 != 6)
								{
									HopThanhVatPham_Table.Clear();
								}
								else
								{
									switch (World.TieuThu_ThietBi)
									{
									default:
										HeThongNhacNho("System 221, vui lòng báo mã số này cho Admin.");
										return;
									case 0:
										CheckTheNumberOfIngotsInBaibaoge();
										if (base.Player_Money < Phi_HopThanh)
										{
											HeThongNhacNho("Point không đủ, cần" + Phi_HopThanh + "Point.");
											return;
										}
										KiemSoatNguyenBao_SoLuong(Phi_HopThanh, 0);
										Save_NguyenBaoData();
										break;
									case 1:
										CheckTheNumberOfIngotsInBaibaoge();
										if (base.FLD_Coin < Phi_HopThanh)
										{
											HeThongNhacNho("Coin không đủ, cần:" + Phi_HopThanh + "Coin.");
											return;
										}
										CheckTheIngotPointData(Phi_HopThanh, 0);
										Save_NguyenBaoData();
										break;
									case 2:
										if (base.Player_Money < PhiGold)
										{
											HeThongNhacNho("Tiền vàng không đủ " + PhiGold + " lượng");
											return;
										}
										break;
									}
									if (base.Player_Money >= PhiGold)
									{
										base.Player_Money -= PhiGold;
										UpdateMoneyAndWeight();
										byte[] array3 = new byte[World.VatPham_ThuocTinh_KichThuoc];
										Random random11 = new Random();
										string[] array12 = World.TongXacSuat_PhamChat.Split(';');
										double num29 = random11.Next(int.Parse(array12[0]), int.Parse(array12[1]));
										double num30 = 0.0;
										if (array3[52] == 0)
										{
											num30 = 60.0;
										}
										else if (array3[52] == 1)
										{
											num30 = 80.0;
										}
										byte[] VatPham_ID = hcItimesClass84.VatPham_id;
										System.Buffer.BlockCopy(hcItimesClass84.VatPham_ThuocTinh, 0, array3, 0, World.VatPham_ThuocTinh_KichThuoc);
										if (num29 >= num30)
										{
											if ((value39.FLD_RESIDE2 == 6 && value39.FLD_NJ > 0) || value39.FLD_RESIDE2 != 6)
											{
												VatPham_ID = BitConverter.GetBytes(BitConverter.ToInt32(hcItimesClass84.VatPham_id, 0));
											}
											if (array3[52] == 0)
											{
												System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array3, 52, 4);
											}
											else if (array3[52] == 1)
											{
												System.Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array3, 52, 4);
											}
											RxjhClass.SyntheticRecord(base.Userid, base.UserName, value39.ItmeNAME, num, "Item-Upgrade", "Thanh Cong", Item_In_Bag[hcItimesClass84.Position]);
											SynthesisHint(num, 1, Phi_HopThanh, Item_In_Bag[hcItimesClass84.Position]);
										}
										else
										{
											RxjhClass.SyntheticRecord(base.Userid, base.UserName, value39.ItmeNAME, num, "Item-Upgrade", "That Bai", Item_In_Bag[hcItimesClass84.Position]);
											SynthesisHint(num, 0, Phi_HopThanh, Item_In_Bag[hcItimesClass84.Position]);
										}
										SubtractItems(hcItimesClass85.Position, 1);
										if (hcItimesClass86 != null)
										{
											SubtractItems(hcItimesClass86.Position, 1);
										}
										if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass84.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass84.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass84.ItemGlobal_ID, 0))
										{
											if (num29 >= num30)
											{
												SubtractItems(hcItimesClass84.Position, 1);
												AddItems(hcItimesClass84.ItemGlobal_ID, VatPham_ID, hcItimesClass84.Position, hcItimesClass84.VatPhamSoLuong, array3);
											}
											else if (HopThanhVatPham_Table.Count == 3)
											{
												SubtractItems(hcItimesClass84.Position, 1);
												AddItems(hcItimesClass84.ItemGlobal_ID, VatPham_ID, hcItimesClass84.Position, hcItimesClass84.VatPhamSoLuong, array3);
											}
											else
											{
												SubtractItems(hcItimesClass84.Position, 1);
											}
											UpdateEquipmentEffects();
											CalculateCharacterEquipmentData();
										}
										Item_In_Bag[hcItimesClass84.Position].Khoa_Chat = false;
										HopThanhVatPham_Table.Clear();
										SynthesisSystemUnlocked();
										NguyenBao_HopThanh_MoRa = 0;
										Phi_HopThanh = 0;
									}
									else
									{
										SynthesisHint(11, 4, 0, Item_In_Bag[hcItimesClass84.Position]);
										HopThanhVatPham_Table.Clear();
									}
								}
							}
							else
							{
								HopThanhVatPham_Table.Clear();
							}
						}
					}
				}
				catch (Exception ex45)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成 error![" + base.Userid + "]-[" + base.UserName + "]" + ex45.Message);
				}
				goto default;
			case 19:
			case 510:
			case 240:
				try
				{
					if (HopThanhVatPham_Table.TryGetValue(1, out var value38))
					{
						Item_In_Bag[value38.Position].Khoa_Chat = false;
						SynthesisHint(num, 1, 0, Item_In_Bag[value38.Position]);
					}
					HopThanhVatPham_Table.Clear();
					SynthesisSystemUnlocked();
					NguyenBao_HopThanh_MoRa = 0;
				}
				catch (Exception ex44)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成4 error![" + base.Userid + "]-[" + base.UserName + "]" + ex44.Message);
				}
				goto default;
			case 11:
			case 511:
			case 241:
				try
				{
					if ((base.CurrentOperationType == 8 || base.CurrentOperationType == 209 || base.CurrentOperationType == 323) && !HopThanhVatPham_Table.ContainsKey(1))
					{
						ItmeClass value37;
						if (Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
						}
						else if (World.Itme.TryGetValue((int)Item_In_Bag[fromPos].GetVatPham_ID, out value37))
						{
							if (value37.FLD_RESIDE2 == 12)
							{
								SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
							}
							else if (value37.FLD_LEVEL < 120 && Item_In_Bag[fromPos].FLD_CuongHoaSoLuong == 10)
							{
								SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
							}
							else if (Item_In_Bag[fromPos].FLD_CuongHoaSoLuong < 15)
							{
								int SoTien = (Phi_HopThanh = CalculateSyntheticEnhancementCost(value37, fromPos, num));
								HcItimesClass hcItimesClass83 = new HcItimesClass();
								hcItimesClass83.Position = fromPos;
								hcItimesClass83.VatPham = Item_In_Bag[fromPos].VatPham_byte;
								Item_In_Bag[fromPos].Khoa_Chat = true;
								HopThanhVatPham_Table.Add(1, hcItimesClass83);
								SynthesisHint(num, 1, SoTien, Item_In_Bag[fromPos]);
							}
						}
					}
				}
				catch (Exception ex42)
				{
					Form1.WriteLine(1, "CuongHoaNPC input 1 error![" + base.Userid + "]-[" + base.UserName + "]" + ex42.Message);
				}
				goto default;
			case 12:
			case 512:
			case 242:
				try
				{
					HcItimesClass hcItimesClass81;
					if ((base.CurrentOperationType == 8 || base.CurrentOperationType == 209 || base.CurrentOperationType == 323) && !HopThanhVatPham_Table.ContainsKey(4))
					{
						if (HopThanhVatPham_Table.Count == 0)
						{
							SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
						}
						else if (HopThanhVatPham_Table.ContainsKey(1))
						{
							hcItimesClass81 = HopThanhVatPham_Table[1];
							if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) >= 1 && World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass81.VatPham_id, 0), out var value36))
							{
								if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 800000006 && BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 800000060)
								{
									SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
								}
								else if (value36.FLD_LEVEL >= 0 && value36.FLD_LEVEL < 130)
								{
									if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) == 800000006)
									{
										goto IL_1b635;
									}
									SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
								}
								else if (value36.FLD_LEVEL >= 130)
								{
									if (value36.FLD_LEVEL < 130 || BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) == 800000060)
									{
										goto IL_1b635;
									}
									SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
								}
							}
						}
						else
						{
							SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
						}
					}
					goto end_IL_1b332;
					IL_1b58c:
					HcItimesClass hcItimesClass82;
					if (hcItimesClass81.ThuocTinh1.ThuocTinhSoLuong + hcItimesClass81.ThuocTinh2.ThuocTinhSoLuong != 0 && hcItimesClass81.ThuocTinh3.ThuocTinhSoLuong + hcItimesClass81.ThuocTinh4.ThuocTinhSoLuong != 0 && BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) >= 3 && !HopThanhVatPham_Table.ContainsKey(4))
					{
						HopThanhVatPham_Table.Add(4, hcItimesClass82);
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
					goto end_IL_1b332;
					IL_1b635:
					if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 800000006 && BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 800000060)
					{
						SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
					}
					else
					{
						hcItimesClass82 = new HcItimesClass();
						hcItimesClass82.Position = fromPos;
						hcItimesClass82.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						Item_In_Bag[fromPos].Khoa_Chat = true;
						if (!HopThanhVatPham_Table.ContainsKey(2))
						{
							HopThanhVatPham_Table.Add(2, hcItimesClass82);
							SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
						}
						hcItimesClass81.DatDuocThuocTinh();
						if (hcItimesClass81.ThuocTinh1.ThuocTinhSoLuong + hcItimesClass81.ThuocTinh2.ThuocTinhSoLuong == 0)
						{
							goto IL_1b58c;
						}
						if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) >= 2)
						{
							if (!HopThanhVatPham_Table.ContainsKey(3))
							{
								HopThanhVatPham_Table.Add(3, hcItimesClass82);
								SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
							}
							goto IL_1b58c;
						}
					}
					end_IL_1b332:;
				}
				catch (Exception ex41)
				{
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成2 error![" + base.Userid + "]-[" + base.UserName + "]" + ex41.Message);
				}
				goto default;
			case 13:
			case 513:
			case 243:
				try
				{
					if (base.CurrentOperationType == 8 || base.CurrentOperationType == 209 || base.CurrentOperationType == 323)
					{
						if (HopThanhVatPham_Table.Count == 0)
						{
							SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
						}
						else if (!HopThanhVatPham_Table.ContainsKey(5))
						{
							HcItimesClass hcItimesClass79 = new HcItimesClass();
							hcItimesClass79.Position = fromPos;
							hcItimesClass79.VatPham = Item_In_Bag[fromPos].VatPham_byte;
							Item_In_Bag[fromPos].Khoa_Chat = true;
							HopThanhVatPham_Table.Add(5, hcItimesClass79);
							SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
						}
					}
				}
				catch (Exception ex40)
				{
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成3 error![" + base.Userid + "]-[" + base.UserName + "]" + ex40.Message);
				}
				goto default;
			case 10:
			case 514:
			case 244:
				try
				{
					int numx = 0;
					HcItimesClass hcItimesClass74;
					HcItimesClass hcItimesClass75;
					HcItimesClass hcItimesClass76;
					HcItimesClass hcItimesClass77;
					HcItimesClass hcItimesClass78;
					ItmeClass value35;
					if (HopThanhVatPham_Table.Count > 0 && (base.CurrentOperationType == 8 || base.CurrentOperationType == 209 || base.CurrentOperationType == 323))
					{
						hcItimesClass74 = null;
						hcItimesClass75 = null;
						hcItimesClass76 = null;
						hcItimesClass77 = null;
						hcItimesClass78 = null;
						if (HopThanhVatPham_Table.ContainsKey(1))
						{
							hcItimesClass74 = HopThanhVatPham_Table[1];
						}
						if (HopThanhVatPham_Table.ContainsKey(2))
						{
							hcItimesClass75 = HopThanhVatPham_Table[2];
						}
						if (HopThanhVatPham_Table.ContainsKey(3))
						{
							hcItimesClass76 = HopThanhVatPham_Table[3];
						}
						if (HopThanhVatPham_Table.ContainsKey(4))
						{
							hcItimesClass77 = HopThanhVatPham_Table[4];
						}
						if (HopThanhVatPham_Table.ContainsKey(5))
						{
							hcItimesClass78 = HopThanhVatPham_Table[5];
						}
						if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass74.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass74.ItemGlobal_ID, 0))
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass74.Position]);
							HopThanhVatPham_Table.Clear();
						}
						else if (hcItimesClass75 == null)
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass74.Position]);
							HopThanhVatPham_Table.Clear();
						}
						else if (hcItimesClass75 != null && BitConverter.ToInt64(Item_In_Bag[hcItimesClass75.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass75.ItemGlobal_ID, 0))
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass75.Position]);
							HopThanhVatPham_Table.Clear();
						}
						else if (hcItimesClass76 != null && BitConverter.ToInt64(Item_In_Bag[hcItimesClass76.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass76.ItemGlobal_ID, 0))
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass76.Position]);
							HopThanhVatPham_Table.Clear();
						}
						else if (hcItimesClass77 != null && BitConverter.ToInt64(Item_In_Bag[hcItimesClass77.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass77.ItemGlobal_ID, 0))
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass77.Position]);
							HopThanhVatPham_Table.Clear();
						}
						else if (hcItimesClass78 != null && BitConverter.ToInt64(Item_In_Bag[hcItimesClass78.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass78.ItemGlobal_ID, 0))
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass78.Position]);
							HopThanhVatPham_Table.Clear();
						}
						else
						{
							hcItimesClass74.DatDuocThuocTinh();
							hcItimesClass74.CuongHoaThuocTinhGiaiDoan();
							int num_value = Item_In_Bag[hcItimesClass74.Position].FLD_MAGIC0;
							numx = num_value;
							if (!World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass74.VatPham_id, 0), out value35))
							{
								SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass74.Position]);
								HopThanhVatPham_Table.Clear();
							}
							else if (value35.FLD_RESIDE2 != 1 && value35.FLD_RESIDE2 != 2 && value35.FLD_RESIDE2 != 4 && value35.FLD_RESIDE2 != 5 && value35.FLD_RESIDE2 != 6 && value35.FLD_RESIDE2 != 14 && value35.FLD_RESIDE2 != 23 && value35.FLD_RESIDE2 != 24 && value35.FLD_RESIDE2 != 25)
							{
								SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass74.Position]);
								HopThanhVatPham_Table.Clear();
							}
							else if (Item_In_Bag[hcItimesClass74.Position].Vat_Pham_Khoa_Lai)
							{
								SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass74.Position]);
								HopThanhVatPham_Table.Clear();
							}
							else if (hcItimesClass74.CuongHoaSoLuong >= 15)
							{
								SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass74.Position]);
								HopThanhVatPham_Table.Clear();
							}
							else if (value35.FLD_LEVEL >= 130)
							{
								if (hcItimesClass75 != null && BitConverter.ToInt32(hcItimesClass75.VatPham_id, 0) != 800000060)
								{
									SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass74.Position]);
									HopThanhVatPham_Table.Clear();
								}
								else if (hcItimesClass76 != null && BitConverter.ToInt32(hcItimesClass76.VatPham_id, 0) != 800000060)
								{
									SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass74.Position]);
									HopThanhVatPham_Table.Clear();
								}
								else
								{
									if (hcItimesClass77 == null || BitConverter.ToInt32(hcItimesClass77.VatPham_id, 0) == 800000060)
									{
										goto IL_1c1e5;
									}
									SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass74.Position]);
									HopThanhVatPham_Table.Clear();
								}
							}
							else if (hcItimesClass75 != null && BitConverter.ToInt32(hcItimesClass75.VatPham_id, 0) != 800000006)
							{
								SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass74.Position]);
								HopThanhVatPham_Table.Clear();
							}
							else if (hcItimesClass76 != null && BitConverter.ToInt32(hcItimesClass76.VatPham_id, 0) != 800000006)
							{
								SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass74.Position]);
								HopThanhVatPham_Table.Clear();
							}
							else
							{
								if (hcItimesClass77 == null || BitConverter.ToInt32(hcItimesClass77.VatPham_id, 0) == 800000006)
								{
									goto IL_1c1e5;
								}
								SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass74.Position]);
								HopThanhVatPham_Table.Clear();
							}
						}
					}
					goto end_IL_1b972;
					IL_1c2cb:
					SynthesisHint(11, 4, 0, Item_In_Bag[hcItimesClass74.Position]);
					HopThanhVatPham_Table.Clear();
					goto end_IL_1b972;
					IL_1c1e5:
					if (Phi_HopThanh > 0)
					{
						if (value35.FLD_NJ == 0)
						{
							if (base.Player_Money < Phi_HopThanh)
							{
								goto IL_1c2cb;
							}
							base.Player_Money -= Phi_HopThanh;
							UpdateMoneyAndWeight();
						}
						else
						{
							if (base.Player_Money < Phi_HopThanh)
							{
								goto IL_1c2cb;
							}
							hcItimesClass74.FLD_FJ_NJ = 0;
							base.Player_Money -= Phi_HopThanh;
							UpdateMoneyAndWeight();
							Init_Item_In_Bag();
							CalculateCharacterEquipmentData();
							UpdateMartialArtsAndStatus();
						}
					}
					Random random = new Random();
					string[] array4 = World.TongXacSuat_CuongHoa.Split(';');
					double rate = random.Next(int.Parse(array4[0]), int.Parse(array4[1]));
					if (hcItimesClass78 != null)
					{
						rate += (double)LuckyCharmBonus(BitConverter.ToInt32(hcItimesClass78.VatPham_id, 0));
					}
					if (NguyenBao_HopThanh_MoRa == 1)
					{
						rate += 5.0;
					}
					if (base.FLD_VIP == 1)
					{
					}
					if (World.TyLe_CuongHoa != 0.0)
					{
						rate += 100.0 * World.TyLe_CuongHoa;
					}
					if (base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
					{
						rate += 100.0 * base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
					}
					if (base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
					{
						rate += 100.0 * base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
					}
					if (hcItimesClass74.ThuocTinh1.ThuocTinhSoLuong + hcItimesClass74.ThuocTinh2.ThuocTinhSoLuong != 0 && hcItimesClass76 == null)
					{
						HeThongNhacNho("Cần 2 cường hoá thạch", 10, "Cường hóa");
					}
					else if (hcItimesClass74.ThuocTinh1.ThuocTinhSoLuong + hcItimesClass74.ThuocTinh2.ThuocTinhSoLuong != 0 && hcItimesClass74.ThuocTinh3.ThuocTinhSoLuong + hcItimesClass74.ThuocTinh4.ThuocTinhSoLuong != 0 && hcItimesClass77 == null)
					{
						HeThongNhacNho("Cần 3 cường hoá thạch", 10, "Cường hóa");
					}
					else
					{
						if ((hcItimesClass74.CuongHoaSoLuong == 0 && !(rate < 100.0 - World.CuongHoa1TyLe_HopThanh)) || (hcItimesClass74.CuongHoaSoLuong == 1 && !(rate < 100.0 - World.CuongHoa2TyLe_HopThanh)) || (hcItimesClass74.CuongHoaSoLuong == 2 && !(rate < 100.0 - World.CuongHoa3TyLe_HopThanh)) || (hcItimesClass74.CuongHoaSoLuong == 3 && !(rate < 100.0 - World.CuongHoa4TyLe_HopThanh)) || (hcItimesClass74.CuongHoaSoLuong == 4 && !(rate < 100.0 - World.CuongHoa5TyLe_HopThanh)) || (hcItimesClass74.CuongHoaSoLuong == 5 && !(rate < 100.0 - World.CuongHoa6TyLe_HopThanh)) || (hcItimesClass74.CuongHoaSoLuong == 6 && !(rate < 100.0 - World.CuongHoa7TyLe_HopThanh)) || (hcItimesClass74.CuongHoaSoLuong == 7 && !(rate < 100.0 - World.CuongHoa8TyLe_HopThanh)) || (hcItimesClass74.CuongHoaSoLuong == 8 && !(rate < 100.0 - World.CuongHoa9TyLe_HopThanh)) || (hcItimesClass74.CuongHoaSoLuong == 9 && !(rate < 100.0 - World.CuongHoa10TyLe_HopThanh)) || (hcItimesClass74.CuongHoaSoLuong == 10 && !(rate < 100.0 - World.CuongHoa11TyLe_HopThanh)) || (hcItimesClass74.CuongHoaSoLuong == 11 && !(rate < 100.0 - World.CuongHoa12TyLe_HopThanh)) || (hcItimesClass74.CuongHoaSoLuong == 12 && !(rate < 100.0 - World.CuongHoa13TyLe_HopThanh)) || (hcItimesClass74.CuongHoaSoLuong == 13 && !(rate < 100.0 - World.CuongHoa14TyLe_HopThanh)) || (hcItimesClass74.CuongHoaSoLuong == 14 && rate >= 100.0 - World.CuongHoa15TyLe_HopThanh))
						{
							hcItimesClass74.CuongHoaLoaiHinh = ((value35.FLD_RESIDE2 == 4) ? 1 : 2);
							HcItimesClass hcItimesClass156 = hcItimesClass74;
							int giaiDoanSoLuong = hcItimesClass156.CuongHoaSoLuong + 1;
							hcItimesClass156.CuongHoaSoLuong = giaiDoanSoLuong;
							if (hcItimesClass74.CuongHoaSoLuong >= 15)
							{
								hcItimesClass74.CuongHoaSoLuong = 15;
							}
							hcItimesClass74.ThietLap_GiaiDoanThuocTinh();
							if (hcItimesClass74.CuongHoaSoLuong >= 10)
							{
								SendNewsletter(BitConverter.ToInt32(hcItimesClass74.VatPham_id, 0), base.UserName, hcItimesClass74.CuongHoaSoLuong, base.Player_Zx);
							}
							if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass74.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass74.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass74.ItemGlobal_ID, 0))
							{
								SubtractItems(hcItimesClass74.Position, 1);
								AddItems(hcItimesClass74.ItemGlobal_ID, hcItimesClass74.VatPham_id, hcItimesClass74.Position, hcItimesClass74.VatPhamSoLuong, hcItimesClass74.VatPham_ThuocTinh);
							}
							Item_In_Bag[hcItimesClass74.Position].Khoa_Chat = false;
							RxjhClass.SyntheticRecord(base.Userid, base.UserName, value35.ItmeNAME, num, "CuongHoa", "Thanh Cong", Item_In_Bag[hcItimesClass74.Position], Item_In_Bag[hcItimesClass74.Position].FLD_CuongHoaSoLuong, numx);
							SynthesisHint(num, 1, Phi_HopThanh, Item_In_Bag[hcItimesClass74.Position]);
						}
						else
						{
							if (value35.FLD_RESIDE2 != 6 && World.SuKienCuongHoa == 1)
							{
								int num27 = GetParcelVacancy(this);
								if (num27 != -1 && hcItimesClass74.ThuocTinh1.SoLuong != 0 && hcItimesClass74.ThuocTinh2.SoLuong != 0 && hcItimesClass74.ThuocTinh3.SoLuong != 0 && hcItimesClass74.ThuocTinh4.SoLuong != 0)
								{
									if (hcItimesClass74.CuongHoaSoLuong == 7 && value35.FLD_LEVEL >= 60)
									{
										IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1008000748), num27, BitConverter.GetBytes(1), new byte[56]);
									}
									else if (hcItimesClass74.CuongHoaSoLuong == 8 && value35.FLD_LEVEL >= 60)
									{
										IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1008000749), num27, BitConverter.GetBytes(1), new byte[56]);
									}
									else if (hcItimesClass74.CuongHoaSoLuong == 9 && value35.FLD_LEVEL >= 60)
									{
										IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1008000750), num27, BitConverter.GetBytes(1), new byte[56]);
									}
								}
							}
							else if (value35.FLD_RESIDE2 == 6 && World.SuKienCuongHoa == 1)
							{
								int num28 = GetParcelVacancy(this);
								if (num28 != -1 && hcItimesClass74.ThuocTinh1.SoLuong != 0 && hcItimesClass74.ThuocTinh2.SoLuong != 0)
								{
									if (hcItimesClass74.CuongHoaSoLuong == 7 && value35.FLD_LEVEL >= 60)
									{
										IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1008000748), num28, BitConverter.GetBytes(1), new byte[56]);
									}
									else if (hcItimesClass74.CuongHoaSoLuong == 8 && value35.FLD_LEVEL >= 60)
									{
										IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1008000749), num28, BitConverter.GetBytes(1), new byte[56]);
									}
									else if (hcItimesClass74.CuongHoaSoLuong == 9 && value35.FLD_LEVEL >= 60)
									{
										IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1008000750), num28, BitConverter.GetBytes(1), new byte[56]);
									}
								}
							}
							SynthesisHint(num, 0, Phi_HopThanh, Item_In_Bag[hcItimesClass74.Position]);
							RxjhClass.SyntheticRecord(base.Userid, base.UserName, value35.ItmeNAME, num, "CuongHoa", "That Bai", Item_In_Bag[hcItimesClass74.Position], -1, numx);
							SubtractItems(hcItimesClass74.Position, 1);
						}
						SubtractItems(hcItimesClass75.Position, 1);
						if (hcItimesClass76 != null)
						{
							SubtractItems(hcItimesClass76.Position, 1);
						}
						if (hcItimesClass77 != null)
						{
							SubtractItems(hcItimesClass77.Position, 1);
						}
						if (hcItimesClass78 != null)
						{
							SubtractItems(hcItimesClass78.Position, 1);
						}
						HopThanhVatPham_Table.Clear();
						SynthesisSystemUnlocked();
						NguyenBao_HopThanh_MoRa = 0;
						Phi_HopThanh = 0;
					}
					end_IL_1b972:;
				}
				catch (Exception ex39)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成 error![" + base.Userid + "]-[" + base.UserName + "]" + ex39.Message);
				}
				goto default;
			case 120:
				if (base.CurrentOperationType == 37)
				{
					int num5 = PacketData[14];
					int num6 = PacketData[26];
					if (Item_In_Bag[num5].GetVatPham_ID != 0L && Item_In_Bag[num6].GetVatPham_ID != 0)
					{
						if (!World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[num5].VatPham_ID, 0), out var value9))
						{
							SynthesisHint(num, 0, 0, Item_In_Bag[num5]);
						}
						else if (value9.FLD_RESIDE2 != 12)
						{
							SynthesisHint(num, 0, 0, Item_In_Bag[num5]);
						}
						else if (!World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[num6].VatPham_ID, 0), out value9))
						{
							SynthesisHint(num, 0, 0, Item_In_Bag[num6]);
						}
						else if (value9.FLD_RESIDE2 != 14)
						{
							SynthesisHint(num, 0, 0, Item_In_Bag[num5]);
						}
						else if (!Item_In_Bag[num5].Vat_Pham_Khoa_Lai && !Item_In_Bag[num6].Vat_Pham_Khoa_Lai)
						{
							if (Item_In_Bag[num5].FLD_MAGIC1 != 0 && Item_In_Bag[num5].FLD_MAGIC2 != 0 && Item_In_Bag[num5].FLD_MAGIC3 != 0 && Item_In_Bag[num5].FLD_MAGIC4 != 0)
							{
								if (Item_In_Bag[num6].FLD_MAGIC1 == 0 && Item_In_Bag[num6].FLD_MAGIC2 == 0 && Item_In_Bag[num6].FLD_MAGIC3 == 0 && Item_In_Bag[num6].FLD_MAGIC4 == 0)
								{
									int num7 = GetParcelVacancyPosition();
									if (num7 == -1)
									{
										HeThongNhacNho("Không còn chỗ trống", 10, "Túi đồ");
									}
									else
									{
										byte[] array36 = new byte[56];
										System.Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array36, 0, 4);
										System.Buffer.BlockCopy(BitConverter.GetBytes(Item_In_Bag[num5].FLD_MAGIC1), 0, array36, 4, 4);
										System.Buffer.BlockCopy(BitConverter.GetBytes(Item_In_Bag[num5].FLD_MAGIC2), 0, array36, 8, 4);
										System.Buffer.BlockCopy(BitConverter.GetBytes(Item_In_Bag[num5].FLD_MAGIC3), 0, array36, 12, 4);
										System.Buffer.BlockCopy(BitConverter.GetBytes(Item_In_Bag[num5].FLD_MAGIC4), 0, array36, 16, 4);
										AddItems(Item_In_Bag[num6].ItemGlobal_ID, BitConverter.GetBytes(Item_In_Bag[num6].GetVatPham_ID), num7, BitConverter.GetBytes(1), array36);
										SubtractItems(num5, 1);
										SubtractItems(num6, 1);
										SynthesisHint(num, 1, 0, Item_In_Bag[num6]);
									}
								}
								else
								{
									SynthesisHint(num, 0, 0, Item_In_Bag[num5]);
								}
							}
							else
							{
								SynthesisHint(num, 0, 0, Item_In_Bag[num5]);
							}
						}
						else
						{
							SynthesisHint(num, 0, 0, Item_In_Bag[num5]);
						}
					}
					else
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[num5]);
					}
				}
				goto default;
			case 319:
				try
				{
					if (HopThanhVatPham_Table.TryGetValue(1, out var value3))
					{
						Item_In_Bag[value3.Position].Khoa_Chat = false;
						HopThanhVatPham_Table.Clear();
						SynthesisSystemUnlocked();
					}
					NguyenBao_HopThanh_MoRa = 0;
					OpenWarehouse = false;
					SynthesisHint(num, 0, 0, Item_In_Bag[fromPos]);
					break;
				}
				catch (Exception ex12)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 披风分解取消 210 error![" + base.Userid + "]-[" + base.UserName + "]" + ex12.Message);
					break;
				}
			case 299:
				try
				{
					if (HopThanhVatPham_Table.TryGetValue(1, out var value91))
					{
						Item_In_Bag[value91.Position].Khoa_Chat = false;
					}
					HopThanhVatPham_Table.Clear();
					SynthesisSystemUnlocked();
					NguyenBao_HopThanh_MoRa = 0;
					OpenWarehouse = false;
				}
				catch (Exception ex11)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成4 error![" + base.Userid + "]-[" + base.UserName + "]" + ex11.Message);
				}
				goto default;
			case 300:
				try
				{
					if (base.CurrentOperationType == 300 && HopThanhVatPham_Table.Count > 0)
					{
						if (base.Player_Money < 10000000)
						{
							SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
						}
						else
						{
							base.Player_Money -= 10000000L;
							UpdateMoneyAndWeight();
							HcItimesClass hcItimesClass124 = null;
							HcItimesClass hcItimesClass143 = null;
							if (HopThanhVatPham_Table.ContainsKey(1))
							{
								hcItimesClass124 = HopThanhVatPham_Table[1];
							}
							if (HopThanhVatPham_Table.ContainsKey(2))
							{
								hcItimesClass143 = HopThanhVatPham_Table[2];
							}
							if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass124.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass124.ItemGlobal_ID, 0) && (hcItimesClass143 == null || BitConverter.ToInt64(Item_In_Bag[hcItimesClass143.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass143.ItemGlobal_ID, 0)) && World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass124.VatPham_id, 0), out var value80) && (value80.FLD_RESIDE2 == 1 || value80.FLD_RESIDE2 == 4) && !Item_In_Bag[hcItimesClass124.Position].Vat_Pham_Khoa_Lai)
							{
								byte[] array35 = new byte[4];
								System.Buffer.BlockCopy(PacketData, 14, array35, 0, 4);
								int num2 = BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array35, 0)].VatPham_ID, 0);
								if (num2 != 0)
								{
									Random random9 = new Random();
									string[] array10 = World.TongXacSuat_TuLinh.Split(';');
									double num3 = random9.Next(int.Parse(array10[0]), int.Parse(array10[1]));
									double num4 = 10.0;
									if (hcItimesClass143 != null)
									{
										num3 += (double)LuckyCharmBonus(BitConverter.ToInt32(hcItimesClass143.VatPham_id, 0));
									}
									if (NguyenBao_HopThanh_MoRa == 1)
									{
										num3 += 5.0;
									}
									if (base.FLD_VIP == 1)
									{
									}
									if (base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
									{
										num3 += 100.0 * base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
									}
									if (base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
									{
										num3 += 100.0 * base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
									}
									if (num3 >= num4)
									{
										int fLD_FJ_四神之力 = 0;
										switch (num2)
										{
										case 1000001122:
											fLD_FJ_四神之力 = 1;
											break;
										case 1000001123:
											fLD_FJ_四神之力 = 2;
											break;
										case 1000001124:
											fLD_FJ_四神之力 = 3;
											break;
										case 1000001125:
											fLD_FJ_四神之力 = 4;
											break;
										}
										hcItimesClass124.FLD_FJ_SucManh4ViThan = fLD_FJ_四神之力;
										SynthesisHint(num, 1, 10000000, Item_In_Bag[hcItimesClass124.Position]);
										RxjhClass.SyntheticRecord(base.Userid, base.UserName, value80.ItmeNAME, num, "OptTuLinh", "Thanh Cong", Item_In_Bag[hcItimesClass124.Position]);
									}
									else
									{
										SynthesisHint(num, 6, 0, Item_In_Bag[hcItimesClass124.Position]);
										RxjhClass.SyntheticRecord(base.Userid, base.UserName, value80.ItmeNAME, num, "OptTuLinh", "That Bai", Item_In_Bag[hcItimesClass124.Position]);
									}
									if (hcItimesClass143 != null)
									{
										SubtractItems(hcItimesClass143.Position, 1);
									}
									if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass124.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass124.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass124.ItemGlobal_ID, 0))
									{
										SubtractItems(hcItimesClass124.Position, 1);
										Gia_tang_vat_pham_Bon_than(hcItimesClass124.ItemGlobal_ID, hcItimesClass124.VatPham_id, hcItimesClass124.Position, hcItimesClass124.VatPhamSoLuong, hcItimesClass124.VatPham_ThuocTinh, hcItimesClass124.FLD_FJ_SucManh4ViThan);
									}
									Item_In_Bag[hcItimesClass124.Position].Khoa_Chat = false;
									SubtractItems(BitConverter.ToInt32(array35, 0), 1);
									HopThanhVatPham_Table.Clear();
									SynthesisSystemUnlocked();
									NguyenBao_HopThanh_MoRa = 0;
									OpenWarehouse = false;
								}
							}
						}
					}
				}
				catch (Exception ex10)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 合成ThuocTinhGiaiDoan 合成 error![" + base.Userid + "]-[" + base.UserName + "]" + ex10.Message);
				}
				goto default;
			case 301:
				try
				{
					if (base.CurrentOperationType == 300 && !HopThanhVatPham_Table.ContainsKey(1) && !Item_In_Bag[fromPos].Khoa_Chat && !Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
					{
						ItmeClass value66;
						if (base.Player_Money < 10000000)
						{
							SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
						}
						else if (Item_In_Bag[fromPos].FLD_FJ_LowSoul == 0)
						{
							SynthesisHint(num, 4, 0, Item_In_Bag[fromPos]);
						}
						else if (Item_In_Bag[fromPos].FLD_TuLinh != 0)
						{
							SynthesisHint(num, 7, 0, Item_In_Bag[fromPos]);
						}
						else if (World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0), out value66) && (value66.FLD_RESIDE2 == 1 || value66.FLD_RESIDE2 == 4))
						{
							HcItimesClass hcItimesClass110 = new HcItimesClass();
							hcItimesClass110.Position = fromPos;
							hcItimesClass110.VatPham = Item_In_Bag[fromPos].VatPham_byte;
							Item_In_Bag[fromPos].Khoa_Chat = true;
							HopThanhVatPham_Table.Add(1, hcItimesClass110);
							SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
						}
					}
				}
				catch (Exception ex2)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成1 error![" + base.Userid + "]-[" + base.UserName + "]" + ex2.Message);
				}
				goto default;
			case 302:
				if (base.CurrentOperationType == 300)
				{
					int num91 = BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0);
					if ((uint)(num91 - 1000001122) <= 3u)
					{
						if (HopThanhVatPham_Table.Count != 0)
						{
							HopThanhVatPham_Table.Clear();
						}
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
						Item_In_Bag[fromPos].Khoa_Chat = true;
						OpenWarehouse = true;
					}
					break;
				}
				goto default;
			case 303:
				try
				{
					if (base.CurrentOperationType == 300 && HopThanhVatPham_Table != null && HopThanhVatPham_Table.Count != 0 && HopThanhVatPham_Table.ContainsKey(1) && !HopThanhVatPham_Table.ContainsKey(2))
					{
						HopThanhVatPham_Table.Add(2, new HcItimesClass
						{
							Position = fromPos,
							VatPham = Item_In_Bag[fromPos].VatPham_byte
						});
						Item_In_Bag[fromPos].Khoa_Chat = true;
						SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
					}
				}
				catch (Exception ex65)
				{
					Form1.WriteLine(1, "合成系统 合成ThuocTinh 合成3 error![" + base.Userid + "]-[" + base.UserName + "]" + ex65.Message);
				}
				goto default;
			case 309:
				try
				{
					NguyenBao_HopThanh_MoRa = 0;
					OpenWarehouse = false;
					HopThanhVatPham_Table.Clear();
					System.Buffer.BlockCopy(array24, 0, array, 11, 2);
					SynthesisSystemUnlocked();
				}
				catch (Exception ex54)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 再造合成石-取消 error![" + base.Userid + "]-[" + base.UserName + "]" + ex54.Message);
					break;
				}
				goto default;
			case 310:
				try
				{
					if (!HopThanhVatPham_Table.TryGetValue(1, out var hcItimesClass3) || !World.Itme.TryGetValue((int)Item_In_Bag[hcItimesClass3.Position].GetVatPham_ID, out var itmeClass) || Item_In_Bag[hcItimesClass3.Position].Vat_Pham_Khoa_Lai)
					{
						break;
					}
					int offsetValue = 0;
					if (itmeClass.FLD_RESIDE2 != 2 && itmeClass.FLD_RESIDE2 != 5 && itmeClass.FLD_RESIDE2 != 6 && itmeClass.FLD_RESIDE2 != 7 && itmeClass.FLD_RESIDE2 != 8 && itmeClass.FLD_RESIDE2 != 10)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
						break;
					}
					offsetValue = ((itmeClass.FLD_RESIDE1 == 11) ? ((itmeClass.FLD_ZX != 1) ? (-100) : 900) : ((itmeClass.FLD_ZX != 1) ? 100 : (-900)));
					if (itmeClass.FLD_PID == 100018)
					{
						offsetValue = 99;
					}
					else if (itmeClass.FLD_PID == 100117)
					{
						offsetValue = -99;
					}
					if (itmeClass.FLD_PID == 100020)
					{
						offsetValue = 98;
					}
					else if (itmeClass.FLD_PID == 100118)
					{
						offsetValue = -98;
					}
					if (itmeClass.FLD_PID == 100022)
					{
						offsetValue = 98;
					}
					else if (itmeClass.FLD_PID == 100120)
					{
						offsetValue = -98;
					}
					if (itmeClass.FLD_PID == 100026)
					{
						offsetValue = 98;
					}
					else if (itmeClass.FLD_PID == 100124)
					{
						offsetValue = -98;
					}
					if (itmeClass.FLD_PID == 100021)
					{
						offsetValue = 98;
					}
					else if (itmeClass.FLD_PID == 100119)
					{
						offsetValue = -98;
					}
					if (itmeClass.FLD_PID == 100028)
					{
						offsetValue = 97;
					}
					else if (itmeClass.FLD_PID == 100125)
					{
						offsetValue = -97;
					}
					if (itmeClass.FLD_PID == 100029)
					{
						offsetValue = 97;
					}
					else if (itmeClass.FLD_PID == 100126)
					{
						offsetValue = -97;
					}
					if (itmeClass.FLD_PID == 700911)
					{
						offsetValue = 10;
					}
					else if (itmeClass.FLD_PID == 700921)
					{
						offsetValue = -10;
					}
					if (World.Itme.TryGetValue(itmeClass.FLD_PID + offsetValue, out var _))
					{
						X_Vat_Pham_Loai ItemClass = DatDuocVatPhamLoaiHinh(1008001753L);
						if (ItemClass != null)
						{
							SubtractItems(fromBag, 1);
							SubtractItems(hcItimesClass3.Position, 1);
							AddItems(hcItimesClass3.ItemGlobal_ID, BitConverter.GetBytes(BitConverter.ToInt32(hcItimesClass3.VatPham_id, 0) + offsetValue), hcItimesClass3.Position, hcItimesClass3.VatPhamSoLuong, hcItimesClass3.VatPham_ThuocTinh);
							OpenWarehouse = false;
							HopThanhVatPham_Table.Clear();
							NguyenBao_HopThanh_MoRa = 0;
							SynthesisSystemUnlocked();
							SynthesisHint(num, 1, 0, Item_In_Bag[hcItimesClass3.Position]);
						}
					}
					break;
				}
				catch (Exception ex43)
				{
					Form1.WriteLine(1, "Luyện Kim Thuật 310 error ![" + base.Userid + "]-[" + base.UserName + "]" + ex43.Message);
					break;
				}
			case 311:
				try
				{
					if (!World.Itme.TryGetValue((int)Item_In_Bag[fromPos].GetVatPham_ID, out var itmeClass2) || Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
					{
						break;
					}
					if (itmeClass2.FLD_RESIDE2 != 2 && itmeClass2.FLD_RESIDE2 != 5 && itmeClass2.FLD_RESIDE2 != 6 && itmeClass2.FLD_RESIDE2 != 7 && itmeClass2.FLD_RESIDE2 != 8 && itmeClass2.FLD_RESIDE2 != 10)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
						break;
					}
					int offsetValue2 = 0;
					offsetValue2 = ((itmeClass2.FLD_RESIDE1 == 11) ? ((itmeClass2.FLD_ZX != 1) ? (-100) : 900) : ((itmeClass2.FLD_ZX != 1) ? 100 : (-900)));
					if (itmeClass2.FLD_PID == 100018)
					{
						offsetValue2 = 99;
					}
					else if (itmeClass2.FLD_PID == 100117)
					{
						offsetValue2 = -99;
					}
					if (itmeClass2.FLD_PID == 100020)
					{
						offsetValue2 = 98;
					}
					else if (itmeClass2.FLD_PID == 100118)
					{
						offsetValue2 = -98;
					}
					if (itmeClass2.FLD_PID == 100021)
					{
						offsetValue2 = 98;
					}
					else if (itmeClass2.FLD_PID == 100119)
					{
						offsetValue2 = -98;
					}
					if (itmeClass2.FLD_PID == 100022)
					{
						offsetValue2 = 98;
					}
					else if (itmeClass2.FLD_PID == 100120)
					{
						offsetValue2 = -98;
					}
					if (itmeClass2.FLD_PID == 100026)
					{
						offsetValue2 = 98;
					}
					else if (itmeClass2.FLD_PID == 100124)
					{
						offsetValue2 = -98;
					}
					if (itmeClass2.FLD_PID == 100028)
					{
						offsetValue2 = 97;
					}
					else if (itmeClass2.FLD_PID == 100125)
					{
						offsetValue2 = -97;
					}
					if (itmeClass2.FLD_PID == 100029)
					{
						offsetValue2 = 97;
					}
					else if (itmeClass2.FLD_PID == 100126)
					{
						offsetValue2 = -97;
					}
					if (itmeClass2.FLD_PID == 700911)
					{
						offsetValue2 = 10;
					}
					else if (itmeClass2.FLD_PID == 700921)
					{
						offsetValue2 = -10;
					}
					if (World.Itme.TryGetValue(itmeClass2.FLD_PID + offsetValue2, out var itmeClass4))
					{
						if (HopThanhVatPham_Table.Count != 0)
						{
							HopThanhVatPham_Table.Clear();
						}
						HcItimesClass hcItimesClass2 = new HcItimesClass();
						hcItimesClass2.Position = fromPos;
						hcItimesClass2.VatPham = Item_In_Bag[fromPos].VatPham_byte;
						if (!HopThanhVatPham_Table.ContainsKey(1))
						{
							HopThanhVatPham_Table.Add(1, hcItimesClass2);
						}
						AlchemySynthesisHint(num, 1, 0, Item_In_Bag[fromPos], itmeClass4.FLD_PID);
					}
					break;
				}
				catch (Exception ex31)
				{
					string[] obj2 = new string[6]
					{
						"Luyện Kim Thuật 311 error",
						base.Client.WorldId.ToString(),
						"|",
						base.Client.ToString(),
						" ",
						null
					};
					obj2[5] = ex31?.ToString();
					Form1.WriteLine(1, string.Concat(obj2));
					break;
				}
				case 610:
					OpenWarehouse = false;
					System.Buffer.BlockCopy(BitConverter.GetBytes(base.CharacterFullServerID), 0, array, 4, 2);
					if (base.Client != null)
					{
						base.Client.Send_Map_Data(array, array.Length);
					}
				break;
				//case 511:
    //                HeThongNhacNho("CH Moi DKT");
				//	try
				//	{
				//		if ((base.CurrentOperationType == 8 || base.CurrentOperationType == 323) && !HopThanhVatPham_Table.ContainsKey(1))
				//		{
				//			ItmeClass value37;
				//			if (Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
				//			{
				//				SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
				//			}
				//			else if (World.Itme.TryGetValue((int)Item_In_Bag[fromPos].GetVatPham_ID, out value37))
				//			{
				//				if (value37.FLD_RESIDE2 == 12)
				//				{
				//					SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
				//				}
				//				else if (value37.FLD_LEVEL < 120 && Item_In_Bag[fromPos].FLD_CuongHoaSoLuong == 10)
				//				{
				//					SynthesisHint(num, 5, 0, Item_In_Bag[fromPos]);
				//				}
				//				else if (Item_In_Bag[fromPos].FLD_CuongHoaSoLuong < 15)
				//				{
				//					int SoTien = (Phi_HopThanh = CalculateSyntheticEnhancementCost(value37, fromPos, num));
				//					HcItimesClass hcItimesClass83 = new HcItimesClass();
				//					hcItimesClass83.Position = fromPos;
				//					hcItimesClass83.VatPham = Item_In_Bag[fromPos].VatPham_byte;
				//					Item_In_Bag[fromPos].Khoa_Chat = true;
				//					HopThanhVatPham_Table.Add(1, hcItimesClass83);
				//					SynthesisHint(num, 1, SoTien, Item_In_Bag[fromPos]);
				//				}
				//			}
				//		}
				//	}
				//	catch (Exception ex42)
				//	{
				//		Form1.WriteLine(1, "CuongHoaNPC input 1 error![" + base.Userid + "]-[" + base.UserName + "]" + ex42.Message);
				//	}
				//	// Cường hóa mới đao kiếm tiếu
				//	break;
			default:
				if (num != 0)
				{
					System.Buffer.BlockCopy(BitConverter.GetBytes(base.CharacterFullServerID), 0, array, 4, 2);
					if (base.Client != null)
					{
						base.Client.Send_Map_Data(array, array.Length);
					}
				}
				break;
			case 371:
				try
				{
					if (HopThanhVatPham_Table.ContainsKey(1))
					{
						break;
					}
					if (Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
					}
					else
					{
						if (!World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0), out var value19))
						{
							break;
						}
						if (value19.FLD_RESIDE2 != 16)
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
							break;
						}
						int num73 = BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0);
						if ((uint)(num73 - 1000001170) <= 5u)
						{
							int num83 = num73 + 6;
							if ((uint)(num83 - 1000001176) <= 5u)
							{
								HcItimesClass hcItimesClass56 = new HcItimesClass();
								hcItimesClass56.Position = fromPos;
								hcItimesClass56.VatPham = Item_In_Bag[fromPos].VatPham_byte;
								Item_In_Bag[fromPos].Khoa_Chat = true;
								HopThanhVatPham_Table.Add(1, hcItimesClass56);
								SynthesisHint(num, 1, 0, Item_In_Bag[fromPos]);
								AlchemySynthesisHint(num, 1, 0, Item_In_Bag[fromPos], num83);
							}
						}
					}
					break;
				}
				catch (Exception ex19)
				{
					string[] obj = new string[6]
					{
						"211披风分解 error",
						base.Client.WorldId.ToString(),
						"|",
						base.Client.ToString(),
						" ",
						null
					};
					obj[5] = ex19?.ToString();
					Form1.WriteLine(1, string.Concat(obj));
					break;
				}
		case 614:
            try
				{
					byte[] array31;
					int num70;
					HcItimesClass hcItimesClass6;
					HcItimesClass hcItimesClass7;
					HcItimesClass hcItimesClass8;
					HcItimesClass hcItimesClass9;
					HcItimesClass hcItimesClass10;
					ItmeClass value84;
					if (base.CurrentOperationType == 101 || base.CurrentOperationType == 324)
					{
						array31 = new byte[4];
						byte[] dst4 = new byte[4];
						System.Buffer.BlockCopy(PacketData, 14, array31, 0, 4);
						System.Buffer.BlockCopy(PacketData, 18, dst4, 0, 4);
						num70 = BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array31, 0)].VatPham_ID, 0);
						if (num70 != 0 && HopThanhVatPham_Table.Count > 0)
						{
							hcItimesClass6 = null;
							hcItimesClass7 = null;
							hcItimesClass8 = null;
							hcItimesClass9 = null;
							hcItimesClass10 = null;
							if (HopThanhVatPham_Table.ContainsKey(1))
							{
								hcItimesClass6 = HopThanhVatPham_Table[1];
							}
							if (HopThanhVatPham_Table.ContainsKey(2))
							{
								hcItimesClass7 = HopThanhVatPham_Table[2];
							}
							if (HopThanhVatPham_Table.ContainsKey(3))
							{
								hcItimesClass8 = HopThanhVatPham_Table[3];
							}
							if (HopThanhVatPham_Table.ContainsKey(4))
							{
								hcItimesClass9 = HopThanhVatPham_Table[4];
							}
							if (HopThanhVatPham_Table.ContainsKey(5))
							{
								hcItimesClass10 = HopThanhVatPham_Table[5];
							}
							if (BitConverter.ToInt64(Item_In_Bag[hcItimesClass6.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass6.ItemGlobal_ID, 0))
							{
								SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
								HopThanhVatPham_Table.Clear();
							}
							else if (hcItimesClass7 != null)
							{
								if (hcItimesClass7 != null && BitConverter.ToInt64(Item_In_Bag[hcItimesClass7.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass7.ItemGlobal_ID, 0))
								{
									SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass7.Position]);
									HopThanhVatPham_Table.Clear();
								}
								else if (hcItimesClass8 != null && BitConverter.ToInt64(Item_In_Bag[hcItimesClass8.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass8.ItemGlobal_ID, 0))
								{
									SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass8.Position]);
									HopThanhVatPham_Table.Clear();
								}
								else if (hcItimesClass9 != null && BitConverter.ToInt64(Item_In_Bag[hcItimesClass9.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass9.ItemGlobal_ID, 0))
								{
									SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass9.Position]);
									HopThanhVatPham_Table.Clear();
								}
								else if (hcItimesClass10 != null && BitConverter.ToInt64(Item_In_Bag[hcItimesClass10.Position].ItemGlobal_ID, 0) != BitConverter.ToInt64(hcItimesClass10.ItemGlobal_ID, 0))
								{
									SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass10.Position]);
									HopThanhVatPham_Table.Clear();
								}
								else
								{
									hcItimesClass6.DatDuocThuocTinh();
									hcItimesClass6.CuongHoaThuocTinhGiaiDoan();
									if (!World.Itme.TryGetValue(BitConverter.ToInt32(hcItimesClass6.VatPham_id, 0), out value84))
									{
										SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
										HopThanhVatPham_Table.Clear();
									}
									else if (value84.FLD_RESIDE2 != 1 && value84.FLD_RESIDE2 != 2 && value84.FLD_RESIDE2 != 4 && value84.FLD_RESIDE2 != 5 && value84.FLD_RESIDE2 != 6 && value84.FLD_RESIDE2 != 14 && value84.FLD_RESIDE2 != 23 && value84.FLD_RESIDE2 != 24 && value84.FLD_RESIDE2 != 25)
									{
										SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
										HopThanhVatPham_Table.Clear();
									}
									else if (value84.FLD_RESIDE2 == 4)
									{
										if (num70 == ********** || num70 == 1008000112 || num70 == 1008000113 || num70 == 1008001057 || num70 == **********)
										{
											goto IL_321d;
										}
										SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
									}
									else
									{
										if (num70 == ********** || num70 == 1008000115 || num70 == 1008000116 || num70 == 1008001058 || num70 == **********)
										{
											goto IL_321d;
										}
										SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
									}
								}
							}
						}
					}
					goto end_IL_2d3f;
					IL_321d:
					if ((num70 == ********** || num70 == **********) && Item_In_Bag[hcItimesClass6.Position].FLD_CuongHoaSoLuong != 5)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
					}
					else if ((num70 == ********** || num70 == ********** || num70 == 1008000113 || num70 == 1008000116 || num70 == 1008000112 || num70 == 1008000115) && Item_In_Bag[hcItimesClass6.Position].FLD_CuongHoaSoLuong >= 10)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
					}
					else if ((num70 == 1008001057 || num70 == 1008001058) && Item_In_Bag[hcItimesClass6.Position].FLD_CuongHoaSoLuong < 10)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
					}
					else if (Item_In_Bag[hcItimesClass6.Position].Vat_Pham_Khoa_Lai)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
						HopThanhVatPham_Table.Clear();
					}
					else if (hcItimesClass6.CuongHoaSoLuong >= 15)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
						HopThanhVatPham_Table.Clear();
					}
					else if (value84.FLD_LEVEL >= 130)
					{
						if (hcItimesClass7 != null && BitConverter.ToInt32(hcItimesClass7.VatPham_id, 0) != 800000060)
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
							HopThanhVatPham_Table.Clear();
						}
						else if (hcItimesClass8 != null && BitConverter.ToInt32(hcItimesClass8.VatPham_id, 0) != 800000060)
						{
							SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
							HopThanhVatPham_Table.Clear();
						}
						else
						{
							if (hcItimesClass9 == null || BitConverter.ToInt32(hcItimesClass9.VatPham_id, 0) == 800000060)
							{
								goto IL_47ce;
							}
							SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
							HopThanhVatPham_Table.Clear();
						}
					}
					else if (hcItimesClass7 != null && BitConverter.ToInt32(hcItimesClass7.VatPham_id, 0) != 800000006)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
						HopThanhVatPham_Table.Clear();
					}
					else if (hcItimesClass8 != null && BitConverter.ToInt32(hcItimesClass8.VatPham_id, 0) != 800000006)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
						HopThanhVatPham_Table.Clear();
					}
					else
					{
						if (hcItimesClass9 == null || BitConverter.ToInt32(hcItimesClass9.VatPham_id, 0) == 800000006)
						{
							goto IL_47ce;
						}
						SynthesisHint(num, 2, 0, Item_In_Bag[hcItimesClass6.Position]);
						HopThanhVatPham_Table.Clear();
					}
					goto end_IL_2d3f;
					IL_47ce:
					if (Phi_HopThanh <= 0)
					{
						goto IL_35b3;
					}
					if (value84.FLD_NJ == 0)
					{
						if (base.Player_Money >= Phi_HopThanh)
						{
							base.Player_Money -= Phi_HopThanh;
							UpdateMoneyAndWeight();
							goto IL_35b3;
						}
					}
					else if (base.Player_Money >= Phi_HopThanh)
					{
						hcItimesClass6.FLD_FJ_NJ = 0;
						base.Player_Money -= Phi_HopThanh;
						UpdateMoneyAndWeight();
						Update_Item_In_Bag();
						goto IL_35b3;
					}
					SynthesisHint(11, 4, 0, Item_In_Bag[hcItimesClass6.Position]);
					HopThanhVatPham_Table.Clear();
					goto end_IL_2d3f;
					IL_35b3:
					double num71 = new Random((int)DateTime.Now.Ticks).Next(1, 110);
					int i = 0;
					while (i < new Random((int)DateTime.Now.Ticks).Next(5, 15))
					{
						num71 = new Random(World.GetRandomSeed()).Next(1, 110);
						int giaiDoanSoLuong = i + 1;
						i = giaiDoanSoLuong;
					}
					if (hcItimesClass10 != null)
					{
						num71 += (double)LuckyCharmBonus(BitConverter.ToInt32(hcItimesClass10.VatPham_id, 0));
					}
					if (NguyenBao_HopThanh_MoRa == 1)
					{
						num71 += 5.0;
					}
					if (base.FLD_VIP == 1)
					{
					}
					if (World.TyLe_CuongHoa_ToiCao != 0.0)
					{
						num71 += World.TyLe_CuongHoa_ToiCao;
					}
					if (base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
					{
						num71 += 100.0 * base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
					}
					if (base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
					{
						num71 += 100.0 * base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
					}
					if (hcItimesClass6.ThuocTinh1.ThuocTinhSoLuong + hcItimesClass6.ThuocTinh2.ThuocTinhSoLuong != 0 && hcItimesClass8 == null)
					{
						HeThongNhacNho("Tảng đá thả thiếu đi ít nhất đê\u0301n thả 2 cái.");
					}
					else if (hcItimesClass6.ThuocTinh1.ThuocTinhSoLuong + hcItimesClass6.ThuocTinh2.ThuocTinhSoLuong != 0 && hcItimesClass6.ThuocTinh3.ThuocTinhSoLuong + hcItimesClass6.ThuocTinh4.ThuocTinhSoLuong != 0 && hcItimesClass9 == null)
					{
						HeThongNhacNho("Tảng đá thả thiếu đi ít nhất đê\u0301n thả 3 cái.");
					}
					else
					{
						int numx2 = 0;
						int num_value2 = Item_In_Bag[hcItimesClass6.Position].FLD_MAGIC0;
						numx2 = num_value2;
						int num92 = num70;
						int num93 = num92;
						if (num70 == ********** || num70 == ********** || num70 == ********** || num70 == **********)
						{
							num71 += World.TyLe_TangCuong_VatPham;
						}
						if ((hcItimesClass6.CuongHoaSoLuong == 0 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh1) || (hcItimesClass6.CuongHoaSoLuong == 1 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh2) || (hcItimesClass6.CuongHoaSoLuong == 2 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh3) || (hcItimesClass6.CuongHoaSoLuong == 3 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh4) || (hcItimesClass6.CuongHoaSoLuong == 4 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh5) || (hcItimesClass6.CuongHoaSoLuong == 5 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh6) || (hcItimesClass6.CuongHoaSoLuong == 6 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh7) || (hcItimesClass6.CuongHoaSoLuong == 7 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh8) || (hcItimesClass6.CuongHoaSoLuong == 8 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh9) || (hcItimesClass6.CuongHoaSoLuong == 9 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh10) || (hcItimesClass6.CuongHoaSoLuong == 10 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh11) || (hcItimesClass6.CuongHoaSoLuong == 11 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh12) || (hcItimesClass6.CuongHoaSoLuong == 12 && num71 >= 100.0 - World.ToiCaoLayNgoc_Manh13) || (hcItimesClass6.CuongHoaSoLuong == 13 && !(num71 < 100.0 - World.ToiCaoLayNgoc_Manh14)) || ((hcItimesClass6.CuongHoaSoLuong == 14 && !(num71 < 100.0 - World.ToiCaoLayNgoc_Manh15)) ? true : false))
						{
							hcItimesClass6.CuongHoaLoaiHinh = ((value84.FLD_RESIDE2 == 4) ? 1 : 2);
							switch (BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array31, 0)].VatPham_ID, 0))
							{
							case **********:
								hcItimesClass6.CuongHoaSoLuong++;
								break;
							case 1008000112:
							{
								HcItimesClass hcItimesClass156 = hcItimesClass6;
								int giaiDoanSoLuong = hcItimesClass156.CuongHoaSoLuong + 1;
								hcItimesClass156.CuongHoaSoLuong = giaiDoanSoLuong;
								break;
							}
							case 1008000113:
							{
								Random random6 = new Random(DateTime.Now.Millisecond);
								int random18 = random6.Next(1, 100);
								if (random18 > 50)
								{
									hcItimesClass6.CuongHoaSoLuong += 2;
								}
								else if (random18 > 80)
								{
									hcItimesClass6.CuongHoaSoLuong += 3;
								}
								else
								{
									hcItimesClass6.CuongHoaSoLuong++;
								}
								if (hcItimesClass6.CuongHoaSoLuong > 10)
								{
									hcItimesClass6.CuongHoaSoLuong = 10;
								}
								break;
							}
							case **********:
								hcItimesClass6.CuongHoaSoLuong++;
								break;
							case 1008000115:
							{
								HcItimesClass hcItimesClass156 = hcItimesClass6;
								int giaiDoanSoLuong = hcItimesClass156.CuongHoaSoLuong + 1;
								hcItimesClass156.CuongHoaSoLuong = giaiDoanSoLuong;
								break;
							}
							case 1008000116:
							{
								Random random5 = new Random(DateTime.Now.Millisecond);
								int random17 = random5.Next(1, 100);
								if (random17 > 50)
								{
									hcItimesClass6.CuongHoaSoLuong += 2;
								}
								else if (random17 > 80)
								{
									hcItimesClass6.CuongHoaSoLuong += 3;
								}
								else
								{
									hcItimesClass6.CuongHoaSoLuong++;
								}
								if (hcItimesClass6.CuongHoaSoLuong > 10)
								{
									hcItimesClass6.CuongHoaSoLuong = 10;
								}
								break;
							}
							case 1008000024:
							{
								HcItimesClass hcItimesClass156 = hcItimesClass6;
								int giaiDoanSoLuong = hcItimesClass156.CuongHoaSoLuong + 1;
								hcItimesClass156.CuongHoaSoLuong = giaiDoanSoLuong;
								break;
							}
							case 1008000023:
							{
								HcItimesClass hcItimesClass156 = hcItimesClass6;
								int giaiDoanSoLuong = hcItimesClass156.CuongHoaSoLuong + 1;
								hcItimesClass156.CuongHoaSoLuong = giaiDoanSoLuong;
								break;
							}
							case 1008001058:
							{
								HcItimesClass hcItimesClass156 = hcItimesClass6;
								int giaiDoanSoLuong = hcItimesClass156.CuongHoaSoLuong + 1;
								hcItimesClass156.CuongHoaSoLuong = giaiDoanSoLuong;
								break;
							}
							case 1008001057:
							{
								HcItimesClass hcItimesClass156 = hcItimesClass6;
								int giaiDoanSoLuong = hcItimesClass156.CuongHoaSoLuong + 1;
								hcItimesClass156.CuongHoaSoLuong = giaiDoanSoLuong;
								break;
							}
							case **********:
							{
								HcItimesClass hcItimesClass156 = hcItimesClass6;
								int giaiDoanSoLuong = hcItimesClass156.CuongHoaSoLuong + 1;
								hcItimesClass156.CuongHoaSoLuong = giaiDoanSoLuong;
								break;
							}
							case **********:
							{
								HcItimesClass hcItimesClass156 = hcItimesClass6;
								int giaiDoanSoLuong = hcItimesClass156.CuongHoaSoLuong + 1;
								hcItimesClass156.CuongHoaSoLuong = giaiDoanSoLuong;
								break;
							}
							}
							if (hcItimesClass6.CuongHoaSoLuong > 15)
							{
								hcItimesClass6.CuongHoaSoLuong = 15;
							}
							hcItimesClass6.ThietLap_GiaiDoanThuocTinh();
							SubtractItems(BitConverter.ToInt32(array31, 0), 1);
							if (hcItimesClass6.CuongHoaSoLuong >= 10)
							{
								SendNewsletter(BitConverter.ToInt32(hcItimesClass6.VatPham_id, 0), base.UserName, hcItimesClass6.CuongHoaSoLuong, base.Player_Zx);
							}
							if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass6.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass6.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass6.ItemGlobal_ID, 0))
							{
								SubtractItems(hcItimesClass6.Position, 1);
								AddItems(hcItimesClass6.ItemGlobal_ID, hcItimesClass6.VatPham_id, hcItimesClass6.Position, hcItimesClass6.VatPhamSoLuong, hcItimesClass6.VatPham_ThuocTinh);
							}
							Item_In_Bag[hcItimesClass6.Position].Khoa_Chat = false;
							RxjhClass.SyntheticRecord(base.Userid, base.UserName, value84.ItmeNAME, num, Item_In_Bag[BitConverter.ToInt32(array31, 0)].DatDuocVatPhamTen_XungHao(), "Thanh Cong", Item_In_Bag[hcItimesClass6.Position], Item_In_Bag[hcItimesClass6.Position].FLD_CuongHoaSoLuong, numx2);
							SynthesisHint(num, 1, Phi_HopThanh, Item_In_Bag[hcItimesClass6.Position]);
						}
						else
						{
							int capcuonghoa = 0;
							switch (BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array31, 0)].VatPham_ID, 0))
							{
							case 1008000024:
								hcItimesClass6.CuongHoaLoaiHinh = 0;
								hcItimesClass6.CuongHoaSoLuong = 0;
								capcuonghoa = 0;
								hcItimesClass6.ThietLap_GiaiDoanThuocTinh();
								if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass6.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass6.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass6.ItemGlobal_ID, 0))
								{
									SubtractItems(hcItimesClass6.Position, 1);
									AddItems(hcItimesClass6.ItemGlobal_ID, hcItimesClass6.VatPham_id, hcItimesClass6.Position, hcItimesClass6.VatPhamSoLuong, hcItimesClass6.VatPham_ThuocTinh);
								}
								break;
							case 1008000023:
								hcItimesClass6.CuongHoaLoaiHinh = 0;
								hcItimesClass6.CuongHoaSoLuong = 0;
								capcuonghoa = 0;
								hcItimesClass6.ThietLap_GiaiDoanThuocTinh();
								if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass6.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass6.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass6.ItemGlobal_ID, 0))
								{
									SubtractItems(hcItimesClass6.Position, 1);
									AddItems(hcItimesClass6.ItemGlobal_ID, hcItimesClass6.VatPham_id, hcItimesClass6.Position, hcItimesClass6.VatPhamSoLuong, hcItimesClass6.VatPham_ThuocTinh);
								}
								break;
							case 1008001058:
								hcItimesClass6.CuongHoaSoLuong -= 2;
								if (hcItimesClass6.CuongHoaSoLuong <= 0)
								{
									hcItimesClass6.CuongHoaSoLuong = 0;
									hcItimesClass6.CuongHoaLoaiHinh = 0;
								}
								capcuonghoa = hcItimesClass6.CuongHoaSoLuong;
								hcItimesClass6.ThietLap_GiaiDoanThuocTinh();
								if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass6.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass6.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass6.ItemGlobal_ID, 0))
								{
									SubtractItems(hcItimesClass6.Position, 1);
									AddItems(hcItimesClass6.ItemGlobal_ID, hcItimesClass6.VatPham_id, hcItimesClass6.Position, hcItimesClass6.VatPhamSoLuong, hcItimesClass6.VatPham_ThuocTinh);
								}
								break;
							case 1008001057:
								hcItimesClass6.CuongHoaSoLuong -= 2;
								if (hcItimesClass6.CuongHoaSoLuong <= 0)
								{
									hcItimesClass6.CuongHoaSoLuong = 0;
									hcItimesClass6.CuongHoaLoaiHinh = 0;
								}
								capcuonghoa = hcItimesClass6.CuongHoaSoLuong;
								hcItimesClass6.ThietLap_GiaiDoanThuocTinh();
								if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass6.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass6.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass6.ItemGlobal_ID, 0))
								{
									SubtractItems(hcItimesClass6.Position, 1);
									AddItems(hcItimesClass6.ItemGlobal_ID, hcItimesClass6.VatPham_id, hcItimesClass6.Position, hcItimesClass6.VatPhamSoLuong, hcItimesClass6.VatPham_ThuocTinh);
								}
								break;
							case 1008000112:
								hcItimesClass6.CuongHoaSoLuong -= 2;
								if (hcItimesClass6.CuongHoaSoLuong <= 0)
								{
									hcItimesClass6.CuongHoaSoLuong = 0;
									hcItimesClass6.CuongHoaLoaiHinh = 0;
								}
								capcuonghoa = hcItimesClass6.CuongHoaSoLuong;
								hcItimesClass6.ThietLap_GiaiDoanThuocTinh();
								if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass6.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass6.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass6.ItemGlobal_ID, 0))
								{
									SubtractItems(hcItimesClass6.Position, 1);
									AddItems(hcItimesClass6.ItemGlobal_ID, hcItimesClass6.VatPham_id, hcItimesClass6.Position, hcItimesClass6.VatPhamSoLuong, hcItimesClass6.VatPham_ThuocTinh);
								}
								break;
							case 1008000113:
								hcItimesClass6.CuongHoaLoaiHinh = 0;
								hcItimesClass6.CuongHoaSoLuong = 0;
								hcItimesClass6.ThietLap_GiaiDoanThuocTinh();
								capcuonghoa = hcItimesClass6.CuongHoaSoLuong;
								if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass6.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass6.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass6.ItemGlobal_ID, 0))
								{
									SubtractItems(hcItimesClass6.Position, 1);
									AddItems(hcItimesClass6.ItemGlobal_ID, hcItimesClass6.VatPham_id, hcItimesClass6.Position, hcItimesClass6.VatPhamSoLuong, hcItimesClass6.VatPham_ThuocTinh);
								}
								break;
							case 1008000115:
								hcItimesClass6.CuongHoaSoLuong -= 2;
								if (hcItimesClass6.CuongHoaSoLuong <= 0)
								{
									hcItimesClass6.CuongHoaSoLuong = 0;
									hcItimesClass6.CuongHoaLoaiHinh = 0;
								}
								hcItimesClass6.ThietLap_GiaiDoanThuocTinh();
								capcuonghoa = hcItimesClass6.CuongHoaSoLuong;
								if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass6.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass6.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass6.ItemGlobal_ID, 0))
								{
									SubtractItems(hcItimesClass6.Position, 1);
									AddItems(hcItimesClass6.ItemGlobal_ID, hcItimesClass6.VatPham_id, hcItimesClass6.Position, hcItimesClass6.VatPhamSoLuong, hcItimesClass6.VatPham_ThuocTinh);
								}
								break;
							case 1008000116:
								hcItimesClass6.CuongHoaLoaiHinh = 0;
								hcItimesClass6.CuongHoaSoLuong = 0;
								hcItimesClass6.ThietLap_GiaiDoanThuocTinh();
								capcuonghoa = hcItimesClass6.CuongHoaSoLuong;
								if (BitConverter.ToInt32(Item_In_Bag[hcItimesClass6.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[hcItimesClass6.Position].ItemGlobal_ID, 0) == BitConverter.ToInt64(hcItimesClass6.ItemGlobal_ID, 0))
								{
									SubtractItems(hcItimesClass6.Position, 1);
									AddItems(hcItimesClass6.ItemGlobal_ID, hcItimesClass6.VatPham_id, hcItimesClass6.Position, hcItimesClass6.VatPhamSoLuong, hcItimesClass6.VatPham_ThuocTinh);
								}
								break;
							}
							RxjhClass.SyntheticRecord(base.Userid, base.UserName, value84.ItmeNAME, num, Item_In_Bag[BitConverter.ToInt32(array31, 0)].DatDuocVatPhamTen_XungHao(), "That Bai", Item_In_Bag[hcItimesClass6.Position], capcuonghoa, numx2);
							SynthesisHint(num, 0, Phi_HopThanh, Item_In_Bag[hcItimesClass6.Position]);
							SubtractItems(BitConverter.ToInt32(array31, 0), 1);
						}
						SubtractItems(hcItimesClass7.Position, 1);
						if (hcItimesClass8 != null)
						{
							SubtractItems(hcItimesClass8.Position, 1);
						}
						if (hcItimesClass9 != null)
						{
							SubtractItems(hcItimesClass9.Position, 1);
						}
						if (hcItimesClass10 != null)
						{
							SubtractItems(hcItimesClass10.Position, 1);
						}
						HopThanhVatPham_Table.Clear();
						SynthesisSystemUnlocked();
						NguyenBao_HopThanh_MoRa = 0;
						Phi_HopThanh = 0;
						OpenWarehouse = false;
					}
					end_IL_2d3f:;
				}
				catch (Exception ex95)
				{
					NguyenBao_HopThanh_MoRa = 0;
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成 error![" + base.Userid + "]-[" + base.UserName + "]" + ex95.Message);
					Form1.WriteLine(1, "合成系统 CuongHoa合成 合成 error![" + base.Userid + "]-[" + base.UserName + "]" + ex95.StackTrace);
				}
				goto default;
			case 370:
				try
				{
					if (HopThanhVatPham_Table.Count < 1)
					{
						SynthesisHint(num, 2, 0, Item_In_Bag[fromPos]);
					}
					else
					{
						if (Item_In_Bag[fromBag].GetVatPham_ID != 1008001963 && Item_In_Bag[fromBag].GetVatPham_ID != 1008001964)
						{
							break;
						}
						HcItimesClass hcItimesClass = null;
						if (HopThanhVatPham_Table.ContainsKey(1))
						{
							hcItimesClass = HopThanhVatPham_Table[1];
						}
						int Position = hcItimesClass.Position;
						int num45 = BitConverter.ToInt32(Item_In_Bag[Position].VatPham_ID, 0);
						if (!World.Itme.TryGetValue(num45, out var value) || value.FLD_RESIDE2 != 16 || (num45 != 1000001170 && num45 != 1000001171 && num45 != 1000001172 && num45 != 1000001173 && num45 != 1000001174 && num45 != 1000001175))
						{
							break;
						}
						int num62 = num45 + 6;
						if ((uint)(num62 - 1000001176) <= 5u)
						{
							int fLD_MAGIC = Item_In_Bag[hcItimesClass.Position].FLD_MAGIC0;
							int fLD_MAGIC2 = Item_In_Bag[hcItimesClass.Position].FLD_MAGIC1;
							int fLD_MAGIC3 = Item_In_Bag[hcItimesClass.Position].FLD_MAGIC2;
							int fLD_MAGIC4 = Item_In_Bag[hcItimesClass.Position].FLD_MAGIC3;
							int fLD_MAGIC5 = Item_In_Bag[hcItimesClass.Position].FLD_MAGIC4;
							SubtractItems(fromBag, 1);
							SubtractItems(Position, 1);
							int KhoaLai = 0;
							if (Item_In_Bag[hcItimesClass.Position].Vat_Pham_Khoa_Lai)
							{
								KhoaLai = 1;
							}
							IncreaseItemWithAttributes(num62, Position, 1, fLD_MAGIC, fLD_MAGIC2, fLD_MAGIC3, fLD_MAGIC4, fLD_MAGIC5, 0, 0, 0, KhoaLai, 0);
							HopThanhVatPham_Table.Clear();
							SynthesisSystemUnlocked();
							OpenWarehouse = false;
							NguyenBao_HopThanh_MoRa = 0;
							SynthesisHint(num, 1, 0, Item_In_Bag[hcItimesClass.Position]);
						}
					}
					break;
				}
				catch (Exception ex)
				{
					Form1.WriteLine(1, "组合错误![" + base.Userid + "]-[" + base.UserName + "]" + ex.Message);
					break;
				}
			}
		}
		catch (Exception ex9)
		{
			NguyenBao_HopThanh_MoRa = 0;
			Form1.WriteLine(1, "合成系统error![" + base.Userid + "]-[" + base.UserName + "][" + num + "]" + ex9.Message);
		}
	}
	public void SynthesisSystemUnlocked()
	{
		for (int i = 0; i < 96; i++)
		{
			Item_In_Bag[i].Khoa_Chat = false;
		}
		Init_Item_In_Bag();
	}

	public void SynthesisSystemLock()
	{
		for (int i = 0; i < 96; i++)
		{
			Item_In_Bag[i].Khoa_Chat = true;
		}
		Init_Item_In_Bag();
	}

	public void NguyenBao_HopThanh(byte[] PacketData, int PacketSize)
	{
		CheckTheNumberOfIngotsInBaibaoge();
		byte[] dst = new byte[4];
		System.Buffer.BlockCopy(PacketData, 10, dst, 0, 4);
		byte[] array = Converter.HexStringToByte("AA551E002A0121171000010000009400000000000000190000000000000000005C9355AA");
		if (base.FLD_RXPIONT >= World.NguyenBao_HopThanh)
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 10, 4);
			NguyenBao_HopThanh_MoRa = 1;
		}
		else
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(4), 0, array, 10, 4);
			NguyenBao_HopThanh_MoRa = 0;
		}
		System.Buffer.BlockCopy(BitConverter.GetBytes(base.FLD_RXPIONT), 0, array, 14, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 18, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(5), 0, array, 22, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(base.CharacterFullServerID), 0, array, 4, 2);
		if (base.Client != null)
		{
			base.Client.Send_Map_Data(array, array.Length);
		}
	}

	public void NguyenBao_HopThanh2(byte[] PacketData, int PacketSize)
	{
		CheckTheNumberOfIngotsInBaibaoge();
		byte[] dst = new byte[4];
		System.Buffer.BlockCopy(PacketData, 10, dst, 0, 4);
		byte[] array = Converter.HexStringToByte("AA551E002A0123171000010000009400000000000000190000000000000000005C9355AA");
		if (base.FLD_RXPIONT >= World.NguyenBao_HopThanh)
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 10, 4);
			KiemSoatNguyenBao_SoLuong(World.NguyenBao_HopThanh, 0);
			NguyenBao_HopThanh_MoRa = 1;
			RxjhClass.BachBaoCacRecord(base.Userid, base.UserName, 0.0, "HopThanh", 1, World.NguyenBao_HopThanh);
			Save_NguyenBaoData();
		}
		else
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(4), 0, array, 10, 4);
			NguyenBao_HopThanh_MoRa = 0;
		}
		System.Buffer.BlockCopy(BitConverter.GetBytes(base.FLD_RXPIONT), 0, array, 14, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 18, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(5), 0, array, 22, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(base.CharacterFullServerID), 0, array, 4, 2);
		if (base.Client != null)
		{
			base.Client.Send_Map_Data(array, array.Length);
		}
	}

	public static void SendNewsletter(int pid, string name, int level, int zx)
	{
		foreach (Players playersBes in World.allConnectedChars.Values)
		{
			playersBes.EnhancedSynthesisAnnouncement(pid, name, level, zx);
		}
	}

	public int CalculateSyntheticEnhancementCost(ItmeClass Itme, int Position, int ThaoTacD)
	{
		if (Itme.FLD_NJ != 0)
		{
			int num2 = ((Itme.FLD_LEVEL <= 60) ? ((int)((double)Itme.FLD_LEVEL * 0.1 * 2000.0)) : ((Itme.FLD_LEVEL > 60 && Itme.FLD_LEVEL <= 70) ? 50000 : ((Itme.FLD_LEVEL > 70 && Itme.FLD_LEVEL <= 80) ? 60000 : ((Itme.FLD_LEVEL > 80 && Itme.FLD_LEVEL <= 90) ? 80000 : ((Itme.FLD_LEVEL > 90 && Itme.FLD_LEVEL <= 100) ? 100000 : ((Itme.FLD_LEVEL > 100 && Itme.FLD_LEVEL <= 110) ? 150000 : ((Itme.FLD_LEVEL > 110 && Itme.FLD_LEVEL <= 120) ? 400000 : ((Itme.FLD_LEVEL > 120 && Itme.FLD_LEVEL <= 130) ? 750000 : ((Itme.FLD_LEVEL > 130 && Itme.FLD_LEVEL <= 140) ? 1200000 : ((Itme.FLD_LEVEL > 140 && Itme.FLD_LEVEL <= 150) ? 1750000 : ((Itme.FLD_LEVEL <= 140 || Itme.FLD_LEVEL > 150) ? ((int)((double)Itme.FLD_LEVEL * 0.1 * 250000.0)) : 350000)))))))))));
			switch (ThaoTacD)
			{
			case 61:
				if (Item_In_Bag[Position].FLD_CuongHoaSoLuong != 0)
				{
					num2 *= Item_In_Bag[Position].FLD_CuongHoaSoLuong + 1;
				}
				break;
			case 21:
				if (Item_In_Bag[Position].FLD_CuongHoaSoLuong != 0)
				{
					num2 *= Item_In_Bag[Position].FLD_CuongHoaSoLuong + 1;
				}
				break;
			case 11:
				if (Item_In_Bag[Position].FLD_CuongHoaSoLuong != 0)
				{
					num2 *= Item_In_Bag[Position].FLD_CuongHoaSoLuong + 1;
				}
				break;
			default:
				if (Item_In_Bag[Position].FLD_CuongHoaSoLuong != 0)
				{
					num2 *= Item_In_Bag[Position].FLD_CuongHoaSoLuong + 1;
				}
				break;
			}
			return num2;
		}
		int num = ((Itme.FLD_LEVEL <= 60) ? ((int)((double)Itme.FLD_LEVEL * 0.1 * 2000.0)) : ((Itme.FLD_LEVEL > 60 && Itme.FLD_LEVEL <= 70) ? 50000 : ((Itme.FLD_LEVEL > 70 && Itme.FLD_LEVEL <= 80) ? 60000 : ((Itme.FLD_LEVEL > 80 && Itme.FLD_LEVEL <= 90) ? 80000 : ((Itme.FLD_LEVEL > 90 && Itme.FLD_LEVEL <= 100) ? 100000 : ((Itme.FLD_LEVEL > 100 && Itme.FLD_LEVEL <= 110) ? 150000 : ((Itme.FLD_LEVEL > 110 && Itme.FLD_LEVEL <= 120) ? 400000 : ((Itme.FLD_LEVEL > 120 && Itme.FLD_LEVEL <= 130) ? 750000 : ((Itme.FLD_LEVEL > 130 && Itme.FLD_LEVEL <= 140) ? 1200000 : ((Itme.FLD_LEVEL > 140 && Itme.FLD_LEVEL <= 150) ? 1750000 : ((Itme.FLD_LEVEL <= 140 || Itme.FLD_LEVEL > 150) ? ((int)((double)Itme.FLD_LEVEL * 0.1 * 250000.0)) : 350000)))))))))));
		switch (ThaoTacD)
		{
		case 61:
			if (Item_In_Bag[Position].FLD_CuongHoaSoLuong != 0)
			{
				num *= Item_In_Bag[Position].FLD_CuongHoaSoLuong + 1;
			}
			break;
		case 21:
			if (Item_In_Bag[Position].FLD_CuongHoaSoLuong != 0)
			{
				num *= Item_In_Bag[Position].FLD_CuongHoaSoLuong + 1;
			}
			break;
		case 11:
			if (Item_In_Bag[Position].FLD_CuongHoaSoLuong != 0)
			{
				num *= Item_In_Bag[Position].FLD_CuongHoaSoLuong + 1;
			}
			break;
		default:
			if (Item_In_Bag[Position].FLD_CuongHoaSoLuong != 0)
			{
				num *= Item_In_Bag[Position].FLD_CuongHoaSoLuong + 1;
			}
			break;
		}
		return num;
	}

	public void SynthesisHint(int ThaoTacD, int NhacNho_ID, int SoTien, X_Vat_Pham_Loai VatPham)
	{
		byte[] array = Converter.HexStringToByte("AA557A002302510075003F010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFF0000000000000000000000000000000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(ThaoTacD), 0, array, 10, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(NhacNho_ID), 0, array, 12, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array, 14, 2);
		if (VatPham != null)
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(VatPham.VatPhamViTri), 0, array, 26, 4);
			System.Buffer.BlockCopy(VatPham.VatPham_ID, 0, array, 30, 4);
			System.Buffer.BlockCopy(VatPham.VatPham_ThuocTinh, 0, array, 38, World.VatPham_ThuocTinh_KichThuoc);
		}
		System.Buffer.BlockCopy(BitConverter.GetBytes((long)SoTien), 0, array, 98, 8);
		if (NguyenBao_HopThanh_MoRa == 1)
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes((long)World.NguyenBao_HopThanh), 0, array, 103, 8);
		}
		else
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(0L), 0, array, 103, 8);
		}
		System.Buffer.BlockCopy(BitConverter.GetBytes(base.CharacterFullServerID), 0, array, 4, 2);
		if (base.Client != null)
		{
			base.Client.SendMultiplePackage(array, array.Length);
		}
	}

	public int LuckyCharmBonus(int ID)
	{
		switch (ID)
		{
		case *********:
			return 5;
		case *********:
			return 10;
		case 800000005:
			return 15;
		default:
			return 0;
		case 1008001829:
			return 30;
		case 1008000136:
			return 25;
		case 800000029:
		case 1008000071:
		case 1008001074:
			return 20;
		}
	}
}