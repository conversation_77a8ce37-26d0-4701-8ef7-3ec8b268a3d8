using System;
using System.Linq;
using RxjhServer.DbClss;

namespace RxjhServer;

public partial class Players
{
    
    public int NeoLuckyCharmBonus(int ID)
    {
        return ID switch
        {
            1008002510 or 1008002516 or 1008002513 or 1008002519 or 1008002522 or 1008002525 => 15,
            1008002511 or 1008002517 or 1008002514 or 1008002520 or 1008002523 or 1008002526 => 20,
            1008002512 or 1008002518 or 1008002515 or 1008002521 or 1008002524 or 1008002527 => 25,
            1008002528 or 1008002529 or 1008002530 => 15,
            _ => 0
        };
    }

    readonly int[] ThuyNgocPhuNeo_Giam2Cap = { 1008002533, 1008002536, 1008002539 };

    readonly int[] ThuyNgocPhuNeo_Giam2Cap_Hon10 = { 1008002600 };

    readonly private int[] ThuyTinhPhuNeo_1_3 = { 1008002534,1008002537,1008002540};

    readonly private int[] TinhPhoPhuNeo_1_3 = { 1008002535,1008002538,1008002541};

    readonly private int[] ThuyNgocPhuNeo_Giam2Cap_TB = { 1008002542, 1008002545, 1008002548 };

    readonly private int[] ThuyNgocPhuNeo_Giam2Cap_TB_Hon10 = {1008002603 };
    readonly private int[] ThuyTinhPhuNeo_1_3_TB = { 1008002543,1008002546,1008002549};

    readonly private int[] TinhPhoPhuNeo_1_3_TB = { 1008002544,1008002547,1008002550};

    public void Enhance_Neo(byte[] data)
    {
        int type = BitConverter.ToUInt16(data, 10);
        int fromPos = data[26];
        int num3 = BitConverter.ToUInt16(data, 110);
        if (!OpenWarehouse) OpenWarehouse = true;
        switch (type)
        {
            case 511:
                InputItem_EnhanceNpc(fromPos, type);
                break;
            case 512:
                InputStone_EnhanceNpc(type, fromPos);
                break;
            case 513:
                InputSuccessCharm_EnhanceNpc(type, fromPos);
                break;
            case 514: 
                SubmitEnhanceNpc(type);
                OpenWarehouse = false;
                break;
            case 610:
                CancelEnhance(data,type);
                break;
            case 611:
                InputItem_EnhanceCharm(data, fromPos, type);
                break;
            case 612:
                InputStone_EnhanceNpc(type, fromPos);
                break;
            case 613:
                InputSuccessCharm_EnhanceCharm(type, fromPos);
                break;
            case 614:
                SubmitEnhanceCharm(data, type);
                OpenWarehouse = false;
                break;
        }
    }

    private void InputSuccessCharm_EnhanceCharm(int type, int fromPos)
    {
        if (base.CurrentOperationType == 324)
		{
			if (HopThanhVatPham_Table.Count == 0)
			{
				SynthesisHint(type, 5, 0, Item_In_Bag[fromPos]);
			}
			else if (!HopThanhVatPham_Table.ContainsKey(5))
			{
                if (World.Itme.TryGetValue((int)Item_In_Bag[fromPos].GetVatPham_ID, out var item) && item.FLD_RESIDE2 == 11 && NeoLuckyCharmBonus((int)Item_In_Bag[fromPos].GetVatPham_ID) !=0)
                {
                    HcItimesClass hcItimesClass18 = new HcItimesClass();
			        hcItimesClass18.Position = fromPos;
			        hcItimesClass18.VatPham = Item_In_Bag[fromPos].VatPham_byte;
			        Item_In_Bag[fromPos].Khoa_Chat = true;
			        HopThanhVatPham_Table.Add(5, hcItimesClass18);
			        SynthesisHint(type, 1, 0, Item_In_Bag[fromPos]);
                }
                else
                {
                    HeThongNhacNho("Không tìm thấy PVP này !!!");
                }

			}
		}
    }

    private void InputSuccessCharm_EnhanceNpc(int type, int fromPos)
    {
        if (base.CurrentOperationType == 323)
		{
			if (HopThanhVatPham_Table.Count == 0)
			{
				SynthesisHint(type, 5, 0, Item_In_Bag[fromPos]);
			}
			else if (!HopThanhVatPham_Table.ContainsKey(5))
			{
                if (World.Itme.TryGetValue((int)Item_In_Bag[fromPos].GetVatPham_ID, out var item) && item.FLD_RESIDE2 == 11 && NeoLuckyCharmBonus((int)Item_In_Bag[fromPos].GetVatPham_ID) !=0)
                {
                    HcItimesClass hcItimesClass18 = new HcItimesClass();
			        hcItimesClass18.Position = fromPos;
			        hcItimesClass18.VatPham = Item_In_Bag[fromPos].VatPham_byte;
			        Item_In_Bag[fromPos].Khoa_Chat = true;
			        HopThanhVatPham_Table.Add(5, hcItimesClass18);
			        SynthesisHint(type, 1, 0, Item_In_Bag[fromPos]);
                }
                else
                {
                    HeThongNhacNho("Không tìm thấy PVP này !!!");
                }

			}
		}
    }

    private void CancelEnhance(byte[] data,int type)
    {
        HopThanhVatPham_Table.Clear();
        SynthesisSystemUnlocked();
        NguyenBao_HopThanh_MoRa = 0;
        OpenWarehouse = false;
        int num2 = data[26];
        SynthesisHint(type, 1, 0, Item_In_Bag[num2]);
        return;
    }

    private void SubmitEnhanceCharm(byte[] data, int type)
    {
        try
        {
            byte[] array30;
            int buaId;
            HcItimesClass itemCuongHoa;
            HcItimesClass stoneSlot1;
            HcItimesClass stoneSlot2;
            HcItimesClass stoneSlot3;
            HcItimesClass phucVanPhu;
            ItmeClass item;
            if(base.CurrentOperationType == 324)
            {
                array30 = new byte[4];
                byte[] dst3 = new byte[4];
                System.Buffer.BlockCopy(data, 14, array30, 0, 4);
                System.Buffer.BlockCopy(data, 18, dst3, 0, 4);
                buaId = BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array30, 0)].VatPham_ID, 0);
                if(buaId != 0 && HopThanhVatPham_Table.Count > 0)
                {
                    itemCuongHoa = null;
                    stoneSlot1 = null;
                    stoneSlot2 = null;
                    stoneSlot3 = null;
                    phucVanPhu = null;
                    if(HopThanhVatPham_Table.ContainsKey(1))
                    {
                        itemCuongHoa = HopThanhVatPham_Table[1];
                    }
                    if(HopThanhVatPham_Table.ContainsKey(2))
                    {
                        stoneSlot1 = HopThanhVatPham_Table[2];
                    }
                    if(HopThanhVatPham_Table.ContainsKey(3))
                    {
                        stoneSlot2 = HopThanhVatPham_Table[3];
                    }
                    if(HopThanhVatPham_Table.ContainsKey(4))
                    {
                        stoneSlot3 = HopThanhVatPham_Table[4];
                    }
                    if(HopThanhVatPham_Table.ContainsKey(5))
                    {
                        phucVanPhu = HopThanhVatPham_Table[5];
                    }
                    if(BitConverter.ToInt64(Item_In_Bag[itemCuongHoa.Position].ItemGlobal_ID, 0) !=
                       BitConverter.ToInt64(itemCuongHoa.ItemGlobal_ID, 0))
                    {
                        SynthesisHint(type, 2, 0, Item_In_Bag[itemCuongHoa.Position]);
                        HopThanhVatPham_Table.Clear();
                    } else if(stoneSlot1 != null)
                    {
                        if(stoneSlot1 != null && BitConverter.ToInt64(Item_In_Bag[stoneSlot1.Position].ItemGlobal_ID, 0) !=
                           BitConverter.ToInt64(stoneSlot1.ItemGlobal_ID, 0))
                        {
                            SynthesisHint(type, 2, 0, Item_In_Bag[stoneSlot1.Position]);
                            HopThanhVatPham_Table.Clear();
                        } else if(stoneSlot2 != null && BitConverter.ToInt64(Item_In_Bag[stoneSlot2.Position].ItemGlobal_ID, 0) !=
                                  BitConverter.ToInt64(stoneSlot2.ItemGlobal_ID, 0))
                        {
                            SynthesisHint(type, 2, 0, Item_In_Bag[stoneSlot2.Position]);
                            HopThanhVatPham_Table.Clear();
                        } else if(stoneSlot3 != null && BitConverter.ToInt64(Item_In_Bag[stoneSlot3.Position].ItemGlobal_ID, 0) !=
                                  BitConverter.ToInt64(stoneSlot3.ItemGlobal_ID, 0))
                        {
                            SynthesisHint(type, 2, 0, Item_In_Bag[stoneSlot3.Position]);
                            HopThanhVatPham_Table.Clear();
                        } else if(phucVanPhu != null && BitConverter.ToInt64(Item_In_Bag[phucVanPhu.Position].ItemGlobal_ID, 0) !=
                                  BitConverter.ToInt64(phucVanPhu.ItemGlobal_ID, 0))
                        {
                            SynthesisHint(type, 2, 0, Item_In_Bag[phucVanPhu.Position]);
                            HopThanhVatPham_Table.Clear();
                        } else
                        {
                            itemCuongHoa.DatDuocThuocTinh();
                            itemCuongHoa.CuongHoaThuocTinhGiaiDoan();
                            int thuocTinhManh = itemCuongHoa.CuongHoaSoLuong;
                             //HeThongNhacNho("Bua ID" + buaId);
                            if(!World.Itme.TryGetValue(BitConverter.ToInt32(itemCuongHoa.VatPham_id, 0), out item))
                            {
                                SynthesisHint(type, 2, 0, Item_In_Bag[itemCuongHoa.Position]);
                                HopThanhVatPham_Table.Clear();
                            } else if(item.FLD_RESIDE2 != 1 && item.FLD_RESIDE2 != 2 && item.FLD_RESIDE2 !=
                                      4 && item.FLD_RESIDE2 != 5 && item.FLD_RESIDE2 != 6 && item.FLD_RESIDE2 !=
                                      14 && item.FLD_RESIDE2 != 23 && item.FLD_RESIDE2 != 24 && item.FLD_RESIDE2 !=
                                      25 && item.FLD_RESIDE2 != 14)
                            {
                                SynthesisHint(type, 2, 0, Item_In_Bag[itemCuongHoa.Position]);
                                HopThanhVatPham_Table.Clear();
                            } else if(item.FLD_RESIDE2 == 4)
                            {
                                // case vũ khí
                                // Trường hợp các bùa yêu cầu cường hóa < 10
                                if (ThuyTinhPhuNeo_1_3.Contains(buaId))
                                {
                                    if (thuocTinhManh >= 10)
                                    {
                                        HeThongNhacNho("Vật phẩm có cường hóa >= 10, vui lòng sử dụng bùa Chân.");
                                        return;
                                    }
                                }
                                else if (ThuyNgocPhuNeo_Giam2Cap.Contains(buaId))
                                {
                                    if (thuocTinhManh >= 10)
                                    {
                                        HeThongNhacNho("Vật phẩm có cường hóa >= 10, vui lòng sử dụng bùa Chân.");
                                        return;
                                    }
                                }
                                else if (TinhPhoPhuNeo_1_3.Contains(buaId))
                                {
                                    if (thuocTinhManh >= 10)
                                    {
                                       
                                        HeThongNhacNho("Vật phẩm có cường hóa >= 10, vui lòng sử dụng bùa Chân.");
                                        return;
                                    }
                                }
                                else if (ThuyNgocPhuNeo_Giam2Cap_Hon10.Contains(buaId))
                                {
                                    if (thuocTinhManh < 10){
                                        HeThongNhacNho("Vật phẩm có cường hóa < 10, vui lòng sử dụng bùa khác phù hợp.");
                                        return;
                                    }
                                }
                                else
                                {
                                    HeThongNhacNho("Không tìm thấy bùa hiện tại. Vui lòng liên hệ admin.");
                                    return;
                                }

                                // Tiếp tục xử lý nếu điều kiện bùa và thuộc tính khớp
                                if (SuDungBuaCuongHoa(type, buaId, itemCuongHoa, item, stoneSlot1, stoneSlot2, stoneSlot3))
                                {
                                    if (base.Player_Money >= Phi_HopThanh)
                                    {
                                        HeThongNhacNho($"Đã sử dụng {Phi_HopThanh:N0} Lượng để cường hóa.");
                                        KiemSoatGold_SoLuong(Phi_HopThanh, 0);
                                        UpdateMoneyAndWeight();
                                        EnhanceItemProcessing(type, phucVanPhu, itemCuongHoa, stoneSlot2, stoneSlot3, buaId, item, array30, stoneSlot1);
                                        HopThanhVatPham_Table.Clear();
                                    }
                                    else
                                    {
                                        
                                        SynthesisHint(11, 4, 0, Item_In_Bag[itemCuongHoa.Position]);    
                                    }
                                    return;
                                }
                                SynthesisHint(type, 2, 0, Item_In_Bag[itemCuongHoa.Position]);
                            } else
                            {
                                // Kiểm tra các bùa yêu cầu cường hóa < 10
                               
                                if (ThuyTinhPhuNeo_1_3_TB.Contains(buaId))
                                {
                                    if (thuocTinhManh >= 10)
                                    {
                                        HeThongNhacNho("Vật phẩm có cường hóa >= 10, vui lòng sử dụng bùa Chân.");
                                        SynthesisHint(type, 2, 0, Item_In_Bag[itemCuongHoa.Position]);
                                        return;
                                    }
                                }
                                else if (ThuyNgocPhuNeo_Giam2Cap_TB.Contains(buaId))
                                {
                                    if (thuocTinhManh >= 10)
                                    {
                                        HeThongNhacNho("Vật phẩm có cường hóa >= 10, vui lòng sử dụng bùa Chân.");
                                        SynthesisHint(type, 2, 0, Item_In_Bag[itemCuongHoa.Position]);
                                        return;
                                    }
                                }
                                else if (TinhPhoPhuNeo_1_3_TB.Contains(buaId))
                                {
                                    if (thuocTinhManh >= 10)
                                    {
                                        HeThongNhacNho("Vật phẩm có cường hóa >= 10, vui lòng sử dụng bùa Chân.");
                                        SynthesisHint(type, 2, 0, Item_In_Bag[itemCuongHoa.Position]);
                                        return;
                                    }
                                }
                                // Kiểm tra bùa yêu cầu cường hóa >= 10
                                else if (ThuyNgocPhuNeo_Giam2Cap_TB_Hon10.Contains(buaId))
                                {
                                    if (thuocTinhManh < 10)
                                    {
                                        HeThongNhacNho("Vật phẩm có cường hóa < 10, vui lòng sử dụng bùa khác phù hợp.");
                                        SynthesisHint(type, 2, 0, Item_In_Bag[itemCuongHoa.Position]);
                                        return;
                                    }
                                }
                                else
                                {
                                    HeThongNhacNho("Bùa không hợp lệ.");
                                    SynthesisHint(type, 2, 0, Item_In_Bag[itemCuongHoa.Position]);
                                    return;
                                }

                                // Tiếp tục xử lý nếu điều kiện bùa và thuộc tính khớp
                                if (SuDungBuaCuongHoa(type, buaId, itemCuongHoa, item, stoneSlot1, stoneSlot2, stoneSlot3))
                                {
                                    if (base.Player_Money >= Phi_HopThanh)
                                    {
                                        HeThongNhacNho($"Đã sử dụng {Phi_HopThanh:N0} Lượng");
                                        KiemSoatGold_SoLuong(Phi_HopThanh, 0);
                                        UpdateMoneyAndWeight();
                                        EnhanceItemProcessing(type, phucVanPhu, itemCuongHoa, stoneSlot2, stoneSlot3, buaId, item, array30, stoneSlot1);
                                        HopThanhVatPham_Table.Clear();
                                      }
                                    else
                                    {
                                        SynthesisHint(11, 4, 0, Item_In_Bag[itemCuongHoa.Position]);    
                                    }
                                    return;
                                }

                                SynthesisHint(type, 2, 0, Item_In_Bag[itemCuongHoa.Position]);
                                                            }
                        }
                    }
                }
            }

        } catch(Exception ex79)
        {
            NguyenBao_HopThanh_MoRa = 0;
            Form1.WriteLine(1, "合成系统 CuongHoa合成 合成 error![" + base.Userid + "]-[" + base.UserName + "]" + ex79.Message);
            Form1.WriteLine(1, "合成系统 CuongHoa合成 合成 error![" + base.Userid + "]-[" + base.UserName + "]" + ex79.StackTrace);
        }
    }

    private bool SuDungBuaCuongHoa(int type, int buaId, HcItimesClass hcItimesClass102, ItmeClass value81,
        HcItimesClass hcItimesClass103, HcItimesClass hcItimesClass104, HcItimesClass hcItimesClass105)
    {
        var TNP_Condition = ThuyNgocPhuNeo_Giam2Cap.Contains(buaId) && Item_In_Bag[hcItimesClass102.Position].FLD_CuongHoaSoLuong <= 5 && Item_In_Bag[hcItimesClass102.Position].FLD_CuongHoaSoLuong >= 10;
        var TNP_Condition_Hon10 = ThuyNgocPhuNeo_Giam2Cap_Hon10.Contains(buaId) && Item_In_Bag[hcItimesClass102.Position].FLD_CuongHoaSoLuong < 10;
        if(TNP_Condition || TNP_Condition_Hon10)
        {
            SynthesisHint(type, 2, 0, Item_In_Bag[hcItimesClass102.Position]);
        } else if(TinhPhoPhuNeo_1_3.Contains(buaId) && Item_In_Bag[hcItimesClass102.Position].FLD_LEVEL >=
                  100)
        {
            SynthesisHint(type, 2, 0, Item_In_Bag[hcItimesClass102.Position]);
        } else if(Item_In_Bag[hcItimesClass102.Position].Vat_Pham_Khoa_Lai)
        {
            SynthesisHint(type, 2, 0, Item_In_Bag[hcItimesClass102.Position]);
            HopThanhVatPham_Table.Clear();
        } else if(hcItimesClass102.CuongHoaSoLuong >= 16)
        {
            SynthesisHint(type, 2, 0, Item_In_Bag[hcItimesClass102.Position]);
            HopThanhVatPham_Table.Clear();
        } else if(value81.FLD_LEVEL >= 130)
        {
            if(hcItimesClass103 != null && BitConverter.ToInt32(hcItimesClass103.VatPham_id, 0) !=
               800000060)
            {
                SynthesisHint(type, 2, 0, Item_In_Bag[hcItimesClass102.Position]);
                HopThanhVatPham_Table.Clear();
            } else if(hcItimesClass104 != null && BitConverter.ToInt32(hcItimesClass104.VatPham_id, 0) !=
                      800000060)
            {
                SynthesisHint(type, 2, 0, Item_In_Bag[hcItimesClass102.Position]);
                HopThanhVatPham_Table.Clear();
            } else
            {
                if(hcItimesClass105 == null || BitConverter.ToInt32(hcItimesClass105.VatPham_id, 0) ==
                   800000060)
                {
                    return true;
                }
                SynthesisHint(type, 2, 0, Item_In_Bag[hcItimesClass102.Position]);
                HopThanhVatPham_Table.Clear();
            }
        } else if(hcItimesClass103 != null && BitConverter.ToInt32(hcItimesClass103.VatPham_id, 0) !=
                  800000006)
        {
            SynthesisHint(type, 2, 0, Item_In_Bag[hcItimesClass102.Position]);
            HopThanhVatPham_Table.Clear();
        } else if(hcItimesClass104 != null && BitConverter.ToInt32(hcItimesClass104.VatPham_id, 0) !=
                  800000006)
        {
            SynthesisHint(type, 2, 0, Item_In_Bag[hcItimesClass102.Position]);
            HopThanhVatPham_Table.Clear();
        } else
        {
            if(hcItimesClass105 == null || BitConverter.ToInt32(hcItimesClass105.VatPham_id, 0) ==
               800000006)
            {
                return true;
            }
            SynthesisHint(type, 2, 0, Item_In_Bag[hcItimesClass102.Position]);
            HopThanhVatPham_Table.Clear();
        }

        return false;
    }

    private void EnhanceItemProcessing(int type, HcItimesClass phucVanPhu, HcItimesClass item,
        HcItimesClass hcItimesClass104, HcItimesClass hcItimesClass105, int buaId, ItmeClass value81, byte[] array30,
        HcItimesClass hcItimesClass103)
    {
        double rateMin = 0;
        double rateMax = 100;
        var option_before = Item_In_Bag[item.Position].FLD_MAGIC0;

        double rate = new Random((int)DateTime.Now.Ticks).Next((int)rateMin, (int)rateMax);
        int num45 = 0;
        while (num45 < new Random((int)DateTime.Now.Ticks).Next(5, 15))
        {
            rate = new Random(World.GetRandomSeed()).Next((int)rateMin, (int)rateMax);
            int num12 = num45 + 1;
            num45 = num12;
        }
        if (phucVanPhu != null)
        {
            rate += (double)NeoLuckyCharmBonus(BitConverter.ToInt32(phucVanPhu.VatPham_id, 0));
        }
        if (NguyenBao_HopThanh_MoRa == 1)
        {
            rate += 5.0;
            rateMax += 5.0;
        }
        //if(base.FLD_VIP == 1)
        //{
        //    rate += 100.0 * World.VIPTyLe_HopThanhGiaTang_TiLePhanTram;
        //    rateMax += 100.0 * World.VIPTyLe_HopThanhGiaTang_TiLePhanTram;
        //}
        if(World.TyLe_CuongHoa_ToiCao != 0.0)
        {
            rate += World.TyLe_CuongHoa_ToiCao;
            rateMax += World.TyLe_CuongHoa_ToiCao;
        }
        if(base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
        {
            rate += 100.0 * base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
            rateMax += 100.0 * base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
        }
        if(base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
        {
            rate += 100.0 * base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
            rateMax += 100.0 * base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
        }
        if(item.ThuocTinh1.ThuocTinhSoLuong + item.ThuocTinh2.ThuocTinhSoLuong !=
           0 && hcItimesClass104 == null)
        {
            HeThongNhacNho("Cần 2 đá cường hóa.");
        } else if(item.ThuocTinh1.ThuocTinhSoLuong + item.ThuocTinh2.ThuocTinhSoLuong !=
                  0 && item.ThuocTinh3.ThuocTinhSoLuong + item.ThuocTinh4.ThuocTinhSoLuong !=
                  0 && hcItimesClass105 == null)
        {
            HeThongNhacNho("Cần 3 đá cường hóa.");
        } else
        {
            int num67 = buaId;
            if(buaId == ********** || buaId == **********)
            {
                rate += World.TyLe_TangCuong_VatPham;
            }
            var successRate = 100.0 - item.CuongHoaSoLuong switch
            {
                0 => World.CuongHoa1TyLe_HopThanh,
                1 => World.CuongHoa2TyLe_HopThanh,
                2 => World.CuongHoa3TyLe_HopThanh,
                3 => World.CuongHoa4TyLe_HopThanh,
                4 => World.CuongHoa5TyLe_HopThanh,
                5 => World.CuongHoa6TyLe_HopThanh,
                6 => World.CuongHoa7TyLe_HopThanh,
                7 => World.CuongHoa8TyLe_HopThanh,
                8 => World.CuongHoa9TyLe_HopThanh,
                9 => World.CuongHoa10TyLe_HopThanh,
                10 => World.CuongHoa11TyLe_HopThanh,
                11 => World.CuongHoa12TyLe_HopThanh,
                12 => World.CuongHoa13TyLe_HopThanh,
                13 => World.CuongHoa14TyLe_HopThanh,
                14 => World.CuongHoa15TyLe_HopThanh,
                15 => World.CuongHoa16TyLe_HopThanh,
                _ => -100,
            };
            
            HeThongNhacNho("RandomRate : Min : " + rateMin + "/ Max : " + rateMax + " - Pill Lucky : " + base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram + " - Item Lucky : " + base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram, 7);
            HeThongNhacNho("SuccessRate : " + rate + "/" + (int) successRate, 7);
            if (rate >= successRate){
                if(!EnhanceSuccessed(type, item, value81, array30, option_before))
                {
                    HeThongNhacNho("Không thể sử dụng với vật phẩm này.",7 );
                    return;
                };
            }
            else
            {
                 EnhanceFailed(type, item, value81, array30, option_before);
            }

            SubtractItems(hcItimesClass103.Position, 1);
            if(hcItimesClass104 != null)
            {
                SubtractItems(hcItimesClass104.Position, 1);
            }
            if(hcItimesClass105 != null)
            {
                SubtractItems(hcItimesClass105.Position, 1);
            }
            if(phucVanPhu != null)
            {
                SubtractItems(phucVanPhu.Position, 1);
            }
            HopThanhVatPham_Table.Clear();
            SynthesisSystemUnlocked();
            NguyenBao_HopThanh_MoRa = 0;
            Phi_HopThanh = 0;
            OpenWarehouse = false;
        }
    }

    private void EnhanceFailed(int type, HcItimesClass enhanceItem, ItmeClass item, byte[] array30, int optionBefore)
    {
        var buaId = BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array30, 0)].VatPham_ID, 0);
        if (ThuyNgocPhuNeo_Giam2Cap.Contains(buaId) || ThuyNgocPhuNeo_Giam2Cap_TB.Contains(buaId) || ThuyNgocPhuNeo_Giam2Cap_TB_Hon10.Contains(buaId) || ThuyNgocPhuNeo_Giam2Cap_Hon10.Contains(buaId))
        {
            if (enhanceItem.CuongHoaSoLuong > 2)
            {
                enhanceItem.CuongHoaSoLuong -= 2;
            }
            else
            {
                enhanceItem.CuongHoaSoLuong -= 1;
            }
            enhanceItem.ThietLap_GiaiDoanThuocTinh();
            if (BitConverter.ToInt32(Item_In_Bag[enhanceItem.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[enhanceItem.Position].ItemGlobal_ID, 0) ==
                BitConverter.ToInt64(enhanceItem.ItemGlobal_ID, 0))
            {
                SubtractItems(enhanceItem.Position, 1);
                AddItems(enhanceItem.ItemGlobal_ID, enhanceItem.VatPham_id, enhanceItem.Position, enhanceItem.VatPhamSoLuong, enhanceItem.VatPham_ThuocTinh);
            }
        }
        else
        {
                enhanceItem.CuongHoaLoaiHinh = 0;
                enhanceItem.CuongHoaSoLuong = 0;
                enhanceItem.ThietLap_GiaiDoanThuocTinh();
                if (BitConverter.ToInt32(Item_In_Bag[enhanceItem.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[enhanceItem.Position].ItemGlobal_ID, 0) ==
                   BitConverter.ToInt64(enhanceItem.ItemGlobal_ID, 0))
                {
                    SubtractItems(enhanceItem.Position, 1);
                    AddItems(enhanceItem.ItemGlobal_ID, enhanceItem.VatPham_id, enhanceItem.Position, enhanceItem.VatPhamSoLuong, enhanceItem.VatPham_ThuocTinh);
                }
        }
         RxjhClass.SyntheticRecord(base.Userid, base.UserName, item.ItmeNAME, type, "CuongHoa", "CharmFailed", Item_In_Bag[enhanceItem.Position],magic0_before: optionBefore);
       
        SynthesisHint(type, 0, Phi_HopThanh, Item_In_Bag[enhanceItem.Position]);
        SubtractItems(BitConverter.ToInt32(array30, 0), 1);
    }

    private bool EnhanceSuccessed(int type, HcItimesClass enhanceItem, ItmeClass item, byte[] array30, int optionBefore)
    {
        enhanceItem.CuongHoaLoaiHinh = (item.FLD_RESIDE2 == 4) ? 1 : 2;
        var buaId = BitConverter.ToInt32(Item_In_Bag[BitConverter.ToInt32(array30, 0)].VatPham_ID, 0);
        if (ThuyTinhPhuNeo_1_3.Contains(buaId) || ThuyTinhPhuNeo_1_3_TB.Contains(buaId))
        {
            if(enhanceItem.CuongHoaLoaiHinh > 10)
                return false;
            int rate1_3 = RNG.Next(0, 100);
            enhanceItem.CuongHoaSoLuong += rate1_3 switch
            {
                < 20 => 3,
                >= 20 and < 40 => 2,
                _ => 1,
            };
            if (enhanceItem.CuongHoaSoLuong > 10)
            {
                enhanceItem.CuongHoaSoLuong = 10;
            }
        }
        else if (TinhPhoPhuNeo_1_3.Contains(buaId) || TinhPhoPhuNeo_1_3_TB.Contains(buaId)){
            if(item.FLD_LEVEL > 100)
                return false;
            int rate1_3 = RNG.Next(0, 100);
            enhanceItem.CuongHoaSoLuong += rate1_3 switch
            {
                < 20 => 3,
                >= 20 and < 40 => 2,
                _ => 1,
            };
            if (enhanceItem.CuongHoaSoLuong > 10)
            {
                enhanceItem.CuongHoaSoLuong = 10;
            }
        }
        else
        {
            enhanceItem.CuongHoaSoLuong += 1;
        }
        
        if (enhanceItem.CuongHoaSoLuong > 16)
        {
            enhanceItem.CuongHoaSoLuong = 16;
        }
        enhanceItem.ThietLap_GiaiDoanThuocTinh();
        SubtractItems(BitConverter.ToInt32(array30, 0), 1);
        if (enhanceItem.CuongHoaSoLuong >= 10)
        {
            SendNewsletter(BitConverter.ToInt32(enhanceItem.VatPham_id, 0),base.UserName, enhanceItem.CuongHoaSoLuong, base.Player_Zx);
        }
        if (BitConverter.ToInt32(Item_In_Bag[enhanceItem.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[enhanceItem.Position].ItemGlobal_ID, 0) ==
           BitConverter.ToInt64(enhanceItem.ItemGlobal_ID, 0))
        {
            SubtractItems(enhanceItem.Position, 1);
            AddItems(enhanceItem.ItemGlobal_ID, enhanceItem.VatPham_id, enhanceItem.Position, enhanceItem.VatPhamSoLuong, enhanceItem.VatPham_ThuocTinh);
        }
        Item_In_Bag[enhanceItem.Position].Khoa_Chat = false;
        RxjhClass.SyntheticRecord(base.Userid, base.UserName, item.ItmeNAME, type, "CuongHoa", "CharmSucessed", Item_In_Bag[enhanceItem.Position],magic0_before: optionBefore);
        SynthesisHint(type, 1, Phi_HopThanh, Item_In_Bag[enhanceItem.Position]);
        return true;
    }

    private void InputStone_EnhanceCharm(int type, int fromPos)
    {
        try
        {
            HcItimesClass chtSlot1;
            HcItimesClass chtSlot2;
            if (base.CurrentOperationType == 324 && !HopThanhVatPham_Table.ContainsKey(4))
            {
                if (HopThanhVatPham_Table.ContainsKey(1))
                {
                    chtSlot1 = HopThanhVatPham_Table[1];
                    ItmeClass value78;
                    if (HopThanhVatPham_Table.Count == 0)
                    {
                        SynthesisHint(type, 5, 0, Item_In_Bag[fromPos]);
                    }
                    else if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) >= 1 &&
                             World.Itme.TryGetValue(BitConverter.ToInt32(chtSlot1.VatPham_id, 0),
                                 out value78))
                    {
                        if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 800000006 &&
                            BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) !=
                            800000060)
                        {
                            SynthesisHint(type, 7, 0, Item_In_Bag[fromPos]);
                        }
                        else if (value78.FLD_LEVEL >= 130)
                        {
                            if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) == 800000060)
                            {
                                chtSlot2 = new HcItimesClass();
                                chtSlot2.Position = fromPos;
                                chtSlot2.VatPham = Item_In_Bag[fromPos].VatPham_byte;
                                Item_In_Bag[fromPos].Khoa_Chat = true;
                                if (!HopThanhVatPham_Table.ContainsKey(2))
                                {
                                    HopThanhVatPham_Table.Add(2, chtSlot2);
                                    SynthesisHint(type, 1, 0, Item_In_Bag[fromPos]);
                                }

                                chtSlot1.DatDuocThuocTinh();
                                if (chtSlot1.ThuocTinh1.ThuocTinhSoLuong +
                                    chtSlot1.ThuocTinh2.ThuocTinhSoLuong ==
                                    0)
                                {
                                    if (chtSlot1.ThuocTinh1.ThuocTinhSoLuong +
                                        chtSlot1.ThuocTinh2.ThuocTinhSoLuong !=
                                        0 && chtSlot1.ThuocTinh3.ThuocTinhSoLuong +
                                        chtSlot1.ThuocTinh4.ThuocTinhSoLuong !=
                                        0 && BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) >=
                                        3 && !HopThanhVatPham_Table.ContainsKey(4))
                                    {
                                        HopThanhVatPham_Table.Add(4, chtSlot2);
                                        SynthesisHint(type, 1, 0, Item_In_Bag[fromPos]);
                                        return;
                                    }
                                }

                                if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) >= 2)
                                {
                                    if (!HopThanhVatPham_Table.ContainsKey(3))
                                    {
                                        HopThanhVatPham_Table.Add(3, chtSlot2);
                                        SynthesisHint(type, 1, 0, Item_In_Bag[fromPos]);
                                    }

                                    if (chtSlot1.ThuocTinh1.ThuocTinhSoLuong +
                                        chtSlot1.ThuocTinh2.ThuocTinhSoLuong !=
                                        0 && chtSlot1.ThuocTinh3.ThuocTinhSoLuong +
                                        chtSlot1.ThuocTinh4.ThuocTinhSoLuong !=
                                        0 && BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) >=
                                        3 && !HopThanhVatPham_Table.ContainsKey(4))
                                    {
                                        HopThanhVatPham_Table.Add(4, chtSlot2);
                                        SynthesisHint(type, 1, 0, Item_In_Bag[fromPos]);
                                        return;
                                    }
                                }
                            }

                            //SynthesisHint(type, 5, 0, Item_In_Bag[fromPos]);
                        }
                        else
                        {
                            if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) == 800000006)
                            {
                                chtSlot2 = new HcItimesClass();
                                chtSlot2.Position = fromPos;
                                chtSlot2.VatPham = Item_In_Bag[fromPos].VatPham_byte;
                                Item_In_Bag[fromPos].Khoa_Chat = true;
                                if (!HopThanhVatPham_Table.ContainsKey(2))
                                {
                                    HopThanhVatPham_Table.Add(2, chtSlot2);
                                    SynthesisHint(type, 1, 0, Item_In_Bag[fromPos]);
                                }

                                chtSlot1.DatDuocThuocTinh();
                                if (chtSlot1.ThuocTinh1.ThuocTinhSoLuong +
                                    chtSlot1.ThuocTinh2.ThuocTinhSoLuong ==
                                    0)
                                {
                                    if (chtSlot1.ThuocTinh1.ThuocTinhSoLuong +
                                        chtSlot1.ThuocTinh2.ThuocTinhSoLuong !=
                                        0 && chtSlot1.ThuocTinh3.ThuocTinhSoLuong +
                                        chtSlot1.ThuocTinh4.ThuocTinhSoLuong !=
                                        0 && BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) >=
                                        3 && !HopThanhVatPham_Table.ContainsKey(4))
                                    {
                                        HopThanhVatPham_Table.Add(4, chtSlot2);
                                        SynthesisHint(type, 1, 0, Item_In_Bag[fromPos]);
                                        return;
                                    }
                                }

                                if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) >= 2)
                                {
                                    if (!HopThanhVatPham_Table.ContainsKey(3))
                                    {
                                        HopThanhVatPham_Table.Add(3, chtSlot2);
                                        SynthesisHint(type, 1, 0, Item_In_Bag[fromPos]);
                                    }

                                    if (chtSlot1.ThuocTinh1.ThuocTinhSoLuong +
                                        chtSlot1.ThuocTinh2.ThuocTinhSoLuong !=
                                        0 && chtSlot1.ThuocTinh3.ThuocTinhSoLuong +
                                        chtSlot1.ThuocTinh4.ThuocTinhSoLuong !=
                                        0 && BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) >=
                                        3 && !HopThanhVatPham_Table.ContainsKey(4))
                                    {
                                        HopThanhVatPham_Table.Add(4, chtSlot2);
                                        SynthesisHint(type, 1, 0, Item_In_Bag[fromPos]);
                                        return;
                                    }
                                }
                            }

                            SynthesisHint(type, 1, 0, Item_In_Bag[fromPos]);
                        }
                    }
                }
                else
                {
                    SynthesisHint(type, 5, 0, Item_In_Bag[fromPos]);
                }
            }
        }
        catch (Exception ex76)
        {
            Form1.WriteLine(1, "InputStone_EnhanceCharm error![" + base.Userid + "]-[" + base.UserName + "]" + ex76.Message);
        }
    }

    private void InputItem_EnhanceCharm(byte[] data, int fromPos, int type)
    {
        try
        {
            int itemID;
            if (base.CurrentOperationType == 324 && !HopThanhVatPham_Table.ContainsKey(1))
            {
                if (Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
                {
                    SynthesisHint(type, 2, 0, Item_In_Bag[fromPos]);
                }
                else
                {
                    byte[] array31 = new byte[4];
                    System.Buffer.BlockCopy(data, 14, array31, 0, 4);
                    if (World.Itme.TryGetValue((int)Item_In_Bag[fromPos].GetVatPham_ID, out ItmeClass enhancingItem))
                    {
                        if (enhancingItem.FLD_RESIDE2 == 12)
                        {
                            SynthesisHint(type, 5, 0, Item_In_Bag[fromPos]);
                        }
                        else if (Item_In_Bag[fromPos].FLD_CuongHoaSoLuong < 20)
                        {
                            if (enhancingItem.FLD_RESIDE2 != 1 && enhancingItem.FLD_RESIDE2 != 2 && enhancingItem.FLD_RESIDE2 !=
                                4 && enhancingItem.FLD_RESIDE2 != 5 && enhancingItem.FLD_RESIDE2 != 6 &&
                                enhancingItem.FLD_RESIDE2 !=
                                14)
                            {
                                SynthesisHint(type, 5, 0, Item_In_Bag[fromPos]);
                            }
                            else
                            {
                                itemID = (int)Item_In_Bag[BitConverter.ToInt32(array31, 0)].GetVatPham_ID;
                                if (enhancingItem.FLD_RESIDE2 == 4 )
                                {
                                    //if (ThuyTinhPhuNeo_1_3.Contains(itemID) )
                                    //{
                                    //    if (Item_In_Bag[fromPos].FLD_CuongHoaSoLuong >= 10){
                                    //        SynthesisHint(type,5,0,Item_In_Bag[fromPos]);
                                    //        HeThongNhacNho("Vui lòng sử dụng vật phẩm cường hóa dưới 10",6,"Hệ Thống");
                                    //        return;
                                    //    }
                                    //}
                                    //else if (ThuyNgocPhuNeo_Giam2Cap.Contains(itemID))
                                    //{
                                    //   if (Item_In_Bag[fromPos].FLD_CuongHoaSoLuong >= 10)
                                    //    {
                                    //        SynthesisHint(type,5,0,Item_In_Bag[fromPos]);
                                    //        HeThongNhacNho("Vui lòng sử dụng vật phẩm cường hóa dưới 10",6,"Hệ Thống");
                                    //        return;
                                    //    }
                                    //}
                                    //else if (ThuyNgocPhuNeo_Giam2Cap_Hon10.Contains(itemID))
                                    //{
                                    //    if (Item_In_Bag[fromPos].FLD_CuongHoaSoLuong < 10)
                                    //    {
                                    //        SynthesisHint(type,5,0,Item_In_Bag[fromPos]);
                                    //        HeThongNhacNho("Vui lòng sử dụng vật phẩm cường hóa dưới 10",6,"Hệ Thống");
                                    //        return;
                                    //    }
                                    //}
                                    //else if (TinhPhoPhuNeo_1_3.Contains(itemID)){
                                    //    if (Item_In_Bag[fromPos].FLD_LEVEL >= 100)
                                    //    {
                                    //        SynthesisHint(type,5,0,Item_In_Bag[fromPos]);
                                    //        return;
                                    //    }
                                    //}
                                    //else
                                    //{
                                    //    SynthesisHint(type, 5, 0, Item_In_Bag[fromPos]);
                                    //    return;
                                    //}
                                    int soTien6 = Phi_HopThanh =
                                            CalculateSyntheticEnhancementCost(enhancingItem, fromPos, type);
                                        HcItimesClass hcItimesClass124 = new()
                                        {
                                            Position = fromPos,
                                            VatPham = Item_In_Bag[fromPos].VatPham_byte
                                        };
                                        Item_In_Bag[fromPos].Khoa_Chat = true;
                                        HopThanhVatPham_Table.Add(1, hcItimesClass124);
                                        SynthesisHint(type, 1, soTien6, Item_In_Bag[fromPos]);
                                    // SynthesisHint(type, 5, 0, Item_In_Bag[fromPos]);
                                }
                                else if (enhancingItem.FLD_RESIDE2 == 1 || enhancingItem.FLD_RESIDE2 == 2 ||  enhancingItem.FLD_RESIDE2 == 5 || enhancingItem.FLD_RESIDE2 == 6 || enhancingItem.FLD_RESIDE2 == 14)
                                {
                                    //if (ThuyTinhPhuNeo_1_3_TB.Contains(itemID) )
                                    //{
                                    //    if (Item_In_Bag[fromPos].FLD_CuongHoaSoLuong >= 10){
                                    //        SynthesisHint(type,5,0,Item_In_Bag[fromPos]);
                                    //        HeThongNhacNho("Vui lòng sử dụng vật phẩm cường hóa dưới 10",6,"Hệ Thống");
                                    //        return;
                                    //    }
                                    //}
                                    //else if (ThuyNgocPhuNeo_Giam2Cap_TB.Contains(itemID))
                                    //{
                                    //    if (Item_In_Bag[fromPos].FLD_CuongHoaSoLuong < 4)
                                    //    {
                                    //        SynthesisHint(type,5,0,Item_In_Bag[fromPos]);
                                    //        HeThongNhacNho("Vui lòng sử dụng vật phẩm cường hóa 4 trở lên",6,"Hệ Thống");
                                    //        return;
                                    //    }
                                    //    else if (Item_In_Bag[fromPos].FLD_CuongHoaSoLuong >= 10)
                                    //    {
                                    //        SynthesisHint(type,5,0,Item_In_Bag[fromPos]);
                                    //        HeThongNhacNho("Vui lòng sử dụng vật phẩm cường hóa dưới 10",6,"Hệ Thống");
                                    //        return;
                                    //    }
                                    //}
                                    //else if (ThuyNgocPhuNeo_Giam2Cap_TB_Hon10.Contains(itemID))
                                    //{
                                    //    if (Item_In_Bag[fromPos].FLD_CuongHoaSoLuong < 10)
                                    //    {
                                    //        SynthesisHint(type,5,0,Item_In_Bag[fromPos]);
                                    //        HeThongNhacNho("Vui lòng sử dụng vật phẩm cường hóa trên 10",6,"Hệ Thống");
                                    //        return;
                                    //    }
                                    //}
                                    //else if (TinhPhoPhuNeo_1_3_TB.Contains(itemID)){
                                    //    if (Item_In_Bag[fromPos].FLD_LEVEL >= 100)
                                    //    {
                                    //        SynthesisHint(type,5,0,Item_In_Bag[fromPos]);
                                    //        return;
                                    //    }
                                    //}
                                    //else
                                    //{
                                    //    SynthesisHint(type, 5, 0, Item_In_Bag[fromPos]);
                                    //    return;
                                    //}
                                    int soTien6 = Phi_HopThanh =
                                            CalculateSyntheticEnhancementCost(enhancingItem, fromPos, type);
                                        HcItimesClass hcItimesClass124 = new()
                                        {
                                            Position = fromPos,
                                            VatPham = Item_In_Bag[fromPos].VatPham_byte
                                        };
                                        Item_In_Bag[fromPos].Khoa_Chat = true;
                                        HopThanhVatPham_Table.Add(1, hcItimesClass124);
                                        SynthesisHint(type, 1, soTien6, Item_In_Bag[fromPos]);
                                }
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex98)
        {
            Form1.WriteLine(1,
                "InputItem_EnhanceCharm error" + base.Userid + "]-[" + base.UserName + "]" + ex98.Message);
        }
    }

    private void SubmitEnhanceNpc(int type)
    {
        try
        {
            HcItimesClass enhancedItem;
            HcItimesClass hcItimesClass37;
            HcItimesClass hcItimesClass38;
            HcItimesClass hcItimesClass39;
            HcItimesClass phucVanPhu;
            ItmeClass value33;
            if(HopThanhVatPham_Table.Count > 0 && (base.CurrentOperationType == 323))
            {
                enhancedItem = null;
                hcItimesClass37 = null;
                hcItimesClass38 = null;
                hcItimesClass39 = null;
                phucVanPhu = null;
                if(HopThanhVatPham_Table.ContainsKey(1))
                {
                    enhancedItem = HopThanhVatPham_Table[1];
                }
                if(HopThanhVatPham_Table.ContainsKey(2))
                {
                    hcItimesClass37 = HopThanhVatPham_Table[2];
                }
                if(HopThanhVatPham_Table.ContainsKey(3))
                {
                    hcItimesClass38 = HopThanhVatPham_Table[3];
                }
                if(HopThanhVatPham_Table.ContainsKey(4))
                {
                    hcItimesClass39 = HopThanhVatPham_Table[4];
                }
                if(HopThanhVatPham_Table.ContainsKey(5))
                {
                    phucVanPhu = HopThanhVatPham_Table[5];
                }
                if(BitConverter.ToInt64(Item_In_Bag[enhancedItem.Position].ItemGlobal_ID, 0) !=
                   BitConverter.ToInt64(enhancedItem.ItemGlobal_ID, 0))
                {
                    SynthesisHint(type, 2, 0, Item_In_Bag[enhancedItem.Position]);
                    HopThanhVatPham_Table.Clear();
                } else if(hcItimesClass37 == null)
                {
                    SynthesisHint(type, 2, 0, Item_In_Bag[enhancedItem.Position]);
                    HopThanhVatPham_Table.Clear();
                } else if(hcItimesClass37 != null && BitConverter.ToInt64(Item_In_Bag[hcItimesClass37.Position].ItemGlobal_ID, 0) !=
                          BitConverter.ToInt64(hcItimesClass37.ItemGlobal_ID, 0))
                {
                    SynthesisHint(type, 2, 0, Item_In_Bag[hcItimesClass37.Position]);
                    HopThanhVatPham_Table.Clear();
                } else if(hcItimesClass38 != null && BitConverter.ToInt64(Item_In_Bag[hcItimesClass38.Position].ItemGlobal_ID, 0) !=
                          BitConverter.ToInt64(hcItimesClass38.ItemGlobal_ID, 0))
                {
                    SynthesisHint(type, 2, 0, Item_In_Bag[hcItimesClass38.Position]);
                    HopThanhVatPham_Table.Clear();
                } else if(hcItimesClass39 != null && BitConverter.ToInt64(Item_In_Bag[hcItimesClass39.Position].ItemGlobal_ID, 0) !=
                          BitConverter.ToInt64(hcItimesClass39.ItemGlobal_ID, 0))
                {
                    SynthesisHint(type, 2, 0, Item_In_Bag[hcItimesClass39.Position]);
                    HopThanhVatPham_Table.Clear();
                } else if(phucVanPhu != null && BitConverter.ToInt64(Item_In_Bag[phucVanPhu.Position].ItemGlobal_ID, 0) !=
                          BitConverter.ToInt64(phucVanPhu.ItemGlobal_ID, 0))
                {
                    SynthesisHint(type, 2, 0, Item_In_Bag[phucVanPhu.Position]);
                    HopThanhVatPham_Table.Clear();
                } else
                {
                    enhancedItem.DatDuocThuocTinh();
                    enhancedItem.CuongHoaThuocTinhGiaiDoan();
                    if(!World.Itme.TryGetValue(BitConverter.ToInt32(enhancedItem.VatPham_id, 0), out value33))
                    {
                        SynthesisHint(type, 2, 0, Item_In_Bag[enhancedItem.Position]);
                        HopThanhVatPham_Table.Clear();
                    } else if(value33.FLD_RESIDE2 != 1 && value33.FLD_RESIDE2 != 2 && value33.FLD_RESIDE2 !=
                              4 && value33.FLD_RESIDE2 != 5 && value33.FLD_RESIDE2 != 6 && value33.FLD_RESIDE2 !=
                              14 && value33.FLD_RESIDE2 != 23 && value33.FLD_RESIDE2 != 24 && value33.FLD_RESIDE2 !=
                              25)
                    {
                        SynthesisHint(type, 2, 0, Item_In_Bag[enhancedItem.Position]);
                        HopThanhVatPham_Table.Clear();
                    } else if(Item_In_Bag[enhancedItem.Position].Vat_Pham_Khoa_Lai)
                    {
                        SynthesisHint(type, 2, 0, Item_In_Bag[enhancedItem.Position]);
                        HopThanhVatPham_Table.Clear();
                    } else if(enhancedItem.CuongHoaSoLuong >= 16)
                    {
                        SynthesisHint(type, 2, 0, Item_In_Bag[enhancedItem.Position]);
                        HopThanhVatPham_Table.Clear();
                    } else if(value33.FLD_LEVEL >= 130)
                    {
                        if(hcItimesClass37 != null && BitConverter.ToInt32(hcItimesClass37.VatPham_id, 0) !=
                           800000060)
                        {
                            SynthesisHint(type, 2, 0, Item_In_Bag[enhancedItem.Position]);
                            HopThanhVatPham_Table.Clear();
                        } else if(hcItimesClass38 != null && BitConverter.ToInt32(hcItimesClass38.VatPham_id, 0) !=
                                  800000060)
                        {
                            SynthesisHint(type, 2, 0, Item_In_Bag[enhancedItem.Position]);
                            HopThanhVatPham_Table.Clear();
                        } else
                        {
                            if(hcItimesClass39 == null || BitConverter.ToInt32(hcItimesClass39.VatPham_id, 0) ==
                               800000060)
                            {
                                goto IL_16318;
                            }
                            SynthesisHint(type, 2, 0, Item_In_Bag[enhancedItem.Position]);
                            HopThanhVatPham_Table.Clear();
                        }
                    } else if(hcItimesClass37 != null && BitConverter.ToInt32(hcItimesClass37.VatPham_id, 0) !=
                              800000006)
                    {
                        SynthesisHint(type, 2, 0, Item_In_Bag[enhancedItem.Position]);
                        HopThanhVatPham_Table.Clear();
                    } else if(hcItimesClass38 != null && BitConverter.ToInt32(hcItimesClass38.VatPham_id, 0) !=
                              800000006)
                    {
                        SynthesisHint(type, 2, 0, Item_In_Bag[enhancedItem.Position]);
                        HopThanhVatPham_Table.Clear();
                    } else
                    {
                        if(hcItimesClass39 == null || BitConverter.ToInt32(hcItimesClass39.VatPham_id, 0) ==
                           800000006)
                        {
                            goto IL_16318;
                        }
                        SynthesisHint(type, 2, 0, Item_In_Bag[enhancedItem.Position]);
                        HopThanhVatPham_Table.Clear();
                    }
                }
            }
            return;
            IL_16318:
            if(Phi_HopThanh <= 0)
            {
                goto IL_1639d;
            }
            if(base.Player_Money >= Phi_HopThanh)
            {
                HeThongNhacNho($"Đã Sử dụng {Phi_HopThanh:N0} Lượng");
                KiemSoatGold_SoLuong(Phi_HopThanh, 0);
                UpdateMoneyAndWeight();
                goto IL_1639d;
            }
            SynthesisHint(11, 4, 0, Item_In_Bag[enhancedItem.Position]);
            HopThanhVatPham_Table.Clear();
            goto end_IL_15ad5;
            IL_1639d:
            double num31 = RNG.Next(1, 100);
            if(phucVanPhu != null)
            {
                num31 += (double)NeoLuckyCharmBonus(BitConverter.ToInt32(phucVanPhu.VatPham_id, 0));
            }
            if(NguyenBao_HopThanh_MoRa == 1)
            {
                num31 += 5.0;
            }
            if(base.FLD_VIP == 1)
            {
                num31 += 100.0 * World.VIPTyLe_HopThanhGiaTang_TiLePhanTram;
            }
            if(World.TyLe_CuongHoa != 0.0)
            {
                num31 += 100.0 * World.TyLe_CuongHoa;
            }
            if(base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
            {
                num31 += 100.0 * base.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
            }
            var option_before = Item_In_Bag[enhancedItem.Position].FLD_MAGIC0;
            if(base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram != 0.0)
            {
                num31 += 100.0 * base.FLD_TrangBi_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram;
            }
            if(enhancedItem.ThuocTinh1.ThuocTinhSoLuong + enhancedItem.ThuocTinh2.ThuocTinhSoLuong !=
               0 && hcItimesClass38 == null)
            {
                HeThongNhacNho("石头SoLuong不足,最少需要2颗CuongHoa石.");
            } else if(enhancedItem.ThuocTinh1.ThuocTinhSoLuong + enhancedItem.ThuocTinh2.ThuocTinhSoLuong !=
                      0 && enhancedItem.ThuocTinh3.ThuocTinhSoLuong + enhancedItem.ThuocTinh4.ThuocTinhSoLuong !=
                      0 && hcItimesClass39 == null)
            {
                HeThongNhacNho("石头SoLuong不足,最少需要3颗CuongHoa石.");
            } else
            {
                if((enhancedItem.CuongHoaSoLuong == 0 && !(num31 < 100.0 - World.CuongHoa1TyLe_HopThanh)) || (enhancedItem.CuongHoaSoLuong ==
                       1 && !(num31 < 100.0 - World.CuongHoa2TyLe_HopThanh)) || (enhancedItem.CuongHoaSoLuong ==
                       2 && !(num31 < 100.0 - World.CuongHoa3TyLe_HopThanh)) || (enhancedItem.CuongHoaSoLuong ==
                       3 && !(num31 < 100.0 - World.CuongHoa4TyLe_HopThanh)) || (enhancedItem.CuongHoaSoLuong ==
                       4 && !(num31 < 100.0 - World.CuongHoa5TyLe_HopThanh)) || (enhancedItem.CuongHoaSoLuong ==
                       5 && !(num31 < 100.0 - World.CuongHoa6TyLe_HopThanh)) || (enhancedItem.CuongHoaSoLuong ==
                       6 && !(num31 < 100.0 - World.CuongHoa7TyLe_HopThanh)) || (enhancedItem.CuongHoaSoLuong ==
                       7 && !(num31 < 100.0 - World.CuongHoa8TyLe_HopThanh)) || (enhancedItem.CuongHoaSoLuong ==
                       8 && !(num31 < 100.0 - World.CuongHoa9TyLe_HopThanh)) || (enhancedItem.CuongHoaSoLuong ==
                       9 && !(num31 < 100.0 - World.CuongHoa10TyLe_HopThanh)) || (enhancedItem.CuongHoaSoLuong ==
                       10 && !(num31 < 100.0 - World.CuongHoa11TyLe_HopThanh)) || (enhancedItem.CuongHoaSoLuong ==
                       11 && !(num31 < 100.0 - World.CuongHoa12TyLe_HopThanh)) || (enhancedItem.CuongHoaSoLuong ==
                       12 && !(num31 < 100.0 - World.CuongHoa13TyLe_HopThanh)) || (enhancedItem.CuongHoaSoLuong ==
                       13 && !(num31 < 100.0 - World.CuongHoa14TyLe_HopThanh)) || (enhancedItem.CuongHoaSoLuong ==
                       14 && num31 >= 100.0 - World.CuongHoa15TyLe_HopThanh) ||(enhancedItem.CuongHoaSoLuong ==
                       15 && num31 >= 100.0 - World.CuongHoa16TyLe_HopThanh) ) 
                {
                    enhancedItem.CuongHoaLoaiHinh = (value33.FLD_RESIDE2 == 4) ? 1 : 2;
                    enhancedItem.CuongHoaSoLuong++;
                    if(enhancedItem.CuongHoaSoLuong >= 16)
                    {
                        enhancedItem.CuongHoaSoLuong = 16;
                    }
                    enhancedItem.ThietLap_GiaiDoanThuocTinh();
                    if(enhancedItem.CuongHoaSoLuong >= 8)
                    {
                        SendNewsletter(BitConverter.ToInt32(enhancedItem.VatPham_id, 0),base.UserName, enhancedItem.CuongHoaSoLuong, base.Player_Zx);
                    }
                    if(BitConverter.ToInt32(Item_In_Bag[enhancedItem.Position].VatPham_ID, 0) != 0 && BitConverter.ToInt64(Item_In_Bag[enhancedItem.Position].ItemGlobal_ID, 0) ==
                       BitConverter.ToInt64(enhancedItem.ItemGlobal_ID, 0))
                    {
                        SubtractItems(enhancedItem.Position, 1);
                        AddItems(enhancedItem.ItemGlobal_ID, enhancedItem.VatPham_id, enhancedItem.Position, enhancedItem.VatPhamSoLuong, enhancedItem.VatPham_ThuocTinh);
                    }
                    Item_In_Bag[enhancedItem.Position].Khoa_Chat = false;
                    RxjhClass.SyntheticRecord(base.Userid, base.UserName, value33.ItmeNAME, type, "CuongHoa", "Successed", Item_In_Bag[enhancedItem.Position],magic0_before: option_before);
                    SynthesisHint(type, 1, Phi_HopThanh, Item_In_Bag[enhancedItem.Position]);
                } else
                {
                    if(value33.FLD_RESIDE2 != 1 && value33.FLD_RESIDE2 != 2 && value33.FLD_RESIDE2 != 5 && value33.FLD_RESIDE2 !=
                       6)
                    {
                        if(value33.FLD_RESIDE2 == 4)
                        {
                            int num32 = GetParcelVacancy(this);
                            if(num32 != -1)
                            {
                                switch(value33.FLD_JOB_LEVEL)
                                {
                                    case 2:
                                    {
                                        int value41 = RNG.Next(7, 12);
                                        IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1000000729), num32, BitConverter.GetBytes(value41), new byte[56]);
                                        break;
                                    }
                                    case 3:
                                    {
                                        int value40 = RNG.Next(7, 11);
                                        IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1000000730), num32, BitConverter.GetBytes(value40), new byte[56]);
                                        break;
                                    }
                                    case 4:
                                    {
                                        int value39 = RNG.Next(7, 11);
                                        IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1000000731), num32, BitConverter.GetBytes(value39), new byte[56]);
                                        break;
                                    }
                                    case 5:
                                    {
                                        int value38 = RNG.Next(7, 11);
                                        IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1000000732), num32, BitConverter.GetBytes(value38), new byte[56]);
                                        break;
                                    }
                                    case 6:
                                    {
                                        int value37 = RNG.Next(7, 11);
                                        IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1000000759), num32, BitConverter.GetBytes(value37), new byte[56]);
                                        break;
                                    }
                                    case 7:
                                    {
                                        int value36 = RNG.Next(7, 11);
                                        IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1000000760), num32, BitConverter.GetBytes(value36), new byte[56]);
                                        break;
                                    }
                                    case 8:
                                    {
                                        int value35 = RNG.Next(7, 11);
                                        IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1000000761), num32, BitConverter.GetBytes(value35), new byte[56]);
                                        break;
                                    }
                                    case 9:
                                    {
                                        int value34 = RNG.Next(7, 11);
                                        IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1000000762), num32, BitConverter.GetBytes(value34), new byte[56]);
                                        break;
                                    }
                                }
                            }
                        }
                    } else
                    {
                        int value42 = RNG.Next(7, 10);
                        int num33 = GetParcelVacancy(this);
                        if(num33 != -1)
                        {
                            if(value33.FLD_LEVEL >= 140 && value33.FLD_LEVEL < 150)
                            {
                                IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1000000766), num33, BitConverter.GetBytes(value42), new byte[56]);
                            } else if(value33.FLD_LEVEL >= 130 && value33.FLD_LEVEL < 140)
                            {
                                IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1000000765), num33, BitConverter.GetBytes(value42), new byte[56]);
                            } else if(value33.FLD_LEVEL >= 120 && value33.FLD_LEVEL < 130)
                            {
                                IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1000000764), num33, BitConverter.GetBytes(value42), new byte[56]);
                            } else if(value33.FLD_LEVEL >= 115 && value33.FLD_LEVEL < 120)
                            {
                                IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1000000763), num33, BitConverter.GetBytes(value42), new byte[56]);
                            } else if(value33.FLD_LEVEL >= 100 && value33.FLD_LEVEL < 115)
                            {
                                IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1000000744), num33, BitConverter.GetBytes(value42), new byte[56]);
                            } else if(value33.FLD_LEVEL >= 80 && value33.FLD_LEVEL < 99)
                            {
                                IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1000000743), num33, BitConverter.GetBytes(value42), new byte[56]);
                            } else if(value33.FLD_LEVEL >= 60 && value33.FLD_LEVEL < 79)
                            {
                                IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1000000742), num33, BitConverter.GetBytes(value42), new byte[56]);
                            } else if(value33.FLD_LEVEL >= 40 && value33.FLD_LEVEL < 59)
                            {
                                IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1000000741), num33, BitConverter.GetBytes(value42), new byte[56]);
                            }
                        }
                    }
                    SynthesisHint(type, 0, Phi_HopThanh, Item_In_Bag[enhancedItem.Position]);
                    RxjhClass.SyntheticRecord(base.Userid, base.UserName, value33.ItmeNAME, type, "CuongHoa", "Failed", Item_In_Bag[enhancedItem.Position],-1,magic0_before: option_before);
                    SubtractItems(enhancedItem.Position, 1);
                }
                SubtractItems(hcItimesClass37.Position, 1);
                if(hcItimesClass38 != null)
                {
                    SubtractItems(hcItimesClass38.Position, 1);
                }
                if(hcItimesClass39 != null)
                {
                    SubtractItems(hcItimesClass39.Position, 1);
                }
                if(phucVanPhu != null)
                {
                    SubtractItems(phucVanPhu.Position, 1);
                }
                HopThanhVatPham_Table.Clear();
                SynthesisSystemUnlocked();
                NguyenBao_HopThanh_MoRa = 0;
                Phi_HopThanh = 0;
            }
            end_IL_15ad5:;
        } catch(Exception ex38)
        {
            NguyenBao_HopThanh_MoRa = 0;
            Form1.WriteLine(1, "SubmitEnhanceNpc error![" + base.Userid + "]-[" + base.UserName + "]" + ex38.Message);
        }
    }

    private void InputStone_EnhanceNpc(int type, int fromPos)
    {
        try
        {
            if (HopThanhVatPham_Table.ContainsKey(4))
            {
                return;
            }

            HcItimesClass hcItimesClass2 = null;
            if (HopThanhVatPham_Table.ContainsKey(1))
            {
                hcItimesClass2 = HopThanhVatPham_Table[1];
                if (HopThanhVatPham_Table.Count == 0)
                {
                    SynthesisHint(type, 2, 0, Item_In_Bag[fromPos]);
                }
                else
                {
                    if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) < 1)
                    {
                        return;
                    }

                    if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) != 800000006 &&
                        BitConverter.ToInt32(Item_In_Bag[fromPos].VatPham_ID, 0) !=
                        800000060)
                    {
                        SynthesisHint(type, 7, 0, Item_In_Bag[fromPos]);
                        return;
                    }

                    HcItimesClass hcItimesClass3 = new()
                    {
                        Position = fromPos,
                        VatPham = Item_In_Bag[fromPos].VatPham_byte
                    };
                    Item_In_Bag[fromPos].Khoa_Chat = true;
                    if (!HopThanhVatPham_Table.ContainsKey(2))
                    {
                        HopThanhVatPham_Table.Add(2, hcItimesClass3);
                        SynthesisHint(type, 1, 0, Item_In_Bag[fromPos]);
                    }

                    hcItimesClass2.DatDuocThuocTinh();
                    if (hcItimesClass2.ThuocTinh1.ThuocTinhSoLuong +
                        hcItimesClass2.ThuocTinh2.ThuocTinhSoLuong !=
                        0)
                    {
                        if (BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) < 2)
                        {
                            return;
                        }

                        if (!HopThanhVatPham_Table.ContainsKey(3))
                        {
                            HopThanhVatPham_Table.Add(3, hcItimesClass3);
                            SynthesisHint(type, 1, 0, Item_In_Bag[fromPos]);
                        }
                    }

                    if (hcItimesClass2.ThuocTinh1.ThuocTinhSoLuong +
                        hcItimesClass2.ThuocTinh2.ThuocTinhSoLuong !=
                        0 && hcItimesClass2.ThuocTinh3.ThuocTinhSoLuong +
                        hcItimesClass2.ThuocTinh4.ThuocTinhSoLuong !=
                        0 && BitConverter.ToInt32(Item_In_Bag[fromPos].VatPhamSoLuong, 0) >= 3 &&
                        !HopThanhVatPham_Table.ContainsKey(4))
                    {
                        HopThanhVatPham_Table.Add(4, hcItimesClass3);
                        SynthesisHint(type, 1, 0, Item_In_Bag[fromPos]);
                    }

                    HopThanhVatPham_Table[1].DatDuocThuocTinh();
                    HopThanhVatPham_Table[1].CuongHoaThuocTinhGiaiDoan();
                   
                }
            }
            else
            {
                SynthesisHint(type, 5, 0, Item_In_Bag[fromPos]);
            }

            return;
        }
        catch (Exception ex2)
        {
            Form1.WriteLine(1,
                $"InputStone_EnhanceNpc Error {type}" + base.Userid + "]-[" + base.UserName + "]" + ex2.Message);
            return;
        }
    }

    private void InputItem_EnhanceNpc(int fromPos, int type)
    {
        try
        {
            if ((base.CurrentOperationType == 323) && !HopThanhVatPham_Table.ContainsKey(1))
            {
                ItmeClass value;
                if (Item_In_Bag[fromPos].Vat_Pham_Khoa_Lai)
                {
                    SynthesisHint(type, 2, 0, Item_In_Bag[fromPos]);
                }
                else if (World.Itme.TryGetValue((int)Item_In_Bag[fromPos].GetVatPham_ID, out value))
                {
                    if (value.FLD_RESIDE2 == 12)
                    {
                        SynthesisHint(type, 5, 0, Item_In_Bag[fromPos]);
                    }
                    else if (Item_In_Bag[fromPos].FLD_CuongHoaSoLuong < 16)
                    {
                        int soTien = Phi_HopThanh = CalculateSyntheticEnhancementCost(value, fromPos, type);
                        HcItimesClass hcItimesClass = new()
                        {
                            Position = fromPos,
                            VatPham = Item_In_Bag[fromPos].VatPham_byte
                        };
                        Item_In_Bag[fromPos].Khoa_Chat = true;
                        HopThanhVatPham_Table.Add(1, hcItimesClass);
                        SynthesisHint(type, 1, soTien, Item_In_Bag[fromPos]);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1,
                "InputItem_EnhanceNpc error![" + base.Userid + "]-[" + base.UserName + "]" + ex.Message);
        }
    }
}