using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Windows.Forms;
using NLua;

namespace RxjhServer;

public class ScriptClass
{
    public LuaFunction ItemExchangeFunction;

    private string MetMoi;

    public LuaFunction MonsterDeath;

    public LuaFunction MoRaVatPhamEvent;

    public Lua pLuaVM;
    public Dictionary<int, LuaFunction> QuestEventList = new();

    public string ScriptPath = "script";

    private string string_0;

    private string string_1;

    private string string_10;

    private string string_11;

    private string string_12;

    private string string_13;

    private string string_14;

    private string string_15;

    private string string_16;

    private string string_17;

    private string string_18;

    private string string_19;

    private string string_2;

    private string string_20;

    private string string_21;

    private string string_22;

    private string string_23;

    private string string_24;

    private string string_25;

    private string string_26;

    private string string_27;

    private string string_28;

    private string string_29;

    private string string_3;

    private string string_30;

    private string string_31;

    private string string_32;

    private string string_33;

    private string string_34;

    private string string_35;

    private string string_36;

    private string string_37;

    private string string_38;

    private string string_39;

    private string string_4;

    private string string_40;

    private string string_41;

    private string string_42;

    private string string_43;

    private string string_44;

    private string string_45;

    private string string_46;

    private string string_47;

    private string string_48;

    private string string_49;

    private string string_5;

    private string string_50;

    private string string_51;

    private string string_52;

    private string string_53;

    private string string_54;

    private string string_55;

    private string string_6;

    private string string_7;

    private string string_8;

    private string string_9;

    public ScriptClass()
    {
        DangKy_ScriptAPI();
        ScriptPath = Application.StartupPath + "\\Script";
        GetUrlDirectory(ScriptPath);
        Form1.WriteLine(2, "Hoàn tất tải script");
        RegisterLuaEvent();
    }

    public void RegisterLuaEvent()
    {
        if (MoRaVatPhamEvent != null && pLuaVM != null)
        {
            MoRaVatPhamEvent = pLuaVM.GetFunction("OpenItmeTrigGer");
            MonsterDeath = pLuaVM.GetFunction("DestroyMonster");
            ItemExchangeFunction = pLuaVM.GetFunction("ExchangeItem");
        }
    }

    public void SetUrlFile(string filePath)
    {
        if (pLuaVM == null) throw new InvalidOperationException("pLuaVM has not been initialized.");
        if (!Buffer.IsEquals(Path.GetExtension(filePath), ".lua")) return;
        try
        {
            if (string.IsNullOrEmpty(filePath))
                Form1.WriteLine(88, "Error: string_54 is null or empty.");
            else
                pLuaVM.DoFile(filePath);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(2, "Tăng thêm Lua Kịch bản gốc phạm sai lầm" + ex.Message);
        }
    }

    public void GetUrlDirectory(string string_54)
    {
        if (!Directory.Exists(string_54)) return;
        var files = Directory.GetFiles(string_54);
        var array = files;
        foreach (var t in array)
            try
            {
                SetUrlFile(t);
            }
            catch (Exception value)
            {
                Console.Write(value);
            }

        GetUrlDirectoryS(string_54);
    }

    public void GetUrlDirectoryS(string string_54)
    {
        var directories = Directory.GetDirectories(string_54);
        var array = directories;
        foreach (var string_55 in array) GetUrlDirectory(string_55);
    }

    public void KhoiTao_FuctionName()
    {
        Gui_di_luc_chuyen_sach_ky_nang(1, 1);
        Gui_di_that_chuyen_sach_ky_nang(1, 1);
        Gui_di_bat_chuyen_sach_ky_nang(1, 1);
        Gui_di_cuu_chuyen_sach_ky_nang(1, 1);
        Doi_moi_HP_MP_SP(1);
        DeleteQuestItems(1, 1, 1);
        Kiem_tra_nhiem_vu_vat_pham_so_luong(1, 1, 1);
        Tinh_toan_nhan_vat_co_ban_so_lieu(1);
        Hoc_tap_ky_nang_nhac_nho(1);
        ThietLapNhanVat_NguyenBao(1, 1, 1);
        SetupCharacterMembership(1, 1);
        SetCharacterColor(1, 1);
        ThietLapLevelOnlineLanDau(1, 1);
        Vat_pham_su_dung(1, 1, 1);
        RemoveItemScript(1, 1, 1);
        Nhan_goi_hang(1, 1);
        LearningAscentionAbility(1, 1, 1);
        KhoiTaoBanDau_ChungToiDa_TrangBiVatPham(1);
        UpdateKinhNghiemVaTraiNghiem(1);
        UpdateMoneyAndWeight(1);
        Doi_moi_vo_cong_cung_trang_thai(1);
        Moi_hoc_khi_cong(1, 1);
        Nhan_vat_chuyen_chuc_nghiep(1, 1, 1);
        Dat_duoc_bao_khoa_khong_vi_vi_tri(1);
        Dat_duoc_bao_khoa_khong_vi_vi_tri_to(1, 1);
        Get_Nhiem_vu_giai_doan(1, 1);
        GetWorldItme(0);
        GetPlayerThis(1);
        Gia_tang_vat_pham_Script(1, 1, 1, 1);
        Dat_duoc_nhiem_vu_vat_pham(1, 1, 1);
        IncreaseItemWithAttributes(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1);
        TaskPromptDataSending(1, 1, 1, 1);
        HeThongNhacNho(1, "1", 1, "1");
        Thiet_tri_nhiem_vu_so_lieu(1, 1, 1);
        Hoc_tap_ky_nang(1, 1, 1);
        BuffTLC(1, 1, 1, 1, 1, 1);
        Gui_thong_bao("1", 1);
        Dat_duoc_bao_khoa_khong_vi_so(1);
        GetPlayer_Name(1);
        GetPlayer_Level(1);
        GetPlayer_Job(1);
        GetPlayer_MetMoi(1);
        GetPlayer_Money(1);
        GetPlayer_Zx(1);
        GetPlayer_Job_leve(1);
        GetPlayer_Qigong_Point(1);
        SetPlayer_Money(1, 1);
        SetPlayer_MetMoi(1, 1);
        GetPlayer_Sex(1);
        GetPlayer_Wx(1);
        SetPlayer_Wx(1, 0);
        GetWorldItme_Level(0);
        GetWorldItme_Zx(0);
        GetWorldItme_Reside1(0);
        GetWorldItme_Job_Level(0);
        SetPlayer_ExpErience(1, 0);
        GetPlayer_ExpErience(1);
    }

    public void DangKy_ScriptAPI()
    {
        var num2 = 0;
        try
        {
            KhoiTao_FuctionName();
            pLuaVM = new Lua();
            pLuaVM.RegisterFunction("SendMissionMsg", this, GetType().GetMethod(string_27));
            pLuaVM.RegisterFunction("SendSysMsg", this, GetType().GetMethod(string_28));
            pLuaVM.RegisterFunction("SendKongfuMsg", this, GetType().GetMethod(string_4));
            pLuaVM.RegisterFunction("SendNoticeMsg", this, GetType().GetMethod(string_35));
            pLuaVM.RegisterFunction("AddQuest", this, GetType().GetMethod(string_29));
            pLuaVM.RegisterFunction("AddMission", this, GetType().GetMethod(string_29));
            pLuaVM.RegisterFunction("AddStKongfu", this, GetType().GetMethod(string_11));
            pLuaVM.RegisterFunction("AddQigong", this, GetType().GetMethod(string_16));
            pLuaVM.RegisterFunction("AddSkill", this, GetType().GetMethod(string_30));
            pLuaVM.RegisterFunction("AddSkillBook6", this, GetType().GetMethod(string_31));
            pLuaVM.RegisterFunction("AddSkillBook7", this, GetType().GetMethod(string_32));
            pLuaVM.RegisterFunction("AddSkillBook8", this, GetType().GetMethod(string_33));
            pLuaVM.RegisterFunction("AddSkillBook9", this, GetType().GetMethod(string_34));
            pLuaVM.RegisterFunction("AddSkillBook10", this, GetType().GetMethod("ADD_SACH_10"));
            pLuaVM.RegisterFunction("AddItme", this, GetType().GetMethod(string_23));
            pLuaVM.RegisterFunction("AddItmePropts", this, GetType().GetMethod("AddItmePropts"));
            pLuaVM.RegisterFunction("AddItmeProp", this, GetType().GetMethod(string_26));
            pLuaVM.RegisterFunction("DelItme", this, GetType().GetMethod(string_9));
            pLuaVM.RegisterFunction("AddQuestItme", this, GetType().GetMethod("GiaTangNhiemVu_VatPham"));
            pLuaVM.RegisterFunction("DelQuestItme", this, GetType().GetMethod(string_1));
            pLuaVM.RegisterFunction("GetWorldItme", this, GetType().GetMethod(string_21));
            pLuaVM.RegisterFunction("GetPlayer", this, GetType().GetMethod(string_22));
            pLuaVM.RegisterFunction("GetQuestLevel", this, GetType().GetMethod(string_20));
            pLuaVM.RegisterFunction("GetPackage", this, GetType().GetMethod(string_18));
            pLuaVM.RegisterFunction("GetPackagenum", this, GetType().GetMethod(string_37));
            pLuaVM.RegisterFunction("GetPackages", this, GetType().GetMethod(string_19));
            pLuaVM.RegisterFunction("GetPakItme", this, GetType().GetMethod(string_10));
            pLuaVM.RegisterFunction("GetQuestItme", this, GetType().GetMethod(string_24));
            pLuaVM.RegisterFunction("SetPlayerTransfer", this, GetType().GetMethod(string_17));
            pLuaVM.RegisterFunction("SetQigong", this, GetType().GetMethod(string_16));
            pLuaVM.RegisterFunction("SetPlayerLevel", this, GetType().GetMethod(string_7));
            pLuaVM.RegisterFunction("SetPlayerVIP", this, GetType().GetMethod(string_6));
            pLuaVM.RegisterFunction("SetPlayerRxpiont", this, GetType().GetMethod(string_5));
            pLuaVM.RegisterFunction("UpGongFu", this, GetType().GetMethod(string_15));
            pLuaVM.RegisterFunction("UpMoney", this, GetType().GetMethod(string_14));
            pLuaVM.RegisterFunction("UpExp", this, GetType().GetMethod(string_13));
            pLuaVM.RegisterFunction("UpYzbItme", this, GetType().GetMethod(string_12));
            pLuaVM.RegisterFunction("UpUseItme", this, GetType().GetMethod(string_8));
            pLuaVM.RegisterFunction("CheckQitemNum", this, GetType().GetMethod(string_2));
            pLuaVM.RegisterFunction("UpHpMpSp", this, GetType().GetMethod(string_0));
            pLuaVM.RegisterFunction("UpPlayerBase", this, GetType().GetMethod(string_3));
            pLuaVM.RegisterFunction("AddStateItems", this, GetType().GetMethod(string_36));
            pLuaVM.RegisterFunction("Hfgwuqi", this, GetType().GetMethod("checkEquipSpecialWeapons"));
            pLuaVM.RegisterFunction("Hfgzhuanzhi", this, GetType().GetMethod("UpgradeSpecialWeapons"));
            pLuaVM.RegisterFunction("AddExpToCharacter", this, GetType().GetMethod("AddExpToCharacter"));
            pLuaVM.RegisterFunction("MetMoiToCharacter", this, GetType().GetMethod("MetMoiToCharacter"));
            pLuaVM.RegisterFunction("AddKiToCharacter", this, GetType().GetMethod("AddKiToCharacter"));
            pLuaVM.RegisterFunction("AddMoneyToCharacter", this, GetType().GetMethod("AddMoneyToCharacter"));
            pLuaVM.RegisterFunction("AddWuxunToCharacter", this, GetType().GetMethod("AddWuxunToCharacter"));
            pLuaVM.RegisterFunction("AddVoHoangToCharacter", this, GetType().GetMethod("AddVoHoangToCharacter"));
            pLuaVM.RegisterFunction("AddWuxunToCharacter_CTP", this, GetType().GetMethod("AddWuxunToCharacter_CTP"));
            pLuaVM.RegisterFunction("CheckMoney", this, GetType().GetMethod("CheckMoney"));
            pLuaVM.RegisterFunction("CheckVoHuan", this, GetType().GetMethod("CheckVoHuan"));
            pLuaVM.RegisterFunction("GetPlayer_Name", this, GetType().GetMethod(string_38));
            pLuaVM.RegisterFunction("GetPlayer_Level", this, GetType().GetMethod(string_39));
            pLuaVM.RegisterFunction("GetPlayer_Job", this, GetType().GetMethod(string_40));
            pLuaVM.RegisterFunction("GetPlayer_MetMoi", this, GetType().GetMethod(MetMoi));
            pLuaVM.RegisterFunction("GetPlayer_Zx", this, GetType().GetMethod(string_41));
            pLuaVM.RegisterFunction("GetPlayer_Job_leve", this, GetType().GetMethod(string_42));
            pLuaVM.RegisterFunction("GetPlayer_Qigong_Point", this, GetType().GetMethod(string_43));
            pLuaVM.RegisterFunction("GetPlayer_Sex", this, GetType().GetMethod(string_45));
            pLuaVM.RegisterFunction("SetPlayer_Money", this, GetType().GetMethod(string_44));
            pLuaVM.RegisterFunction("GetPlayer_Wx", this, GetType().GetMethod(string_46));
            pLuaVM.RegisterFunction("SetPlayer_Wx", this, GetType().GetMethod(string_47));
            pLuaVM.RegisterFunction("GetWorldItme_Level", this, GetType().GetMethod(string_48));
            pLuaVM.RegisterFunction("GetWorldItme_Zx", this, GetType().GetMethod(string_49));
            pLuaVM.RegisterFunction("GetWorldItme_Reside1", this, GetType().GetMethod(string_50));
            pLuaVM.RegisterFunction("GetWorldItme_Job_Level", this, GetType().GetMethod(string_51));
            pLuaVM.RegisterFunction("SetPlayer_ExpErience", this, GetType().GetMethod(string_52));
            pLuaVM.RegisterFunction("GetPlayer_ExpErience", this, GetType().GetMethod(string_53));
            pLuaVM.RegisterFunction("SetPlayer_MetMoi", this, GetType().GetMethod(string_54));
            pLuaVM.RegisterFunction("Get_DayQuest", this, GetType().GetMethod("Get_DayQuest"));
            pLuaVM.RegisterFunction("Set_DayQuest", this, GetType().GetMethod("Set_DayQuest"));
            pLuaVM.RegisterFunction("AddBuffToCharacter", this, GetType().GetMethod("AddBuffToCharacter"));
            pLuaVM.RegisterFunction("GetPlayer_Money", this, GetType().GetMethod(string_55));
        }
        catch (Exception ex)
        {
            Form1.WriteLine(2, num2 + "| Đăng kí kịch bản gốc API Phạm sai lầm -" + ex.Message);
        }
    }

    public bool Get_DayQuest(int UserWorldId, int idquest, int count = 1, bool showMsg = true)
    {
        var dem = 0;
        if (World.allConnectedChars.TryGetValue(UserWorldId, out var players))
        {
            var DayQuest_Array = players.FLD_DayQuest_Array.Split(';');
            for (var i = 0; i < DayQuest_Array.Length; i++)
                if (idquest.ToString() == DayQuest_Array[i])
                    dem++;
            if (showMsg) players.HeThongNhacNho("Hôm nay bạn đã nhận: " + dem + "/" + count, 10, "Nhiệm vụ");
            if (dem >= count) return true;
        }

        return false;
    }

    public void Set_DayQuest(int UserWorldId, int idquest, int count = 1)
    {
        if (World.allConnectedChars.TryGetValue(UserWorldId, out var players))
            for (var i = 0; i < count; i++)
            {
                var players2 = players;
                players2.FLD_DayQuest_Array = players2.FLD_DayQuest_Array + idquest + ";";
            }
    }

    public void Remove_DayQuest(int UserWorldId, int idquest)
    {
        if (!World.allConnectedChars.TryGetValue(UserWorldId, out var players)) return;
        var str_DayQuest = "";
        var DayQuest_Array = players.FLD_DayQuest_Array.Split(';');
        for (var i = 0; i < DayQuest_Array.Length; i++)
            if (idquest.ToString() != DayQuest_Array[i])
                str_DayQuest = str_DayQuest + DayQuest_Array[i] + ";";
        players.FLD_DayQuest_Array = str_DayQuest;
    }

    public int GetWorldItme_Level(int int_0)
    {
        ItmeClass value = null;
        while (true)
        {
            switch (int_0 != 0 ? 2 : 0)
            {
                case 0:
                    string_48 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return 0;
                case 2:
                    if (World.Itme.TryGetValue(int_0, out value)) break;
                    return 0;
                default:
                    continue;
                case 1:
                    break;
            }

            break;
        }

        return value.FLD_LEVEL;
    }

    public int GetWorldItme_Zx(int int_0)
    {
        ItmeClass value = null;
        while (true)
        {
            switch (int_0 != 0 ? 2 : 0)
            {
                case 0:
                    string_49 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return 0;
                case 2:
                    if (World.Itme.TryGetValue(int_0, out value)) break;
                    return 0;
                default:
                    continue;
                case 1:
                    break;
            }

            break;
        }

        return value.FLD_ZX;
    }

    public int GetWorldItme_Reside1(int int_0)
    {
        ItmeClass value = null;
        while (true)
        {
            switch (int_0 != 0 ? 2 : 0)
            {
                case 0:
                    string_50 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return 0;
                case 2:
                    if (World.Itme.TryGetValue(int_0, out value)) break;
                    return 0;
                default:
                    continue;
                case 1:
                    break;
            }

            break;
        }

        return value.FLD_RESIDE1;
    }

    public int GetWorldItme_Job_Level(int int_0)
    {
        ItmeClass value = null;
        while (true)
        {
            switch (int_0 != 0 ? 2 : 0)
            {
                case 0:
                    string_51 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return 0;
                case 2:
                    if (World.Itme.TryGetValue(int_0, out value)) break;
                    return 0;
                default:
                    continue;
                case 1:
                    break;
            }

            break;
        }

        return value.FLD_JOB_LEVEL;
    }

    public void SetPlayer_ExpErience(int int_0, int int_1)
    {
        Players value = null;
        while (true)
            switch (int_0 == 1 ? 4 : 3)
            {
                case 0:
                    return;
                case 3:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) goto case 2;
                    return;
                case 2:
                    value.Player_ExpErience += int_1;
                    return;
                case 4:
                    string_52 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
            }
    }

    public int GetPlayer_ExpErience(int int_0)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 != 1 ? 2 : 0)
            {
                case 0:
                    string_53 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return 0;
                case 2:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return 0;
                default:
                    continue;
                case 1:
                    break;
            }

            break;
        }

        return value.Player_ExpErience;
    }

    public void SetPlayer_Wx(int int_0, int int_1)
    {
        Players value = null;
        while (true)
            switch (int_0 == 1 ? 4 : 3)
            {
                case 0:
                    return;
                case 3:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) goto case 2;
                    return;
                case 2:
                    value.Player_WuXun += int_1;
                    return;
                case 4:
                    string_47 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
            }
    }

    public int GetPlayer_Wx(int int_0)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 1 : 0)
            {
                case 0:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return 0;
                case 1:
                    string_46 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return 0;
                default:
                    continue;
                case 2:
                    break;
            }

            break;
        }

        return value.Player_WuXun;
    }

    public int GetPlayer_Sex(int int_0)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 1 : 0)
            {
                case 0:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return 0;
                case 1:
                    string_45 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return 0;
                default:
                    continue;
                case 2:
                    break;
            }

            break;
        }

        return value.Player_Sex;
    }

    public string GetPlayer_Name(int int_0)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 1 : 0)
            {
                case 0:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return string.Empty;
                case 1:
                    string_38 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return string.Empty;
                default:
                    continue;
                case 2:
                    break;
            }

            break;
        }

        return value.UserName;
    }

    public int GetPlayer_Qigong_Point(int int_0)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 1 : 0)
            {
                case 0:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return 0;
                case 1:
                    string_43 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return 0;
                default:
                    continue;
                case 2:
                    break;
            }

            break;
        }

        return value.Player_Qigong_point;
    }

    public void SetPlayer_Money(int int_0, int int_1)
    {
        Players value = null;
        while (true)
            switch (int_0 != 1 ? 1 : 3)
            {
                case 2:
                    return;
                case 1:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) goto case 0;
                    return;
                case 0:
                    value.Player_Money += int_1;
                    return;
                case 3:
                    string_44 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
            }
    }

    public int GetPlayer_Level(int int_0)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 1 : 0)
            {
                case 0:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return 0;
                case 1:
                    string_39 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return 0;
                default:
                    continue;
                case 2:
                    break;
            }

            break;
        }

        return value.Player_Level;
    }

    public int GetPlayer_Job(int int_0)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 1 : 0)
            {
                case 0:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return 0;
                case 1:
                    string_40 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return 0;
                default:
                    continue;
                case 2:
                    break;
            }

            break;
        }

        return value.Player_Job;
    }

    public int GetPlayer_MetMoi(int int_0)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 1 : 0)
            {
                case 0:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return 0;
                case 1:
                    MetMoi = new StackTrace().GetFrame(0).GetMethod().Name;
                    return 0;
                default:
                    continue;
                case 2:
                    break;
            }

            break;
        }

        return value.Player_MetMoi;
    }

    public long GetPlayer_Money(int int_0)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 1 : 0)
            {
                case 0:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return 0L;
                case 1:
                    string_55 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return 0L;
                default:
                    continue;
                case 2:
                    break;
            }

            break;
        }

        return value.Player_Money;
    }

    public void SetPlayer_MetMoi(int int_0, int int_1)
    {
        Players value = null;
        while (true)
            switch (int_0 != 1 ? 1 : 3)
            {
                case 2:
                    return;
                case 1:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) goto case 0;
                    return;
                case 0:
                    value.Player_MetMoi -= int_1;
                    return;
                case 3:
                    string_54 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
            }
    }

    public int GetPlayer_Zx(int int_0)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 1 : 0)
            {
                case 0:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return 0;
                case 1:
                    string_41 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return 0;
                default:
                    continue;
                case 2:
                    break;
            }

            break;
        }

        return value.Player_Zx;
    }

    public int GetPlayer_Job_leve(int int_0)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 1 : 0)
            {
                case 0:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return 0;
                case 1:
                    string_42 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return 0;
                default:
                    continue;
                case 2:
                    break;
            }

            break;
        }

        return value.Player_Job_level;
    }

    public void DeleteQuestItems(int int_0, int int_1, int int_2)
    {
        Players value = null;
        while (true)
            switch (int_0 != 1 ? 1 : 3)
            {
                case 2:
                    return;
                case 1:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) goto case 0;
                    return;
                case 0:
                    value.DelTaskItem(int_1, int_2);
                    return;
                case 3:
                    string_1 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
            }
    }

    public void Tinh_toan_nhan_vat_co_ban_so_lieu(int int_0)
    {
        Players value = null;
        while (true)
            switch (int_0 != 1 ? 1 : 3)
            {
                case 2:
                    return;
                case 1:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) goto case 0;
                    return;
                case 0:
                    value.TinhToan_NhanVatCoBan_DuLieu();
                    return;
                case 3:
                    string_3 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
            }
    }

    public void Doi_moi_HP_MP_SP(int int_0)
    {
        Players value = null;
        while (true)
            switch (int_0 != 1 ? 1 : 3)
            {
                case 2:
                    return;
                case 1:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) goto case 0;
                    return;
                case 0:
                    value.CapNhat_HP_MP_SP();
                    return;
                case 3:
                    string_0 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
            }
    }

    public void UpgradeSpecialWeapons(int UserWorldId)
    {
    }

    public void ADD_SACH_10(int UserWorldId, int vitri)
    {
        if (World.allConnectedChars.TryGetValue(UserWorldId, out var players))
            players.Gui_di_muoi_chuyen_sach_ky_nang(vitri);
    }

    public bool checkEquipSpecialWeapons(int UserWorldId)
    {
        return false;
    }

    public void AddItmePropts(int UserWorldId, int 物品ID, int 位置, int 数量, int 物品属性0, int 物品属性1, int 物品属性2, int 物品属性3,
        int 物品属性4, int 初级附魂, int 中级附魂, int 进化, int 绑定, int 天数)
    {
        if (World.allConnectedChars.TryGetValue(UserWorldId, out var players))
            players.IncreaseItemWithAttributes(物品ID, 位置, 数量, 物品属性0, 物品属性1, 物品属性2, 物品属性3, 物品属性4, 初级附魂, 中级附魂, 进化, 绑定, 天数);
    }

    public bool Kiem_tra_nhiem_vu_vat_pham_so_luong(int int_0, int int_1, int int_2)
    {
        Players value = null;
        while (true)
            switch (int_0 != 1 ? 1 : 3)
            {
                case 1:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) goto case 0;
                    return false;
                case 0:
                    return value.Kiem_tra_nhiem_vu_vat_pham_so_luong(int_1, int_2);
                case 3:
                    string_2 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return false;
            }
    }

    public void BuffTLC(int int_0, int int_1, int int_2, int int_3, int int_4, int int_5)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 2 : 4)
            {
                case 0:
                    return;
                case 2:
                    string_36 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
                case 4:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) goto case 1;
                    return;
                case 1:
                case 3:
                    if (value.AppendStatusNewList.Count > 0) break;
                    value.SetAdditionalStatusItems(int_1, int_2, int_3, int_4);
                    value.CalculateCharacterEquipmentData();
                    value.UpdateMartialArtsAndStatus();
                    value.ItemMinusTheNumberOfAttributes(int_5, 1);
                    return;
                default:
                    continue;
                case 6:
                    break;
            }

            break;
        }

        value.NewMedicationReminder();
    }

    public void Hoc_tap_ky_nang_nhac_nho(int int_0)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 1 : 2)
            {
                case 4:
                    return;
                case 1:
                    string_4 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
                case 2:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return;
                default:
                    continue;
                case 0:
                    break;
            }

            break;
        }

        value.LearningSkillsTips();
    }

    public void ThietLapNhanVat_NguyenBao(int int_0, int int_1, int int_2)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 1 : 2)
            {
                case 4:
                    return;
                case 1:
                    string_5 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
                case 2:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return;
                default:
                    continue;
                case 0:
                    break;
            }

            break;
        }

        value.KiemSoatNguyenBao_SoLuong(int_1, int_2);
        value.Save_NguyenBaoData();
    }

    public void SetCharacterColor(int int_0, int int_1)
    {
        if (World.allConnectedChars.TryGetValue(int_0, out var value))
        {
            if (value.FLD_QCVIP == 0)
            {
                var now = DateTime.Now;
                now = DateTime.Now.AddDays(int_1);
                value.FLD_QCVIP = 1;
                value.FLD_QCVIPTIM = now;
                value.HeThongNhacNho("Chúc mừng bạn Nhận được" + int_1 + " Ngày VIP！", 9, ":");
                value.HeThongNhacNho(
                    "Thời hạn VIP của bạn đến:" + value.FLD_QCVIPTIM.ToString("yyyyNãm:MMThaìng:ddNgày: hhGiờ:mmPhút"),
                    9, ":");
            }
            else
            {
                var now2 = DateTime.Now;
                var dateTime5 = value.FLD_QCVIPTIM = now2.AddDays(int_1);
                var dateTime3 = dateTime5;
                var dateTime2 = dateTime3;
                value.HeThongNhacNho(
                    "VIP của bạn được gia hạn đến:" +
                    value.FLD_QCVIPTIM.ToString("yyyy:Nãm:MM:Thaìng:ddNgày hh:Giờ:mm:Phút"), 9, ":");
            }

            value.Bao_ton_tam_mau_hoi_vien_so_lieu();
        }
    }

    public void SetupCharacterMembership(int int_0, int int_1)
    {
        var dateTime = default(DateTime);
        Players value;
        if (int_0 == 1)
        {
            string_6 = new StackTrace().GetFrame(0).GetMethod().Name;
        }
        else if (World.allConnectedChars.TryGetValue(int_0, out value))
        {
            dateTime = value.FLD_VIPTIM;
            var dateTime2 = !(dateTime < DateTime.Now) ? dateTime.AddMonths(int_1) : DateTime.Now.AddMonths(int_1);
            var fLD_VIPTIM = dateTime2;
            value.FLD_VIP = 1;
            value.FLD_VIPTIM = fLD_VIPTIM;
            value.SaveMemberData();
        }
    }

    public void ThietLapLevelOnlineLanDau(int int_0, int int_1)
    {
        Players value;
        if (int_0 == 1)
        {
            string_7 = new StackTrace().GetFrame(0).GetMethod().Name;
        }
        else if (World.allConnectedChars.TryGetValue(int_0, out value))
        {
            value.Player_Level = int_1;
            value.TinhToan_NhanVatCoBan_DuLieu3();
            value.UpdateKinhNghiemVaTraiNghiem();
            value.UpdateMoneyAndWeight();
            value.StoredProcedureForSavingCharacterData();
            value.SaveThePersonalwarehouseStoredProcedure();
            value.SaveTheComprehensivewarehouseStoredProcedure();
            value.SaveGangData();
        }
    }

    public void Vat_pham_su_dung(int int_0, int int_1, int int_2)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 1 : 4)
            {
                case 2:
                    return;
                case 1:
                    string_8 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
                case 4:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return;
                default:
                    continue;
                case 0:
                    break;
            }

            break;
        }

        value.ItemUse(1, int_1, int_2);
    }

    public void RemoveItemScript(int int_0, int int_1, int int_2)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 1 : 4)
            {
                case 2:
                    return;
                case 1:
                    string_9 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
                case 4:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return;
                default:
                    continue;
                case 0:
                    break;
            }

            break;
        }

        value.SubtractItems(int_1, int_2);
    }

    public int Nhan_goi_hang(int int_0, int int_1)
    {
        if (int_1 <= *********)
        {
            if (int_0 == 1)
            {
                string_10 = new StackTrace().GetFrame(0).GetMethod().Name;
                return 0;
            }

            if (World.allConnectedChars.TryGetValue(int_0, out var value2) && value2.Player_Money >= int_1)
                return -int_1;
            value2.HeThongNhacNho("Bạn không đủ ngân lượng");
            return int_1;
        }

        var num = 0;
        if (int_0 == 1)
        {
            string_10 = new StackTrace().GetFrame(0).GetMethod().Name;
            return 0;
        }

        if (World.allConnectedChars.TryGetValue(int_0, out var value))
            for (num = 0; num < 96; num++)
                if (Buffer.ToInt32(value.Item_In_Bag[num].VatPham_ID, 0) == int_1)
                    return num;
        return -1;
    }

    public void LearningAscentionAbility(int int_0, int int_1, int int_2)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 1 : 4)
            {
                case 2:
                    return;
                case 1:
                    string_11 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
                case 4:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return;
                default:
                    continue;
                case 0:
                    break;
            }

            break;
        }

        X_Vo_Cong_Loai.LearnMartialArtsBook(value, int_1, int_2);
    }

    public void KhoiTaoBanDau_ChungToiDa_TrangBiVatPham(int int_0)
    {
        Players value;
        if (int_0 == 1)
            string_12 = new StackTrace().GetFrame(0).GetMethod().Name;
        else if (World.allConnectedChars.TryGetValue(int_0, out value)) value.LoadCharacterWearItem();
    }

    public void UpdateKinhNghiemVaTraiNghiem(int int_0)
    {
        Players value;
        if (int_0 == 1)
            string_13 = new StackTrace().GetFrame(0).GetMethod().Name;
        else if (World.allConnectedChars.TryGetValue(int_0, out value)) value.UpdateKinhNghiemVaTraiNghiem();
    }

    public void UpdateMoneyAndWeight(int int_0)
    {
        Players value;
        if (int_0 == 1)
            string_14 = new StackTrace().GetFrame(0).GetMethod().Name;
        else if (World.allConnectedChars.TryGetValue(int_0, out value)) value.UpdateMoneyAndWeight();
    }

    public void AddWuxunToCharacter(int UserWorldId, int Wuxun)
    {
        if (World.allConnectedChars.TryGetValue(UserWorldId, out var players)) players.GsAddWuxunToCharacter(Wuxun);
    }

    public void AddWuxunToCharacter_CTP(int UserWorldId, int Wuxun)
    {
        if (World.allConnectedChars.TryGetValue(UserWorldId, out var players)) players.GsAddWuxunToCharacter_CTP(Wuxun);
    }

    public void AddBuffToCharacter(int UserWorldId, int PID)
    {
        if (World.allConnectedChars.TryGetValue(UserWorldId, out var players)) players.GsAddBuffToCharacter(PID);
    }

    public void AddVoHoangToCharacter(int UserWorldId, int VoHoang)
    {
        if (World.allConnectedChars.TryGetValue(UserWorldId, out var players)) players.GsAddVoHoangToCharacter(VoHoang);
    }

    public void AddMoneyToCharacter(int UserWorldId, long Money)
    {
        if (World.allConnectedChars.TryGetValue(UserWorldId, out var players)) players.GsAddMoneyToCharacter(Money);
    }

    public void CheckMoney(int UserWorldId, long Money)
    {
        if (World.allConnectedChars.TryGetValue(UserWorldId, out var players)) players.GsCheckMoney(Money);
    }

    public void CheckVoHuan(int UserWorldId, int Money)
    {
        if (World.allConnectedChars.TryGetValue(UserWorldId, out var players)) players.GsVoHuanMoiNgay(Money);
    }

    public void AddExpToCharacter(int UserWorldId, long Exp)
    {
        if (World.allConnectedChars.TryGetValue(UserWorldId, out var players)) players.GsAddExpToCharacter(Exp);
    }

    public void MetMoiToCharacter(int UserWorldId, int MM)
    {
        if (World.allConnectedChars.TryGetValue(UserWorldId, out var players)) players.GsMetMoiToCharacter(MM);
    }

    public int GetRandomFix(int min, int max)
    {
        return World.GetStoneValue_Fix(min, max);
    }

    public int GetRandom(int min, int max)
    {
        return new Random(World.GetRandomSeed()).Next(min, max);
    }

    public void AddKiToCharacter(int UserWorldId, int Ki)
    {
        if (World.allConnectedChars.TryGetValue(UserWorldId, out var players)) players.GsAddKiToCharacter(Ki);
    }

    public void Doi_moi_vo_cong_cung_trang_thai(int int_0)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 != 1 ? 3 : 0)
            {
                case 2:
                    return;
                case 0:
                    string_15 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
                case 3:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return;
                default:
                    continue;
                case 4:
                    break;
            }

            break;
        }

        value.UpdateMartialArtsAndStatus();
    }

    public void Moi_hoc_khi_cong(int int_0, int int_1)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 != 1 ? 3 : 0)
            {
                case 2:
                    return;
                case 0:
                    string_16 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
                case 3:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return;
                default:
                    continue;
                case 4:
                    break;
            }

            break;
        }

        value.NewLearningQigong(int_1, 0);
    }

    public void Nhan_vat_chuyen_chuc_nghiep(int int_0, int int_1, int int_2)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 != 1 ? 3 : 0)
            {
                case 2:
                    return;
                case 0:
                    string_17 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
                case 3:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return;
                default:
                    continue;
                case 4:
                    break;
            }

            break;
        }

        value.ThanNuVoCongDiemSo += 5;
        value.CharacterToProfession(int_1, int_2);
        value.UpdateCharacterData(value);
        value.UpdateKinhNghiemVaTraiNghiem();
        value.LoadCharacterWearItem();
        value.UpdateMoneyAndWeight();
        value.UpdateMartialArtsAndStatus();
    }

    public int Dat_duoc_bao_khoa_khong_vi_vi_tri(int int_0)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 1 : 2)
            {
                case 1:
                    string_18 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return 0;
                case 2:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return -1;
                default:
                    continue;
                case 3:
                    break;
            }

            break;
        }

        return value.GetParcelVacancyPosition();
    }

    public int Dat_duoc_bao_khoa_khong_vi_so(int int_0)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 1 : 2)
            {
                case 1:
                    string_37 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return -1;
                case 2:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return -1;
                default:
                    continue;
                case 3:
                    break;
            }

            break;
        }

        return value.GetParcelVacancyNumber();
    }

    public List<int> Dat_duoc_bao_khoa_khong_vi_vi_tri_to(int int_0, int int_1)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 1 : 2)
            {
                case 1:
                    string_19 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return null;
                case 2:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return new List<int>();
                default:
                    continue;
                case 3:
                    break;
            }

            break;
        }

        return value == null ? new List<int>() : value.GetParcelVacancyPositionGroup(int_1);
    }

    public int Get_Nhiem_vu_giai_doan(int int_0, int int_1)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 1 : 2)
            {
                case 1:
                    string_20 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return 0;
                case 2:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return 0;
                default:
                    continue;
                case 3:
                    break;
            }

            break;
        }

        return value.GetTaskStage(int_1);
    }

    public ItmeClass GetWorldItme(int int_0)
    {
        ItmeClass value = null;
        while (true)
        {
            switch (int_0 == 0 ? 1 : 2)
            {
                case 1:
                    string_21 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return null;
                case 2:
                    if (World.Itme.TryGetValue(int_0, out value)) break;
                    return null;
                default:
                    continue;
                case 3:
                    break;
            }

            break;
        }

        return value;
    }

    public Players GetPlayerThis(int pId)
    {
        if (pId == 1)
        {
            string_22 = new StackTrace().GetFrame(0).GetMethod().Name;
            return null;
        }

        Players value;
        return !World.allConnectedChars.TryGetValue(pId, out value) ? null : value;
    }

    public void Gia_tang_vat_pham_Script(int int_0, int int_1, int int_2, int int_3)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 != 1 ? 1 : 2)
            {
                case 0:
                    return;
                case 1:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return;
                case 2:
                    string_23 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
                default:
                    continue;
                case 4:
                    break;
            }

            break;
        }

        value.Gia_tang_vat_pham_Script(int_1, int_2, int_3);
    }

    public bool Dat_duoc_nhiem_vu_vat_pham(int int_0, int int_1, int int_2)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 != 1 ? 1 : 0)
            {
                case 0:
                    string_24 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return false;
                case 1:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return false;
                default:
                    continue;
                case 3:
                    break;
            }

            break;
        }

        return value.Dat_duoc_nhiem_vu_vat_pham(int_1, int_2);
    }

    public void GiaTangNhiemVu_VatPham(int UserWorldId, int VatPhamID, int SoLuong)
    {
        if (World.allConnectedChars.TryGetValue(UserWorldId, out var players))
            players.SetUpQuestItems(VatPhamID, SoLuong);
    }

    public void IncreaseItemWithAttributes(int int_0, int int_1, int int_2, int int_3, int int_4, int int_5, int int_6,
        int int_7, int int_8, int int_9, int int_10, int int_11, int int_12, int int_13)
    {
        Players value;
        if (int_0 == 1)
            string_26 = new StackTrace().GetFrame(0).GetMethod().Name;
        else if (World.allConnectedChars.TryGetValue(int_0, out value))
            value.IncreaseItemWithAttributes(int_1, int_2, int_3, int_4, int_5, int_6, int_7, int_8, int_9, int_10,
                int_11, int_12, int_13);
    }

    public void TaskPromptDataSending(int int_0, int int_1, int int_2, int int_3)
    {
        Players value;
        if (int_0 == 1)
        {
            Form1.WriteLine(88, "Task " + int_0);
            string_27 = new StackTrace().GetFrame(0).GetMethod().Name;
        }
        else if (World.allConnectedChars.TryGetValue(int_0, out value))
        {
            Form1.WriteLine(88, $"Task {int_0} {int_1} {int_2} {int_3}");
            value.TaskPromptDataSending(int_1, int_2, int_3);
        }
    }

    public void HeThongNhacNho(int int_0, string string_54, int int_1, string string_55)
    {
        Players value;
        if (int_0 == 1)
            string_28 = new StackTrace().GetFrame(0).GetMethod().Name;
        else if (World.allConnectedChars.TryGetValue(int_0, out value))
            value.HeThongNhacNho(string_54, int_1, string_55);
    }

    public void Thiet_tri_nhiem_vu_so_lieu(int int_0, int int_1, int int_2)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 2 : 3)
            {
                case 1:
                    return;
                case 2:
                    string_29 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
                case 3:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return;
                default:
                    continue;
                case 0:
                    break;
            }

            break;
        }

        value.SetTaskData(int_1, int_2);
    }

    public void Hoc_tap_ky_nang(int int_0, int int_1, int int_2)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 2 : 3)
            {
                case 1:
                    return;
                case 2:
                    string_30 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
                case 3:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return;
                default:
                    continue;
                case 0:
                    break;
            }

            break;
        }

        value?.LearningSkills(int_1, int_2);
    }

    public void Gui_di_luc_chuyen_sach_ky_nang(int int_0, int int_1)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 2 : 3)
            {
                case 1:
                    return;
                case 2:
                    string_31 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
                case 3:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return;
                default:
                    continue;
                case 0:
                    break;
            }

            break;
        }

        value?.Gui_di_luc_chuyen_sach_ky_nang(int_1);
    }

    public void Gui_di_that_chuyen_sach_ky_nang(int int_0, int int_1)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 2 : 3)
            {
                case 1:
                    return;
                case 2:
                    string_32 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
                case 3:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return;
                default:
                    continue;
                case 0:
                    break;
            }

            break;
        }

        value?.Gui_di_that_chuyen_sach_ky_nang(int_1);
    }

    public void Gui_di_bat_chuyen_sach_ky_nang(int int_0, int int_1)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 2 : 3)
            {
                case 1:
                    return;
                case 2:
                    string_33 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
                case 3:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return;
                default:
                    continue;
                case 0:
                    break;
            }

            break;
        }

        value?.Gui_di_bat_chuyen_sach_ky_nang(int_1);
    }

    public void Gui_di_cuu_chuyen_sach_ky_nang(int int_0, int int_1)
    {
        Players value = null;
        while (true)
        {
            switch (int_0 == 1 ? 2 : 3)
            {
                case 1:
                    return;
                case 2:
                    string_34 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
                case 3:
                    if (World.allConnectedChars.TryGetValue(int_0, out value)) break;
                    return;
                default:
                    continue;
                case 0:
                    break;
            }

            break;
        }

        value?.Gui_di_cuu_chuyen_sach_ky_nang(int_1);
    }

    public void string_28_(int UserWorldId, string msg, int msgType, string name = "")
    {
        if (!World.allConnectedChars.TryGetValue(UserWorldId, out var players)) return;
        var str = string.Format(msg, players.UserName);
        switch (str)
        {
            case "Chua dat yeu cau, quay lai sau !":
                players.HeThongNhacNho("Chưa đạt yêu cầu, quay lại sau");
                return;
            case "Chua Co Bong Vang":
                players.HeThongNhacNho("Bạn chưa có quyển vỡ 1");
                return;
            case "Chua Co Bong Xanh":
                players.HeThongNhacNho("Bạn chưa có quyển vỡ 2");
                return;
            case "Chua Co Bong Hong":
                players.HeThongNhacNho("Bạn chưa có quyển vỡ 3");
                return;
            case "Chua Co Bong Do":
                players.HeThongNhacNho("Bạn chưa có quyển vỡ 4");
                return;
            case "Diem met moi khong du 50 diem":
                players.HeThongNhacNho("Không đủ 50 điểm mệt mỏi", 10, "Nhiệm vụ");
                return;
        }

        if (str == "Diem met moi khong du 50 diem")
        {
            players.HeThongNhacNho("Không đủ 50 điểm mệt mỏi", 10, "Nhiệm vụ");
            return;
        }

        if (str == "Diem met moi khong du 50 diem")
        {
            players.HeThongNhacNho("Không đủ 50 điểm mệt mỏi", 10, "Nhiệm vụ");
            return;
        }

        switch (str)
        {
            case "Diem met moi khong du 50 diem":
                players.HeThongNhacNho("Không đủ 50 điểm mệt mỏi", 10, "Nhiệm vụ");
                return;
            case "Ban vua tu choi nhiem vu !":
            case "Bạn vừa từ chối nhiệm vụ":
                players.HeThongNhacNho("Bạn đã thăng chức", 10, "Nhiệm vụ");
                return;
            case "Ban chua du level de thang chuc":
                players.HeThongNhacNho("Bạn chưa đủ cấp độ để thăng chức", 10, "Nhiệm vụ");
                return;
            case "Chua thang chuc lan 1":
                players.HeThongNhacNho("Bạn chưa thăng chức lần 1", 10, "Nhiệm vụ");
                return;
            case "Chua thang chuc lan 2":
                players.HeThongNhacNho("Bạn chưa thăng chức lần 2", 10, "Nhiệm vụ");
                return;
            case "Chua thang chuc lan 3":
                players.HeThongNhacNho("Bạn chưa thăng chức lần 3", 10, "Nhiệm vụ");
                return;
            case "Chua thang chuc lan 4":
                players.HeThongNhacNho("Bạn chưa thăng chức lần 4", 10, "Nhiệm vụ");
                return;
            case "Chua thang chuc lan 5":
                players.HeThongNhacNho("Bạn đã thăng chức lần 5", 10, "Nhiệm vụ");
                return;
            case "Chua thang chuc lan 6":
                players.HeThongNhacNho("Bạn chưa thăng thiên 1", 10, "Nhiệm vụ");
                return;
            case "Chua thang chuc lan 7":
                players.HeThongNhacNho("Bạn chưa thăng thiên 2", 10, "Nhiệm vụ");
                return;
            case "Chua thang chuc lan 8":
                players.HeThongNhacNho("Bạn chưa thăng thiên 3", 10, "Nhiệm vụ");
                return;
            case "Chua thang chuc lan 9":
                players.HeThongNhacNho("Bạn chưa thăng thiên 4", 10, "Nhiệm vụ");
                return;
            case "Chua thang chuc lan 10":
                players.HeThongNhacNho("Bạn chưa thăng thiên 5", 10, "Nhiệm vụ");
                return;
            case "Ban khong du kieu dieu kien":
                players.HeThongNhacNho("Bạn không phù hợp điều kiện", 10, "Nhiệm vụ");
                return;
            case "Ban phai dat cap do 60":
                players.HeThongNhacNho("Cấp độ 60 mới có thể nhận", 10, "Nhiệm vụ");
                return;
            case "Huy bo nhiem vu thanh cong !":
                players.HeThongNhacNho("Hủy bỏ nhiệm vụ thành công");
                return;
            case "Chuc mung ban da hoan thanh nhiem vu !!!":
                players.HeThongNhacNho("Chức mừng bạn đã hoàn thành");
                return;
            case "Tui do khong du 1 khong gian trong":
                players.HeThongNhacNho("Túi đồ cần 1 ô trống nhận thưởng");
                return;
            case "Tui do khong du 2 khong gian trong":
                players.HeThongNhacNho("Túi đồ cần 2 ô trống nhận thưởng");
                return;
            case "Tui do khong du 3 khong gian trong":
                players.HeThongNhacNho("Túi đồ cần 3 ô trống nhận thưởng");
                return;
            case "Tui do khong du 4 khong gian trong":
                players.HeThongNhacNho("Túi đồ cần 4 ô trống nhận thưởng");
                return;
            case "Tui do khong du 5 khong gian trong":
                players.HeThongNhacNho("Túi đồ cần 5 ô trống nhận thưởng");
                return;
            case "Tui do khong du 6 khong gian trong":
                players.HeThongNhacNho("Túi đồ cần 6 ô trống nhận thưởng");
                return;
            case "Tui do khong du 7 khong gian trong":
                players.HeThongNhacNho("Túi đồ cần 7 ô trống nhận thưởng");
                return;
            case "Tui do khong du 8 khong gian trong":
                players.HeThongNhacNho("Túi đồ cần 8 ô trống nhận thưởng");
                return;
            case "Tui do khong du 9 khong gian trong":
                players.HeThongNhacNho("Túi đồ cần 9 ô trống nhận thưởng");
                return;
            case "Tui do khong du 10 khong gian trong":
                players.HeThongNhacNho("Túi đồ cần 10 ô trống nhận thưởng");
                return;
            case "Tui do khong du 11 khong gian trong":
                players.HeThongNhacNho("Túi đồ cần 11 ô trống nhận thưởng");
                return;
            case "Tui do khong du 12 khong gian trong":
                players.HeThongNhacNho("Túi đồ cần 12 ô trống nhận thưởng");
                return;
            case "Tui do khong du 13 khong gian trong":
                players.HeThongNhacNho("Túi đồ cần 13 ô trống nhận thưởng");
                return;
            case "Tui do khong du 14 khong gian trong":
                players.HeThongNhacNho("Túi đồ cần 14 ô trống nhận thưởng");
                return;
            case "Tui do khong du 15 khong gian trong":
                players.HeThongNhacNho("Túi đồ cần 15 ô trống nhận thưởng");
                return;
            case "Tui do khong du 16 khong gian trong":
                players.HeThongNhacNho("Túi đồ cần 16 ô trống nhận thưởng");
                return;
            case "Tui do khong du 17 khong gian trong":
                players.HeThongNhacNho("Túi đồ cần 17 ô trống nhận thưởng");
                return;
            case "Tui do khong du 18 khong gian trong":
                players.HeThongNhacNho("Túi đồ cần 18 ô trống nhận thưởng");
                return;
            case "Tui do khong du 19 khong gian trong":
                players.HeThongNhacNho("Túi đồ cần 19 ô trống nhận thưởng");
                return;
            case "Ban khong ngan luong":
                players.HeThongNhacNho("Bạn cần thêm ngân lượng");
                return;
            case "Chua du nguyen lieu":
                players.HeThongNhacNho("Chưa đủ nguyên liệu");
                return;
            case "Chua du ha cap":
                players.HeThongNhacNho("Chưa đủ hồn tinh kết (hạ cấp)");
                return;
            case "Chua du trung cap":
                players.HeThongNhacNho("Chưa đủ hồn tinh kết (trung cấp)");
                return;
            case "Tro thu chinh phai":
                players.HeThongNhacNho("Chưa đủ trợ thủ Chính Phái");
                return;
            case "Tro thu ta phai":
                players.HeThongNhacNho("Chưa đủ trợ thủ Tà Phái");
                return;
            case "Chua du chuong vang":
                players.HeThongNhacNho("Chưa đủ chuông vàng");
                return;
            case "Chua du chuong bac":
                players.HeThongNhacNho("Chưa đủ chuông bạc");
                return;
            case "Chua du cay thong":
                players.HeThongNhacNho("Chưa đủ cây thông noel");
                return;
            case "Chua Co Chu X":
                players.HeThongNhacNho("Chưa có chữ X");
                return;
            case "Chua Co Chu --":
                players.HeThongNhacNho("Chưa có chữ --");
                return;
            case "Chua Co Chu M":
                players.HeThongNhacNho("Chưa có chữ M");
                return;
            case "Chua Co Chu A":
                players.HeThongNhacNho("Chưa có chữ A");
                return;
            case "Chua Co Chu S":
                players.HeThongNhacNho("Chưa có chữ S");
                return;
            case "Moi ngay lam 1 lan":
                players.HeThongNhacNho("Mỗi ngày làm 1 lần");
                return;
            case "Moi ngay lam 2 lan":
                players.HeThongNhacNho("Mỗi ngày làm 2 lần");
                return;
            case "Moi ngay lam 3 lan":
                players.HeThongNhacNho("Mỗi ngày làm 3 lần");
                return;
            case "Moi ngay lam 4 lan":
                players.HeThongNhacNho("Mỗi ngày làm 4 lần");
                return;
            case "Moi ngay lam 5 lan":
                players.HeThongNhacNho("Mỗi ngày làm 5 lần");
                return;
            case "Moi ngay lam 6 lan":
                players.HeThongNhacNho("Mỗi ngày làm 6 lần");
                return;
            case "Moi ngay lam 7 lan":
                players.HeThongNhacNho("Mỗi ngày làm 7 lần");
                return;
            case "Moi ngay lam 8 lan":
                players.HeThongNhacNho("Mỗi ngày làm 8 lần");
                return;
            case "Moi ngay lam 9 lan":
                players.HeThongNhacNho("Mỗi ngày làm 9 lần");
                return;
            case "Moi ngay lam 10 lan":
                players.HeThongNhacNho("Mỗi ngày làm 10 lần");
                return;
            case "Ban phai dat cap do 0 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 0 để nhận");
                return;
            case "Ban phai dat cap do 1 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 1 để nhận");
                return;
            case "Ban phai dat cap do 2 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 2 để nhận");
                return;
            case "Ban phai dat cap do 3 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 3 để nhận");
                return;
            case "Ban phai dat cap do 4 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 4 để nhận");
                return;
            case "Ban phai dat cap do 5 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 5 để nhận");
                return;
            case "Ban phai dat cap do 6 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 6 để nhận");
                return;
            case "Ban phai dat cap do 7 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 7 để nhận");
                return;
            case "Ban phai dat cap do 8 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 8 để nhận");
                return;
            case "Ban phai dat cap do 9 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 9 để nhận");
                return;
            case "Ban phai dat cap do 10 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 10 để nhận");
                return;
            case "Ban phai dat cap do 11 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 11 để nhận");
                return;
            case "Ban phai dat cap do 12 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 12 để nhận");
                return;
            case "Ban phai dat cap do 13 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 13 để nhận");
                return;
            case "Ban phai dat cap do 14 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 14 để nhận");
                return;
            case "Ban phai dat cap do 15 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 15 để nhận");
                return;
            case "Ban phai dat cap do 16 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 16 để nhận");
                return;
            case "Ban phai dat cap do 17 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 17 để nhận");
                return;
            case "Ban phai dat cap do 18 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 18 để nhận");
                return;
            case "Ban phai dat cap do 19 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 19 để nhận");
                return;
            case "Ban phai dat cap do 20 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 20 để nhận");
                return;
            case "Ban phai dat cap do 21 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 21 để nhận");
                return;
            case "Ban phai dat cap do 22 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 22 để nhận");
                return;
            case "Ban phai dat cap do 23 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 23 để nhận");
                return;
            case "Ban phai dat cap do 24 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 24 để nhận");
                return;
            case "Ban phai dat cap do 25 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 25 để nhận");
                return;
            case "Ban phai dat cap do 26 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 26 để nhận");
                return;
            case "Ban phai dat cap do 27 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 27 để nhận");
                return;
            case "Ban phai dat cap do 28 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 28 để nhận");
                return;
            case "Ban phai dat cap do 29 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 29 để nhận");
                return;
            case "Ban phai dat cap do 30 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 30 để nhận");
                return;
            case "Ban phai dat cap do 31 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 31 để nhận");
                return;
            case "Ban phai dat cap do 32 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 32 để nhận");
                return;
            case "Ban phai dat cap do 33 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 33 để nhận");
                return;
            case "Ban phai dat cap do 34 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 34 để nhận");
                return;
            case "Ban phai dat cap do 35 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần thăng chức 2", 10, "Nhiệm vụ");
                return;
            case "Danh cho thang chuc 2 tro len":
                players.HeThongNhacNho("Chỉ dành cho thăng chức 2", 10, "Nhiệm vụ");
                return;
            case "Danh cho thang chuc 3 tro len":
                players.HeThongNhacNho("Chỉ dành cho thăng chức 3", 10, "Nhiệm vụ");
                return;
            case "Danh cho thang chuc 4 tro len":
                players.HeThongNhacNho("Chỉ dành cho thăng chức 4", 10, "Nhiệm vụ");
                return;
            case "Danh cho thang chuc 5 tro len":
                players.HeThongNhacNho("Chỉ dành cho thăng chức 5", 10, "Nhiệm vụ");
                return;
            case "Ban phai dat cap do 36 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 36 để nhận");
                return;
            case "Ban phai dat cap do 37 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 37 để nhận");
                return;
            case "Ban phai dat cap do 38 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 38 để nhận");
                return;
            case "Ban phai dat cap do 39 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 39 để nhận");
                return;
            case "Ban phai dat cap do 40 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 40 để nhận");
                return;
            case "Ban phai dat cap do 41 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 41 để nhận");
                return;
            case "Ban phai dat cap do 42 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 42 để nhận");
                return;
            case "Ban phai dat cap do 43 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 43 để nhận");
                return;
            case "Ban phai dat cap do 44 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 44 để nhận");
                return;
            case "Ban phai dat cap do 45 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 45 để nhận");
                return;
            case "Ban phai dat cap do 46 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 46 để nhận");
                return;
            case "Ban phai dat cap do 47 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 47 để nhận");
                return;
            case "Ban phai dat cap do 48 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 48 để nhận");
                return;
            case "Ban phai dat cap do 49 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 49 để nhận");
                return;
            case "Ban phai dat cap do 50 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 50 để nhận");
                return;
            case "Ban phai dat cap do 51 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 51 để nhận");
                return;
            case "Ban phai dat cap do 52 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 52 để nhận");
                return;
            case "Ban phai dat cap do 53 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 53 để nhận");
                return;
            case "Ban phai dat cap do 54 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 54 để nhận");
                return;
            case "Ban phai dat cap do 55 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 55 để nhận");
                return;
            case "Ban phai dat cap do 56 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 56 để nhận");
                return;
            case "Ban phai dat cap do 57 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 57 để nhận");
                return;
            case "Ban phai dat cap do 58 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 58 để nhận");
                return;
            case "Ban phai dat cap do 59 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 59 để nhận");
                return;
            case "Ban phai dat cap do 60 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần thăng chức 3", 10, "Nhiệm vụ");
                return;
            case "Ban phai dat cap do 61 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 61 để nhận");
                return;
        }

        switch (str)
        {
            case "Ban phai dat cap do 61 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 61 để nhận");
                break;
            case "Ban phai dat cap do 62 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 62 để nhận");
                break;
            case "Ban phai dat cap do 63 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 63 để nhận");
                break;
            case "Ban phai dat cap do 64 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 64 để nhận");
                break;
            case "Ban phai dat cap do 65 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 65 để nhận");
                break;
            case "Ban phai dat cap do 66 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 66 để nhận");
                break;
            case "Ban phai dat cap do 67 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 67 để nhận");
                break;
            case "Ban phai dat cap do 68 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 68 để nhận");
                break;
            case "Ban phai dat cap do 69 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 69 để nhận");
                break;
            case "Ban phai dat cap do 70 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 70 để nhận");
                break;
            case "Ban phai dat cap do 71 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 71 để nhận");
                break;
            case "Ban phai dat cap do 72 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 72 để nhận");
                break;
            case "Ban phai dat cap do 73 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 73 để nhận");
                break;
            case "Ban phai dat cap do 74 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 74 để nhận");
                break;
            case "Ban phai dat cap do 75 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 75 để nhận");
                break;
            case "Ban phai dat cap do 76 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 76 để nhận");
                break;
            case "Ban phai dat cap do 77 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 77 để nhận");
                break;
            case "Ban phai dat cap do 78 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 78 để nhận");
                break;
            case "Ban phai dat cap do 79 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 79 để nhận");
                break;
            case "Ban phai dat cap do 80 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần thăng chức 4", 10, "Nhiệm vụ");
                break;
            case "Ban phai dat cap do 81 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 81 để nhận");
                break;
            case "Ban phai dat cap do 82 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 82 để nhận");
                break;
            case "Ban phai dat cap do 83 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 83 để nhận");
                break;
            case "Ban phai dat cap do 84 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 84 để nhận");
                break;
            case "Ban phai dat cap do 85 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 85 để nhận");
                break;
            case "Ban phai dat cap do 86 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 86 để nhận");
                break;
            case "Ban phai dat cap do 87 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 87 để nhận");
                break;
            case "Ban phai dat cap do 88 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 88 để nhận");
                break;
            case "Ban phai dat cap do 89 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 89 để nhận");
                break;
            case "Ban phai dat cap do 90 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 90 để nhận");
                break;
            case "Ban phai dat cap do 91 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 91 để nhận");
                break;
            case "Ban phai dat cap do 92 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 92 để nhận");
                break;
            case "Ban phai dat cap do 93 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 93 để nhận");
                break;
            case "Ban phai dat cap do 94 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 94 để nhận");
                break;
            case "Ban phai dat cap do 95 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 95 để nhận");
                break;
            case "Ban phai dat cap do 96 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 96 để nhận");
                break;
            case "Ban phai dat cap do 97 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 97 để nhận");
                break;
            case "Ban phai dat cap do 98 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 98 để nhận");
                break;
            case "Ban phai dat cap do 99 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 99 để nhận");
                break;
            case "Ban phai dat cap do 100 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần thăng chức 5", 10, "Nhiệm vụ");
                break;
            case "Ban phai dat cap do 101 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 101 để nhận");
                break;
            case "Ban phai dat cap do 102 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 102 để nhận");
                break;
            case "Ban phai dat cap do 103 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 103 để nhận");
                break;
            case "Ban phai dat cap do 104 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 104 để nhận");
                break;
            case "Ban phai dat cap do 105 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 105 để nhận");
                break;
            case "Ban phai dat cap do 106 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 106 để nhận");
                break;
            case "Ban phai dat cap do 107 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 107 để nhận");
                break;
            case "Ban phai dat cap do 108 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 108 để nhận");
                break;
            case "Ban phai dat cap do 109 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 109 để nhận");
                break;
            case "Ban phai dat cap do 110 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 110 để nhận");
                break;
            case "Ban phai dat cap do 111 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 111 để nhận");
                break;
            case "Ban phai dat cap do 112 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 112 để nhận");
                break;
            case "Ban phai dat cap do 113 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 113 để nhận");
                break;
            case "Ban phai dat cap do 114 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 114 để nhận");
                break;
            case "Ban phai dat cap do 115 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 115 để nhận");
                break;
            case "Ban phai dat cap do 116 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 116 để nhận");
                break;
            case "Ban phai dat cap do 117 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 117 để nhận");
                break;
            case "Ban phai dat cap do 118 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 118 để nhận");
                break;
            case "Ban phai dat cap do 119 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 119 để nhận");
                break;
            case "Ban phai dat cap do 120 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 120 để nhận");
                break;
            case "Ban phai dat cap do 121 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 121 để nhận");
                break;
            case "Ban phai dat cap do 122 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 122 để nhận");
                break;
            case "Ban phai dat cap do 123 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 123 để nhận");
                break;
            case "Ban phai dat cap do 124 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 124 để nhận");
                break;
            case "Ban phai dat cap do 125 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 125 để nhận");
                break;
            case "Ban phai dat cap do 126 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 126 để nhận");
                break;
            case "Ban phai dat cap do 127 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 127 để nhận");
                break;
            case "Ban phai dat cap do 128 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 128 để nhận");
                break;
            case "Ban phai dat cap do 129 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 129 để nhận");
                break;
            case "Ban phai dat cap do 130 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 130 để nhận");
                break;
            case "Ban phai dat cap do 131 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 131 để nhận");
                break;
            case "Ban phai dat cap do 132 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 132 để nhận");
                break;
            case "Ban phai dat cap do 133 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 133 để nhận");
                break;
            case "Ban phai dat cap do 134 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 134 để nhận");
                break;
            case "Ban phai dat cap do 135 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 135 để nhận");
                break;
            case "Ban phai dat cap do 136 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 136 để nhận");
                break;
            case "Ban phai dat cap do 137 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 137 để nhận");
                break;
            case "Ban phai dat cap do 138 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 138 để nhận");
                break;
            case "Ban phai dat cap do 139 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 139 để nhận");
                break;
            case "Ban phai dat cap do 140 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 140 để nhận");
                break;
            case "Ban phai dat cap do 141 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 141 để nhận");
                break;
            case "Ban phai dat cap do 142 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 142 để nhận");
                break;
            case "Ban phai dat cap do 143 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 143 để nhận");
                break;
            case "Ban phai dat cap do 144 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 144 để nhận");
                break;
            case "Ban phai dat cap do 145 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 145 để nhận");
                break;
            case "Ban phai dat cap do 146 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 146 để nhận");
                break;
            case "Ban phai dat cap do 147 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 147 để nhận");
                break;
            case "Ban phai dat cap do 148 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 148 để nhận");
                break;
            case "Ban phai dat cap do 149 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 149 để nhận");
                break;
            case "Ban phai dat cap do 150 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 150 để nhận");
                break;
            case "Ban phai dat cap do 151 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 151 để nhận");
                break;
            case "Ban phai dat cap do 152 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 152 để nhận");
                break;
            case "Ban phai dat cap do 153 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 153 để nhận");
                break;
            case "Ban phai dat cap do 154 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 154 để nhận");
                break;
            case "Ban phai dat cap do 155 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 155 để nhận");
                break;
            case "Ban phai dat cap do 156 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 156 để nhận");
                break;
            case "Ban phai dat cap do 157 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 157 để nhận");
                break;
            case "Ban phai dat cap do 158 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 158 để nhận");
                break;
            case "Ban phai dat cap do 159 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 159 để nhận");
                break;
            case "Ban phai dat cap do 160 moi co the nhan nhiem vu !":
                players.HeThongNhacNho("Bạn cần đạt level 160 để nhận");
                break;
            default:
                players.HeThongNhacNho(str);
                break;
        }
    }

    public void Gui_thong_bao(string string_54, int int_0)
    {
        var num2 = 0;
        Players players = null;
        while (true)
        {
            int num5;
            switch (num2)
            {
                default:
                    num5 = string_54 != "1" ? 1 : 2;
                    break;
                case 2:
                    string_35 = new StackTrace().GetFrame(0).GetMethod().Name;
                    return;
                case 1:
                    try
                    {
                        var enumerator = World.allConnectedChars.Values.GetEnumerator();
                        try
                        {
                            num2 = 8;
                            while (true)
                            {
                                num2 = 4;
                                if (!enumerator.MoveNext()) break;
                                players = enumerator.Current;
                                num2 = 9;
                                if (players != null)
                                {
                                    num2 = 7;
                                    num2 = 5;
                                    switch (int_0)
                                    {
                                        default:
                                            num2 = 1;
                                            num2 = 2;
                                            break;
                                        case 0:
                                            players.SystemNotification(string_54);
                                            num2 = 0;
                                            break;
                                        case 1:
                                            players.SystemRollingAnnouncement(string_54);
                                            num2 = 6;
                                            break;
                                        case 2:
                                            players.HeThongNhacNho(string_54, 10, ":");
                                            num2 = 11;
                                            break;
                                    }
                                }
                            }

                            num2 = 3;
                            num2 = 10;
                            return;
                        }
                        finally
                        {
                            num2 = 0;
                            while (true)
                            {
                                switch (num2)
                                {
                                    case 1:
                                        break;
                                    default:
                                        if (enumerator != null)
                                        {
                                            num2 = 2;
                                            continue;
                                        }

                                        break;
                                    case 2:
                                        enumerator.Dispose();
                                        num2 = 1;
                                        continue;
                                }

                                break;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Form1.WriteLine(1, "Gửi đi thông cáo phạm sai lầm 222: " + ex.Message);
                        return;
                    }
            }

            var num4 = num5;
            var num3 = num4;
            num2 = num3;
        }
    }
}