function TrungThuGold(UserWorldId, ItmeId, site, number)
    local Player = GetPlayer(UserWorldId)
	local Itme = GetWorldItme(ItmeId)
	local weizs=GetPackages(UserWorldId,96)
	
local weiz1=GetPakItme(UserWorldId,999000055)
	if weiz1==-1 then 
		SendSysMsg(UserWorldId,"thieu nguyen lieu ", 9, "Thong Bao")
		return 
	end
local weiz2=GetPakItme(UserWorldId,1008000458)
	if weiz2==-1 then 
		SendSysMsg(UserWorldId,"long den vang" , 9, "Thong Bao")
		return 
	end
local weizs=GetPackages(UserWorldId,1)
if weizs.Count<1 then 
	SendSysMsg(UserWorldId,"Ban khong con cho trong", 9, "Thong Bao")
	return 
end

UpUseItme(UserWorldId,weiz1,1)
UpUseItme(UserWorldId,weiz2,1)

	local tile=math.random(5,200)
		
		if tile > 5 and tile <= 10 then
		local chiso=math.random(700034,700035) -- clvc SC
		local weiz800000061=GetPackages(UserWorldId,1)
		AddItmePropts(UserWorldId,800000061,weizs[0],1,chiso,0,0,0,0,0,0,0,0,0)
	end		
	if tile > 10 and tile <= 20 then
		local weiz1008000169=GetPackages(UserWorldId,1)  --chi ton hoan
        AddItmePropts(UserWorldId,1008000169,weizs[0],1,0,0,0,0,0,0,0,0,0,0)		
	end
		if tile > 20 and tile <= 30 then
		local chiso=math.random(1100098,1100100) -- ulpt sc
		local weiz800000062=GetPackages(UserWorldId,1)
		AddItmePropts(UserWorldId,800000062,weizs[0],1,chiso,0,0,0,0,0,0,0,0,0)		
	end
        if tile > 30 and tile <= 45 then
        local weiz800000005=GetPackages(UserWorldId,1)  --pvp 5%
        AddItmePropts(UserWorldId,800000005,weizs[0],1,0,0,0,0,0,0,0,0,0,0)    		
	end	
		if tile > 45 and tile <= 55 then
		local weiz1000000286=GetPackages(UserWorldId,1)  --the vo huan
        AddItmePropts(UserWorldId,1000000286,weizs[0],5,0,0,0,0,0,0,0,0,0,0)		
	end
		if tile > 55 and tile <= 65 then
		local weiz900000787=GetPackages(UserWorldId,1)  --huy chuong tlc
        AddItmePropts(UserWorldId,900000787,weizs[0],2,0,0,0,0,0,0,0,0,0,0)	
	end
		if tile > 65 and tile <= 75 then
		local weiz1000000288=GetPackages(UserWorldId,1)  --the vo huan
        AddItmePropts(UserWorldId,1000000288,weizs[0],5,0,0,0,0,0,0,0,0,0,0)
	end		
		if tile > 75 and tile <= 85 then
		local weiz1008000144=GetPackages(UserWorldId,1)  --del 5%
        AddItmePropts(UserWorldId,1008000144,weizs[0],1,0,0,0,0,0,0,0,0,0,0) 		
	end
		if tile > 85 and tile <= 88 then
		local weiz1008000113=GetPackages(UserWorldId,1)  --VK 13
        AddItmePropts(UserWorldId,1008000113,weizs[0],1,0,0,0,0,0,0,0,0,0,7) 		
	end
	if tile > 88 and tile <= 95 then
		local weiz1000000289=GetPackages(UserWorldId,1)  --the vo huan
        AddItmePropts(UserWorldId,1000000289,weizs[0],5,0,0,0,0,0,0,0,0,0,0)
	end		
        if tile > 95 and tile <= 107 then
        local weiz1008000143=GetPackages(UserWorldId,1)  --dame 3%
        AddItmePropts(UserWorldId,1008000143,weizs[0],1,0,0,0,0,0,0,0,0,0,0)    		
	end	
		if tile > 107 and tile <= 117 then
		local weiz1007000006=GetPackages(UserWorldId,1)  -- HP 500
        AddItmePropts(UserWorldId,1007000006,weizs[0],1,0,0,0,0,0,0,0,0,0,0)		
	end
		if tile > 117 and tile <= 121 then
		local weiz1008001021=GetPackages(UserWorldId,1)  -- kim long chi theu
        AddItmePropts(UserWorldId,1008001021,weizs[0],1,0,0,0,0,0,0,0,0,0,0)	
	end
	if tile > 121 and tile <= 130 then
		local weiz1000000287=GetPackages(UserWorldId,1)  --the vo huan
        AddItmePropts(UserWorldId,1000000287,weizs[0],5,0,0,0,0,0,0,0,0,0,0)
	end
	if tile > 130 and tile <= 135 then
		local weiz1000000288=GetPackages(UserWorldId,1)  --the vo huan
        AddItmePropts(UserWorldId,1000000288,weizs[0],10,0,0,0,0,0,0,0,0,0,0)	
	end
	if tile > 135 and tile <= 150 then
		local weiz1008000095=GetPackages(UserWorldId,1)  --pill EXP 20%
        AddItmePropts(UserWorldId,1008000095,weizs[0],1,0,0,0,0,0,0,0,0,0,0)
	end
	if tile > 150 and tile <= 156 then
		local weiz1000000290=GetPackages(UserWorldId,1)  --the vo huan
        AddItmePropts(UserWorldId,1000000290,weizs[0],5,0,0,0,0,0,0,0,0,0,0)
	end
	if tile > 156 and tile <= 161 then
		local weiz1008000326=GetPackages(UserWorldId,1)  --yeu hoa ma vo
        AddItmePropts(UserWorldId,1008000326,weizs[0],1,0,0,0,0,0,0,0,0,0,0)
	end
	if tile > 161 and tile <= 165 then
		local weiz1008000116=GetPackages(UserWorldId,1)  --TB 13
        AddItmePropts(UserWorldId,1008000116,weizs[0],1,0,0,0,0,0,0,0,0,0,7) 		
	end
	if tile > 165 and tile <= 168 then
		local weiz1008001074=GetPackages(UserWorldId,1)  -- pvp 20%
        AddItmePropts(UserWorldId,1008001074,weizs[0],1,0,0,0,0,0,0,0,0,0,0)	
	end
	if tile > 168 and tile <= 175 then
		local weiz1000000250=GetPackages(UserWorldId,1)  -- hop trang suc
        AddItmePropts(UserWorldId,1000000250,weizs[0],1,0,0,0,0,0,0,0,0,0,0)	
	end
		if tile > 175 and tile <= 179 then
		local weiz16900354=GetPackages(UserWorldId,1)  -----ao choang nam
		AddItmePropts(UserWorldId,16900354,weizs[0],1,0,10000100,20000100,80000002,90000008,0,0,0,0,5)
	end
	if tile > 179 and tile <= 183 then
		local weiz26900337=GetPackages(UserWorldId,1)  -----ao choang nu
		AddItmePropts(UserWorldId,26900337,weizs[0],1,0,10000100,20000100,80000002,90000008,0,0,0,0,5)
	end	
	if tile > 183 and tile <= 185 then
		local weiz800000013=GetPackages(UserWorldId,1)  -----gold 20
        AddItmePropts(UserWorldId,800000013,weizs[0],1,1200020,0,0,0,0,0,0,0,0,0)
	end	
	if tile > 185 and tile <= 187 then
		local weiz800000013=GetPackages(UserWorldId,1)  -----mm2
        AddItmePropts(UserWorldId,800000013,weizs[0],1,900002,0,0,0,0,0,0,0,0,0)
	end	
	if tile > 187 and tile <= 191 then
		local weiz1000000287=GetPackages(UserWorldId,1)  --the vo huan
        AddItmePropts(UserWorldId,1000000287,weizs[0],10,0,0,0,0,0,0,0,0,0,0)
	end
	if tile > 191 and tile <= 194 then
		local weiz1000000290=GetPackages(UserWorldId,1)  --the vo huan
        AddItmePropts(UserWorldId,1000000290,weizs[0],12,0,0,0,0,0,0,0,0,0,0)
	end
	if tile > 194 and tile <= 197 then
		local weiz1000000330=GetPackages(UserWorldId,1)  ----- soul trung
        AddItmePropts(UserWorldId,1000000330,weizs[0],1,0,0,0,0,0,0,0,0,0,0)	
	end
	if tile > 197 and tile <= 200 then
		local weiz800000061=GetPackages(UserWorldId,1)  ----- kc2 
        AddItmePropts(UserWorldId,800000061,weizs[0],1,800002,0,0,0,0,0,0,0,0,0)	
	end
end