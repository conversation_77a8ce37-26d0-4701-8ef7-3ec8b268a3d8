--[һת����]
function MissIon18(UserWorldId,QuestId, CzId, RwJdId)
local weizs=GetPackages(UserWorldId,96)
	local Player = GetPlayer(UserWorldId)
	if CzId ==1 or CzId==2 then
	SendMissionMsg(UserWorldId,QuestId, 11, RwJdId)
		if Player.Player_Job_leve>=1 then 
		    SendSysMsg(UserWorldId,"Da thang chuc roi", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Level < 10 then 
			SendSysMsg(UserWorldId,"Ban chua du level de thang chuc", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return
		end
			
			if Player.Player_Job == 8 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 9 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 11 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 12 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 13 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
		SetPlayerTransfer(UserWorldId,0,1)
		SetQigong(UserWorldId,5)
        UpHpMpSp(UserWorldId)
		UpGongFu(UserWorldId)
		AddWuxunToCharacter(UserWorldId, 1000)
		Player.Player_Money=Player.Player_Money+20000
		AddItmePropts(UserWorldId,1008000501,weizs[0],1,0,0,0,0,0,0,0,0,0,0) -- cho nay
		UpMoney(UserWorldId)
			if Player.Player_Job == 8 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 9 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 11 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 12 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 13 then 
				Hfgzhuanzhi(UserWorldId)
			end
		AddQuest(UserWorldId,QuestId,1)
		SendMissionMsg(UserWorldId,QuestId, 21, 1)
	end	 
end
--[��ת������]
function MissIon11(UserWorldId,QuestId, CzId, RwJdId)
	local Player = GetPlayer(UserWorldId)
	local weizs=GetPackages(UserWorldId,96)
	if CzId ==1 or CzId==2 then
	SendMissionMsg(UserWorldId,QuestId, 11, RwJdId)
		if Player.Player_Job_leve>=2 then 
			SendSysMsg(UserWorldId,"Da thang chuc roi", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Level < 35 then 
			SendSysMsg(UserWorldId,"Ban chua du level de thang chuc", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Job_leve < 1 then 
			SendSysMsg(UserWorldId,"Chua thang chuc lan 1", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		end
			if Player.Player_Job == 8 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 9 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 11 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 12 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 13 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
		SetPlayerTransfer(UserWorldId,1,2)
		SetQigong(UserWorldId,6)
		UpGongFu(UserWorldId)
		AddWuxunToCharacter(UserWorldId, 2000)
		UpYzbItme(UserWorldId)
		Player.Player_Money=Player.Player_Money+500000
		AddItmePropts(UserWorldId,1008000502,weizs[0],1,0,0,0,0,0,0,0,0,0,0) -- cho nay
		UpMoney(UserWorldId)
			if Player.Player_Job == 8 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 9 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 11 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 12 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 13 then 
				Hfgzhuanzhi(UserWorldId)
			end
		AddQuest(UserWorldId,QuestId,1)
		SendMissionMsg(UserWorldId,QuestId, 21, 1)
	end
end
--[��תа����]
function MissIon12(UserWorldId,QuestId, CzId, RwJdId)
	local Player = GetPlayer(UserWorldId)
	local weizs=GetPackages(UserWorldId,96)
	if CzId ==1 or CzId==2 then
	SendMissionMsg(UserWorldId,QuestId, 11, RwJdId)
		if Player.Player_Job_leve>=2 then 
			SendSysMsg(UserWorldId,"Da thang chuc roi", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Level < 35 then 
			SendSysMsg(UserWorldId,"Ban chua du level de thang chuc", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Job_leve < 1 then 
			SendSysMsg(UserWorldId,"Chua thang chuc lan 1", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		end
			if Player.Player_Job == 8 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 9 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 11 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 12 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 13 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
		SetPlayerTransfer(UserWorldId,2,2)
		SetQigong(UserWorldId,6)
		UpGongFu(UserWorldId)
		AddWuxunToCharacter(UserWorldId, 2000)
		UpYzbItme(UserWorldId)
		Player.Player_Money=Player.Player_Money+500000
		AddItmePropts(UserWorldId,1008000502,weizs[0],1,0,0,0,0,0,0,0,0,0,0) -- cho nay
		UpMoney(UserWorldId)
			if Player.Player_Job == 8 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 9 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 11 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 12 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 13 then 
				Hfgzhuanzhi(UserWorldId)
			end
		AddQuest(UserWorldId,QuestId,1)
		SendMissionMsg(UserWorldId,QuestId, 21, 1)
	end	
end

--[��ת������]
function MissIon45(UserWorldId,QuestId, CzId, RwJdId)
	local Player = GetPlayer(UserWorldId)
	local weizs=GetPackages(UserWorldId,96)
	if CzId ==1 or CzId==2 then
	SendMissionMsg(UserWorldId,QuestId, 11, RwJdId)
		if Player.Player_Job_leve>=3 then 
			SendSysMsg(UserWorldId,"Da thang chuc roi", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Level < 60 then 
			SendSysMsg(UserWorldId,"Ban chua du level de thang chuc", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Job_leve < 2 then 
			SendSysMsg(UserWorldId,"Chua thang chuc lan 2", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		end
			if Player.Player_Job == 8 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 9 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 11 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 12 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 13 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
		SetPlayerTransfer(UserWorldId,Player.Player_Zx,3)
		SetQigong(UserWorldId,7)
		UpGongFu(UserWorldId)
		AddWuxunToCharacter(UserWorldId, 5000)
		Player.Player_Money=Player.Player_Money+1000000
		AddItmePropts(UserWorldId,1008000503,weizs[0],1,0,0,0,0,0,0,0,0,0,0)
		UpMoney(UserWorldId)
			if Player.Player_Job == 8 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 9 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 11 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 12 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 13 then 
				Hfgzhuanzhi(UserWorldId)
			end
		AddQuest(UserWorldId,QuestId,1)
		SendMissionMsg(UserWorldId,QuestId, 21, 1)
	end	
end
--[��תа����]
function MissIon46(UserWorldId,QuestId, CzId, RwJdId)
	local Player = GetPlayer(UserWorldId)
	local weizs=GetPackages(UserWorldId,96)
	if CzId ==1 or CzId==2 then
	SendMissionMsg(UserWorldId,QuestId, 11, RwJdId)
		if Player.Player_Job_leve>=3 then 
			SendSysMsg(UserWorldId,"Da thang chuc roi", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Level < 60 then 
			SendSysMsg(UserWorldId,"Ban chua du level de thang chuc", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Job_leve < 2 then 
			SendSysMsg(UserWorldId,"Chua thang chuc lan 2", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		end
			if Player.Player_Job == 8 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 9 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 11 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 12 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 13 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
		SetPlayerTransfer(UserWorldId,Player.Player_Zx,3)
		SetQigong(UserWorldId,7)
		UpGongFu(UserWorldId)
		AddWuxunToCharacter(UserWorldId, 5000)
		Player.Player_Money=Player.Player_Money+1000000
		AddItmePropts(UserWorldId,1008000503,weizs[0],1,0,0,0,0,0,0,0,0,0,0)
		UpMoney(UserWorldId)
			if Player.Player_Job == 8 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 9 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 11 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 12 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 13 then 
				Hfgzhuanzhi(UserWorldId)
			end
		AddQuest(UserWorldId,QuestId,1)
		SendMissionMsg(UserWorldId,QuestId, 21, 1)
	end	
end
--[��ת������]
function MissIon73(UserWorldId,QuestId, CzId, RwJdId)
	local Player = GetPlayer(UserWorldId)
	local weizs=GetPackages(UserWorldId,96)
	if CzId ==1 or CzId==2 then
	SendMissionMsg(UserWorldId,QuestId, 11, RwJdId)
		if Player.Player_Job_leve>=4 then 
			SendSysMsg(UserWorldId,"Da thang chuc roi", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Level < 80 then 
			SendSysMsg(UserWorldId,"Ban chua du level de thang chuc", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Job_leve < 3 then 
			SendSysMsg(UserWorldId,"Chua thang chuc lan 3", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		end
			if Player.Player_Job == 8 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 9 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 11 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 12 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 13 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
		SetPlayerTransfer(UserWorldId,Player.Player_Zx,4)
		SetQigong(UserWorldId,8)
		SetQigong(UserWorldId,9)
		UpGongFu(UserWorldId)
		AddWuxunToCharacter(UserWorldId, 10000)
		Player.Player_Money=Player.Player_Money+3000000
		AddItmePropts(UserWorldId,1008000505,weizs[0],1,0,0,0,0,0,0,0,0,0,0)
		UpMoney(UserWorldId)
			if Player.Player_Job == 8 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 9 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 11 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 12 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 13 then 
				Hfgzhuanzhi(UserWorldId)
			end
		AddQuest(UserWorldId,QuestId,1)
		SendMissionMsg(UserWorldId,QuestId, 21, 1)
	end	
end
--[��תа����]
function MissIon74(UserWorldId,QuestId, CzId, RwJdId)
	local Player = GetPlayer(UserWorldId)
	local weizs=GetPackages(UserWorldId,96)
	if CzId ==1 or CzId==2 then
	SendMissionMsg(UserWorldId,QuestId, 11, RwJdId)
		if Player.Player_Job_leve>=4 then 
			SendSysMsg(UserWorldId,"Da thang chuc roi", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Level < 80 then 
			SendSysMsg(UserWorldId,"Ban chua du level de thang chuc", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Job_leve < 3 then 
			SendSysMsg(UserWorldId,"Chua thang chuc lan 3", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		end
			if Player.Player_Job == 8 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 9 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 11 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 12 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 13 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
		SetPlayerTransfer(UserWorldId,Player.Player_Zx,4)
		SetQigong(UserWorldId,8)
		SetQigong(UserWorldId,9)
		UpGongFu(UserWorldId)
		AddWuxunToCharacter(UserWorldId, 10000)
		Player.Player_Money=Player.Player_Money+3000000
		AddItmePropts(UserWorldId,1008000505,weizs[0],1,0,0,0,0,0,0,0,0,0,0)
		UpMoney(UserWorldId)
			if Player.Player_Job == 8 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 9 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 11 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 12 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 13 then 
				Hfgzhuanzhi(UserWorldId)
			end
		AddQuest(UserWorldId,QuestId,1)
		SendMissionMsg(UserWorldId,QuestId, 21, 1)
	end	
end


--[��ת����]
function MissIon178(UserWorldId,QuestId, CzId, RwJdId)
	local Player = GetPlayer(UserWorldId)
	local weizs=GetPackages(UserWorldId,96)
	if CzId ==1 or CzId==2 then
	SendMissionMsg(UserWorldId,QuestId, 11, RwJdId)
		if Player.Player_Job_leve>=5 then 
			SendSysMsg(UserWorldId,"Da thang chuc roi", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Level < 100 then 
			SendSysMsg(UserWorldId,"Ban chua du level de thang chuc", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Job_leve < 4 then 
			SendSysMsg(UserWorldId,"Chua thang chuc lan 4", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		end
			if Player.Player_Job == 8 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 9 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 11 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 12 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 13 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
		SetPlayerTransfer(UserWorldId,Player.Player_Zx,5)
		SetQigong(UserWorldId,10)
		AddSkill(UserWorldId,0,25)
		AddSkill(UserWorldId,0,26)
		AddSkill(UserWorldId,0,27)
		UpGongFu(UserWorldId)
		AddWuxunToCharacter(UserWorldId, 15000)
		Player.Player_Money=Player.Player_Money+5000000
		AddItmePropts(UserWorldId,1008000506,weizs[0],1,0,0,0,0,0,0,0,0,0,0)
		UpMoney(UserWorldId)
			if Player.Player_Job == 8 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 9 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 11 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 12 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 13 then 
				Hfgzhuanzhi(UserWorldId)
			end
		AddQuest(UserWorldId,QuestId,1)
		SendMissionMsg(UserWorldId,QuestId, 21, 1)
	end	
end
--[��ת����]
function MissIon300(UserWorldId,QuestId, CzId, RwJdId)
	local Player = GetPlayer(UserWorldId)
	local weizs=GetPackages(UserWorldId,96)
	if CzId ==1 or CzId==2 then
	   SendMissionMsg(UserWorldId,QuestId, 11, RwJdId)
		if Player.Player_Job_leve>=6 then 
			SendSysMsg(UserWorldId,"Da thang chuc roi", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Money < 0 then 
			SendSysMsg(UserWorldId,"Chua du tien", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 	
		elseif Player.Player_Level < 110 then 
			SendSysMsg(UserWorldId,"Ban chua du level de thang chuc", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Job_leve < 5 then 
			SendSysMsg(UserWorldId,"Chua thang chuc lan 5", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		end
		
		if Player.Player_Job == 8 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 9 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 11 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 12 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 13 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
		local weiz=GetPackage(UserWorldId)
		if weiz==-1 then 
			SendSysMsg(UserWorldId,"Khong du cho trong", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 31, RwJdId)
			return 
            Hfgzhuanzhi(UserWorldId)
		end
		SetPlayerTransfer(UserWorldId,Player.Player_Zx,6)
		Player.Player_Money=Player.Player_Money+5000000
		if Player.Player_Sex==1 then
			AddItmePropts(UserWorldId,16903141,weizs[0],1,0,80000001,120000020,90000006,0,0,0,0,1,5)
		else
			AddItmePropts(UserWorldId,26903141,weizs[0],1,0,80000001,120000020,90000006,0,0,0,0,1,5)
		end
		--AddSkillBook6(UserWorldId,weiz);
		UpGongFu(UserWorldId)
		AddWuxunToCharacter(UserWorldId, 20000)
		UpMoney(UserWorldId)
		UpExp(UserWorldId)
		UpYzbItme(UserWorldId)
			if Player.Player_Job == 8 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 9 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 11 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 12 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 13 then 
				Hfgzhuanzhi(UserWorldId)
			end
		AddQuest(UserWorldId,QuestId,1)
		SendMissionMsg(UserWorldId,QuestId, 21, 1)
	end	
end
--[��ת����]
function MissIon301(UserWorldId,QuestId, CzId, RwJdId)
	local Player = GetPlayer(UserWorldId)
	local weizs=GetPackages(UserWorldId,96)
	if CzId ==1 or CzId==2 then
	SendMissionMsg(UserWorldId,QuestId, 11, RwJdId)
		if Player.Player_Job_leve>=7 then 
			SendSysMsg(UserWorldId,"Da thang chuc roi", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Level < 120 then 
			SendSysMsg(UserWorldId,"Ban chua du level de thang chuc", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Job_leve < 6 then 
			SendSysMsg(UserWorldId,"Chua thang chuc lan 6", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		end
			if Player.Player_Job == 8 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 9 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 11 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 12 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 13 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
		local weiz=GetPackage(UserWorldId)
		if weiz==-1 then 
			SendSysMsg(UserWorldId,"Khong du cho trong", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 31, RwJdId)
			return
		end
		--AddSkillBook7(UserWorldId,weiz);

		SetPlayerTransfer(UserWorldId,Player.Player_Zx,7)

		Player.Player_Money=Player.Player_Money+5000000
		AddItmePropts(UserWorldId,1000001188,weizs[0],1,0,0,0,0,0,0,0,0,1,6)
		AddItmePropts(UserWorldId,1000001077,weizs[1],1,0,0,0,0,0,0,0,0,0,0)
		UpGongFu(UserWorldId)
		AddWuxunToCharacter(UserWorldId, 25000)
		UpMoney(UserWorldId)
		UpExp(UserWorldId)
		UpYzbItme(UserWorldId)

			if Player.Player_Job == 8 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 9 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 11 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 12 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 13 then 
				Hfgzhuanzhi(UserWorldId)
			end
		AddQuest(UserWorldId,QuestId,1)
		SendMissionMsg(UserWorldId,QuestId, 21, 1)
	end	
end
--[��תת����]
function MissIon400(UserWorldId,QuestId, CzId, RwJdId)
	local Player = GetPlayer(UserWorldId)
	if CzId ==1 or CzId==2 then
	SendMissionMsg(UserWorldId,QuestId, 11, RwJdId)
		if Player.Player_Job_leve>=8 then 
			SendSysMsg(UserWorldId,"Da thang chuc roi", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Level < 130 then 
			SendSysMsg(UserWorldId,"Ban chua du level de thang chuc", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Job_leve < 7 then 
			SendSysMsg(UserWorldId,"Chua thang chuc lan 7", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		end
			if Player.Player_Job == 8 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 9 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 11 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 12 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 13 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
		local weiz=GetPackage(UserWorldId)
		if weiz==-1 then 
			SendSysMsg(UserWorldId,"Khong du cho trong", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 31, RwJdId)
			return 
		end
		--AddSkillBook8(UserWorldId,weiz);

		SetPlayerTransfer(UserWorldId,Player.Player_Zx,8)
		Player.Player_Money=Player.Player_Money+10000000
		UpGongFu(UserWorldId)
		AddWuxunToCharacter(UserWorldId, 30000)
		UpMoney(UserWorldId)
		UpExp(UserWorldId)
		UpYzbItme(UserWorldId)

			if Player.Player_Job == 8 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 9 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 11 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 12 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 13 then 
				Hfgzhuanzhi(UserWorldId)
			end
		AddQuest(UserWorldId,QuestId,1)
		SendMissionMsg(UserWorldId,QuestId, 21, 1)
	end	
end

function MissIon620(UserWorldId,QuestId, CzId, RwJdId)
	local Player = GetPlayer(UserWorldId)
	if CzId ==1 or CzId==2 then
	SendMissionMsg(UserWorldId,QuestId, 11, RwJdId)
		if Player.Player_Job_leve>=9 then 
			SendSysMsg(UserWorldId,"Da thang chuc roi", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Level < 140 then 
			SendSysMsg(UserWorldId,"Ban chua du level de thang chuc", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Job_leve < 8 then 
			SendSysMsg(UserWorldId,"Chua thang chuc lan 8", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		end
			if Player.Player_Job == 8 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 9 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 11 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 12 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 13 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
		local weiz=GetPackage(UserWorldId)
		if weiz==-1 then 
			SendSysMsg(UserWorldId,"Khong du cho trong", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 31, RwJdId)
			return 
		end
		--AddSkillBook9(UserWorldId,weiz);

		SetPlayerTransfer(UserWorldId,Player.Player_Zx,9)
		Player.Player_Money=Player.Player_Money+20000000
		UpGongFu(UserWorldId)
		UpMoney(UserWorldId)
		UpExp(UserWorldId)
		UpYzbItme(UserWorldId)

			if Player.Player_Job == 8 then 
				Hfgzhuanzhi(UserWorldId)
			end
			
			if Player.Player_Job == 9 then 
				Hfgzhuanzhi(UserWorldId)
			end
			
			if Player.Player_Job == 11 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 12 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 13 then 
				Hfgzhuanzhi(UserWorldId)
			end
		AddQuest(UserWorldId,QuestId,1)
		SendMissionMsg(UserWorldId,QuestId, 21, 1)
	end	
end

function MissIon692(UserWorldId,QuestId, CzId, RwJdId)
	local Player = GetPlayer(UserWorldId)
	if CzId ==1 or CzId==2 then
	SendMissionMsg(UserWorldId,QuestId, 11, RwJdId)
		if Player.Player_Job_leve>=10 then 
			SendSysMsg(UserWorldId,"Da thang chuc roi", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Level < 150 then 
			SendSysMsg(UserWorldId,"Ban chua du level de thang chuc", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Job_leve < 9 then 
			SendSysMsg(UserWorldId,"Chua thang chuc lan 9", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		end
			if Player.Player_Job == 8 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 9 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 11 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 12 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 13 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
		local weiz=GetPackage(UserWorldId)
		if weiz==-1 then 
			SendSysMsg(UserWorldId,"Khong du cho trong", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 31, RwJdId)
			return 
		end
		--AddSkillBook10(UserWorldId,weiz);

		SetPlayerTransfer(UserWorldId,Player.Player_Zx,10)
		Player.Player_Money=Player.Player_Money+30000000
		UpGongFu(UserWorldId)
		UpMoney(UserWorldId)
		UpExp(UserWorldId)
		UpYzbItme(UserWorldId)

			if Player.Player_Job == 8 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 9 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 11 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 12 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 13 then 
				Hfgzhuanzhi(UserWorldId)
			end
		AddQuest(UserWorldId,QuestId,1)
		SendMissionMsg(UserWorldId,QuestId, 21, 1)
	end	
end

function MissIon9712(UserWorldId,QuestId, CzId, RwJdId)
	local Player = GetPlayer(UserWorldId)
	if CzId ==1 or CzId==2 then
	SendMissionMsg(UserWorldId,QuestId, 11, RwJdId)
		if Player.Player_Job_leve>=11 then 
			SendSysMsg(UserWorldId,"Da thang chuc roi", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Level < 160 then 
			SendSysMsg(UserWorldId,"Ban chua du level de thang chuc", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		elseif Player.Player_Job_leve < 10 then 
			SendSysMsg(UserWorldId,"Chua thang chuc lan 10", 23, "ERROR")
				SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
			return 
		end
			if Player.Player_Job == 8 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 9 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 11 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 12 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
			if Player.Player_Job == 13 then 
				local Hfgwq = Hfgwuqi(UserWorldId)
				if Hfgwq then
					SendMissionMsg(UserWorldId,QuestId, 12, RwJdId)
					return 
				end
			end
		local weiz=GetPackage(UserWorldId)
		if weiz==-1 then 
			SendSysMsg(UserWorldId,"Khong du cho trong", 23, "ERROR")
			SendMissionMsg(UserWorldId,QuestId, 31, RwJdId)
			return 
		end
		--AddSkillBook10(UserWorldId,weiz);

		SetPlayerTransfer(UserWorldId,Player.Player_Zx,11)
		Player.Player_Money=Player.Player_Money+40000000
		UpGongFu(UserWorldId)
		UpMoney(UserWorldId)
		UpExp(UserWorldId)
		UpYzbItme(UserWorldId)

			if Player.Player_Job == 8 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 9 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 11 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 12 then 
				Hfgzhuanzhi(UserWorldId)
			end
			if Player.Player_Job == 13 then 
				Hfgzhuanzhi(UserWorldId)
			end
		AddQuest(UserWorldId,QuestId,1)
		SendMissionMsg(UserWorldId,QuestId, 21, 1)
	end	
end