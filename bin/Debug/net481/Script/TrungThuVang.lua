function TrungThuVang(UserWorldId, ItmeId, site, number)
    local Player = GetPlayer(UserWorldId)
	local Itme = GetWorldItme(ItmeId)
	local weizs=GetPackages(UserWorldId,96) 
	
local weiz1=GetPakItme(UserWorldId,999000055)
	if weiz1==-1 then 
		SendSysMsg(UserWorldId,"thieu nguyen lieu ", 9, "Thong Bao")
		return 
	end
local weiz2=GetPakItme(UserWorldId,1008000459)
	if weiz2==-1 then 
		SendSysMsg(UserWorldId,"long den xanh" , 9, "Thong Bao")
		return 
	end
local weizs=GetPackages(UserWorldId,1)
if weizs.Count<1 then 
	SendSysMsg(UserWorldId,"Ban khong con cho trong", 9, "Thong Bao")
	return 
end

UpUseItme(UserWorldId,weiz1,1)
UpUseItme(UserWorldId,weiz2,1)

	local tile=math.random(1,207)
		
		if tile > 0 and tile <= 3 then
		local chiso=math.random(700035,700035) -- clvc SC
		local weiz800000061=GetPackages(UserWorldId,1)
		AddItmePropts(UserWorldId,800000061,weizs[0],1,chiso,0,0,0,0,0,0,0,0,0)
	end		
	if tile > 3 and tile <= 6 then
		local weiz1008001057=GetPackages(UserWorldId,1)  ----- bua chan
        AddItmePropts(UserWorldId,1008001057,weizs[0],1,0,0,0,0,0,0,0,0,0,7)		
	end
		if tile > 6 and tile <= 9 then
		local chiso=math.random(1100100,1100100) ------ ulpt sc
		local weiz800000062=GetPackages(UserWorldId,1)
		AddItmePropts(UserWorldId,800000062,weizs[0],1,chiso,0,0,0,0,0,0,0,0,0)		
	end
        if tile > 9 and tile <= 20 then
        local weiz800000005=GetPackages(UserWorldId,1)  -----pvp 15%
        AddItmePropts(UserWorldId,800000005,weizs[0],1,0,0,0,0,0,0,0,0,0,0)    		
	end	
		if tile > 20 and tile <= 25 then
		local weiz1000000293=GetPackages(UserWorldId,1)  -----the vo huan
        AddItmePropts(UserWorldId,1000000293,weizs[0],4,0,0,0,0,0,0,0,0,0,0)		
	end
		if tile > 25 and tile <= 35 then
		local weiz1008000197=GetPackages(UserWorldId,1)  ------dai chien than dan
        AddItmePropts(UserWorldId,1008000197,weizs[0],1,0,0,0,0,0,0,0,0,0,0)	
	end
	if tile > 35 and tile <= 38 then
		local weiz1008001058=GetPackages(UserWorldId,1)  ----- bua chan
        AddItmePropts(UserWorldId,1008001058,weizs[0],1,0,0,0,0,0,0,0,0,0,7)		
	end
		if tile > 38 and tile <= 48 then
		local weiz1000000288=GetPackages(UserWorldId,1)  -----the vo huan
        AddItmePropts(UserWorldId,1000000288,weizs[0],6,0,0,0,0,0,0,0,0,0,0)
	end		
		if tile > 48 and tile <= 60 then
		local weiz1008000144=GetPackages(UserWorldId,1)  -----del 5%
        AddItmePropts(UserWorldId,1008000144,weizs[0],1,0,0,0,0,0,0,0,0,0,0) 		
	end
		if tile > 60 and tile <= 64 then
		local weiz1008001111=GetPackages(UserWorldId,1)  -------keo pk
        AddItmePropts(UserWorldId,1008001111,weizs[0],1,0,0,0,0,0,0,0,0,0,7) 		
	end
	if tile > 64 and tile <= 80 then
		local weiz1000000289=GetPackages(UserWorldId,1)  -----the vo huan
        AddItmePropts(UserWorldId,1000000289,weizs[0],8,0,0,0,0,0,0,0,0,0,0)
	end		
        if tile > 80 and tile <= 92 then
        local weiz1008000143=GetPackages(UserWorldId,1)  ----dame 3%
        AddItmePropts(UserWorldId,1008000143,weizs[0],1,0,0,0,0,0,0,0,0,0,0)    		
	end	
	if tile > 92 and tile <= 95 then
		local weiz1008000136=GetPackages(UserWorldId,1)  ----- pvp 25%
        AddItmePropts(UserWorldId,1008000136,weizs[0],1,0,0,0,0,0,0,0,0,0,0)	
	end
		if tile > 95 and tile <= 110 then
		local weiz1007000006=GetPackages(UserWorldId,1)  ---- HP 700
        AddItmePropts(UserWorldId,1007000006,weizs[0],1,0,0,0,0,0,0,0,0,0,0)		
	end
		if tile > 110 and tile <= 120 then
		local weiz1008001021=GetPackages(UserWorldId,1)  ---- kim long chi theu
        AddItmePropts(UserWorldId,1008001021,weizs[0],1,0,0,0,0,0,0,0,0,0,0)	
	end
	if tile > 120 and tile <= 130 then
		local weiz1000000291=GetPackages(UserWorldId,1)  -----the vo huan
        AddItmePropts(UserWorldId,1000000291,weizs[0],6,0,0,0,0,0,0,0,0,0,0)
	end
	if tile > 130 and tile <= 135 then
		local weiz1008000072=GetPackages(UserWorldId,1) ---- bua trang suc
		AddItmePropts(UserWorldId,1008000072,weizs[0],1,0,0,0,0,0,0,0,0,0,7)		
	end
	if tile > 135 and tile <= 150 then
		local weiz1008000097=GetPackages(UserWorldId,1)  -----pill EXP 30%
        AddItmePropts(UserWorldId,1008000097,weizs[0],1,0,0,0,0,0,0,0,0,0,0)
	end
	if tile > 150 and tile <= 161 then
		local weiz1000000290=GetPackages(UserWorldId,1)  ----the vo huan
        AddItmePropts(UserWorldId,1000000290,weizs[0],6,0,0,0,0,0,0,0,0,0,0)
	end
	if tile > 161 and tile <= 165 then
		local weiz1008001112=GetPackages(UserWorldId,1)  ----- keo train
        AddItmePropts(UserWorldId,1008001112,weizs[0],1,0,0,0,0,0,0,0,0,0,7) 		
	end
	if tile > 165 and tile <= 169 then
		local weiz1008001074=GetPackages(UserWorldId,1)  ----- pvp 20%
        AddItmePropts(UserWorldId,1008001074,weizs[0],1,0,0,0,0,0,0,0,0,0,0)	
	end
	if tile > 169 and tile <= 175 then
		local weiz1000000250=GetPackages(UserWorldId,1)  ----- hop trang suc
        AddItmePropts(UserWorldId,1000000250,weizs[0],1,0,0,0,0,0,0,0,0,0,0)	
	end
	if tile > 175 and tile <= 180 then
		local weiz1000000294=GetPackages(UserWorldId,1)  -----the vo huan
        AddItmePropts(UserWorldId,1000000294,weizs[0],4,0,0,0,0,0,0,0,0,0,0)		
	end
	if tile > 180 and tile <= 184 then
		local weiz16900361=GetPackages(UserWorldId,1)  -----ao choang nam
		AddItmePropts(UserWorldId,16900361,weizs[0],1,0,10000150,20000150,80000003,120000050,0,0,0,0,7)
	end
	if tile > 184 and tile <= 188 then
		local weiz26900345=GetPackages(UserWorldId,1)  -----ao choang nu
		AddItmePropts(UserWorldId,26900345,weizs[0],1,0,10000150,20000150,80000003,120000050,0,0,0,0,7)
	end	
	if tile > 188 and tile <= 192 then
		local weiz1000000299=GetPackages(UserWorldId,1)  -----the vo huan
        AddItmePropts(UserWorldId,1000000299,weizs[0],2,0,0,0,0,0,0,0,0,0,0)
	end	
	if tile > 192 and tile <= 195 then
		local weiz800000013=GetPackages(UserWorldId,1)  -----gold 20
        AddItmePropts(UserWorldId,800000013,weizs[0],1,1200020,0,0,0,0,0,0,0,0,0)
	end	
	if tile > 195 and tile <= 197 then
		local weiz800000013=GetPackages(UserWorldId,1)  -----mm2
        AddItmePropts(UserWorldId,800000013,weizs[0],1,900002,0,0,0,0,0,0,0,0,0)
	end	
	if tile > 197 and tile <= 200 then
		local weiz1000000330=GetPackages(UserWorldId,1)  ----- soul trung
        AddItmePropts(UserWorldId,1000000330,weizs[0],1,0,0,0,0,0,0,0,0,0,0)	
	end
	if tile > 200 and tile <= 204 then
		local weiz1000000299=GetPackages(UserWorldId,1)  -----the vo huan
        AddItmePropts(UserWorldId,1000000299,weizs[0],1,0,0,0,0,0,0,0,0,0,0)
	end	
	if tile > 204 and tile <= 207 then
		local weiz800000061=GetPackages(UserWorldId,1)  ----- kc2 
        AddItmePropts(UserWorldId,800000061,weizs[0],1,800002,0,0,0,0,0,0,0,0,0)	
	end
end