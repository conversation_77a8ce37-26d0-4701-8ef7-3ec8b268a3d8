--Tools tao Script Lua nhiem vu Hiep <PERSON>ch
--Ban quyen thuoc ve hkphongtran
--Ten nhiem vu: (S?? ki��o?n cu?a HKPhongTran) --- So buoc: (2)
function MissIon212(UserWorldId,QuestId, StepQuest, StatusQuest)
  local Player = GetPlayer(UserWorldId)
  -- local listvatpham=math.random(1,45) -- random ngau nhien 3 vat pham co the tuy chinh them
  -- local exprandom=math.random(500,2000) -- random ngau nhien 3 vat pham co the tuy chinh them
  
  local weizs=GetPackages(UserWorldId,96) 
  local Player_Job_Leve = GetPlayer_Job_leve(UserWorldId)
--Cac buoc xu li nhiem vu
  if StepQuest==1 then
      if Player.Player_Level<60 then 
          SendSysMsg(UserWorldId,"Ban phai dat cap do 60 moi co the nhan nhiem vu !", 9, "He thong")
          SendMissionMsg(UserWorldId,QuestId, 12, StatusQuest)  --Chua du cap du yeu cau 
      else
          local QuestLevel=GetQuestLevel(UserWorldId,QuestId)  --lay gia tri QuestLevel la buoc hien tai cua nhan vatt		  
          if Player_Job_Leve==3 then
             
			  AddBuffToCharacter(UserWorldId,11)
				
              StatusQuest=1 
              AddQuest(UserWorldId,QuestId, StatusQuest) 
              SendMissionMsg(UserWorldId,QuestId, 11, StatusQuest)  --cho lam buoc tiep theo la 2
              
			  else 
              SendSysMsg(UserWorldId,"Danh cho thang chuc 3 tro len", 9, "He thong")
              SendMissionMsg(UserWorldId,QuestId, 12, StatusQuest)  --cho lam buoc tiep theo la 2
              end
          if QuestLevel==2 then
              local controng=GetPackages(UserWorldId,3)  --vi tri con trong
              if controng.Count < 2 then  --neu thung do day 
                SendSysMsg(UserWorldId,"Tui do khong du 2 khong gian trong!", 9, "Chu Y")
                SendMissionMsg(UserWorldId,QuestId, 12, StatusQuest)
                return 
              else  --neu con cho trong
              local  bool900000630 = GetQuestItme(UserWorldId, 900000630, 1)             
			  if bool900000630 then --du yeu cau			  
              AddQuestItme(UserWorldId,900000630,0) --xoa vat pham nhiem vu
				---- Add phan thuong o day
              StatusQuest=1  --dat buoc tiep theo la: 3
              AddQuest(UserWorldId, QuestId, StatusQuest)  --cho lam buoc tiep theo la: 3
              SendMissionMsg(UserWorldId,QuestId, 11, StatusQuest)  --cho lam buoc tiep theo la: 3
              else 
                  SendSysMsg(UserWorldId,"Chua dat yeu cau, quay lai sau !", 9, "He thong")
                  SendMissionMsg(UserWorldId,QuestId, 12, StatusQuest) 
              end
           end
           else
           SendMissionMsg(UserWorldId,QuestId, 11, StatusQuest) 
           end
		end   
  elseif StepQuest==2 then
      local QuestLevel=GetQuestLevel(UserWorldId,QuestId)
      if Player.Player_Level<35 then  
          SendSysMsg(UserWorldId,"Ban phai dat cap do 35 moi co the nhan nhiem vu !", 9, "He thong")
          SendMissionMsg(UserWorldId,QuestId, 12, StatusQuest)  --chua dat yeu cau, quay lai sau
      else
            StatusQuest=1
            AddQuest(UserWorldId,QuestId, StatusQuest)
            SendMissionMsg(UserWorldId,QuestId, 21, StatusQuest)
            --SendSysMsg(UserWorldId,"Nhan nhiem vu thanh cong !", 9, "He thong")
         --end
      end
  elseif StepQuest==3 then
      SendMissionMsg(UserWorldId,QuestId, 31, StatusQuest)
      --SendSysMsg(UserWorldId,"Ban vua tu choi nhiem vu !", 9, "He thong")
  elseif StepQuest==4 then
      SendSysMsg(UserWorldId,"Huy bo nhiem vu thanh cong !", 9, "He thong")
  elseif StepQuest==5 then
      local QuestLevel=GetQuestLevel(UserWorldId,QuestId)
	  -- add phan thuong
      SendMissionMsg(UserWorldId,QuestId, 51, StatusQuest)
  end
end