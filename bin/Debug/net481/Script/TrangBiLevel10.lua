function TrangBiLevel10(UserWorldId, ItmeId, site, number)

	local Player = GetPlayer(UserWorldId)
	local Itme = GetWorldItme(ItmeId)
	local weizs=GetPackages(UserWorldId,96) 
	
	if weizs.Count<7 then 
		SendSysMsg(UserWorldId,"<PERSON>hong du 5 o trong chua vat pham", 9, "<PERSON>hong Bao")
		return 
	end
	UpUseItme(UserWorldId,site,1)
	          
				if Player.Player_Job==1 and Player.Player_Job_Leve==1 and Player.Player_Sex==1 then
				AddItmePropts(UserWorldId,110300001,weizs[0],1,20000006,20000020,20000020,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,101200011,weizs[1],1,10000006,10000050,70000050,80000001,0,0,0,0,0,3)
				elseif Player.Player_Job==1 and Player.Player_Job_Leve==1 and Player.Player_Sex==2 then
				AddItmePropts(UserWorldId,120300001,weizs[0],1,20000006,20000020,20000020,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,101200011,weizs[1],1,10000006,10000050,70000050,80000001,0,0,0,0,0,3)
				end
				
				if Player.Player_Job==1 and Player.Player_Job_Leve==2 and Player.Player_Sex==1 then
				AddItmePropts(UserWorldId,110300003,weizs[0],1,20000006,20000030,20000030,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,101200012,weizs[1],1,10000006,10000090,70000090,80000002,0,0,0,0,0,3)
				elseif Player.Player_Job==1 and Player.Player_Job_Leve==2 and Player.Player_Sex==2 then
				AddItmePropts(UserWorldId,120300003,weizs[0],1,20000006,20000030,20000030,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,101200012,weizs[1],1,10000006,10000090,70000090,80000002,0,0,0,0,0,3)
				end
				------------ Kiem
				if Player.Player_Job==2 and Player.Player_Job_Leve==1 and Player.Player_Sex==1 then
				AddItmePropts(UserWorldId,211300001,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,201200011,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				elseif Player.Player_Job==2 and Player.Player_Job_Leve==1 and Player.Player_Sex==2 then
				AddItmePropts(UserWorldId,221300001,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,201200011,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				end
				
				if Player.Player_Job==2 and Player.Player_Job_Leve==2 and Player.Player_Sex==1 then
				AddItmePropts(UserWorldId,211300003,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,201200012,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				elseif Player.Player_Job==2 and Player.Player_Job_Leve==2 and Player.Player_Sex==2 then
				AddItmePropts(UserWorldId,221300003,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,201200012,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				end
				------------ Thuong
				if Player.Player_Job==3 and Player.Player_Job_Leve==1 and Player.Player_Sex==1 then
				AddItmePropts(UserWorldId,311300001,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,301200011,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				elseif Player.Player_Job==3 and Player.Player_Job_Leve==1 and Player.Player_Sex==2 then
				AddItmePropts(UserWorldId,321300001,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,301200011,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				end
				
				if Player.Player_Job==3 and Player.Player_Job_Leve==2 and Player.Player_Sex==1 then
				AddItmePropts(UserWorldId,311300003,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,301200012,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				elseif Player.Player_Job==3 and Player.Player_Job_Leve==2 and Player.Player_Sex==2 then
				AddItmePropts(UserWorldId,321300003,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,301200012,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				end
				------------ Cung
				if Player.Player_Job==4 and Player.Player_Job_Leve==1 and Player.Player_Sex==1 then
				AddItmePropts(UserWorldId,411300001,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,401200011,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				elseif Player.Player_Job==4 and Player.Player_Job_Leve==1 and Player.Player_Sex==2 then
				AddItmePropts(UserWorldId,421300001,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,401200011,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				end
				
				if Player.Player_Job==4 and Player.Player_Job_Leve==2 and Player.Player_Sex==1 then
				AddItmePropts(UserWorldId,411300003,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,401200012,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				elseif Player.Player_Job==4 and Player.Player_Job_Leve==2 and Player.Player_Sex==2 then
				AddItmePropts(UserWorldId,421300003,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,401200012,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				end
				------------ Njnja
				if Player.Player_Job==6 and Player.Player_Job_Leve==1 and Player.Player_Sex==1 then
				AddItmePropts(UserWorldId,711300001,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,701200011,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				elseif Player.Player_Job==6 and Player.Player_Job_Leve==1 and Player.Player_Sex==2 then
				AddItmePropts(UserWorldId,721300001,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,701200011,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				end
				
				if Player.Player_Job==6 and Player.Player_Job_Leve==2 and Player.Player_Sex==1 then
				AddItmePropts(UserWorldId,711300003,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,701200012,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				elseif Player.Player_Job==6 and Player.Player_Job_Leve==2 and Player.Player_Sex==2 then
				AddItmePropts(UserWorldId,721300003,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,701200012,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				end
				------------ Dai Phu
				if Player.Player_Job==5 and Player.Player_Job_Leve==1 and Player.Player_Sex==1 then
				AddItmePropts(UserWorldId,511300001,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,501200011,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				elseif Player.Player_Job==5 and Player.Player_Job_Leve==1 and Player.Player_Sex==2 then
				AddItmePropts(UserWorldId,521300001,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,501200011,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				end
				
				if Player.Player_Job==5 and Player.Player_Job_Leve==2 and Player.Player_Sex==1 then
				AddItmePropts(UserWorldId,511300003,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,501200012,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				elseif Player.Player_Job==5 and Player.Player_Job_Leve==2 and Player.Player_Sex==2 then
				AddItmePropts(UserWorldId,521300003,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,501200012,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				end
				------------ Cam Su
				if Player.Player_Job==7 and Player.Player_Job_Leve==1 and Player.Player_Sex==1 then
				AddItmePropts(UserWorldId,811300001,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,801200011,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				elseif Player.Player_Job==7 and Player.Player_Job_Leve==1 and Player.Player_Sex==2 then
				AddItmePropts(UserWorldId,821300001,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,801200011,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				end
				
				if Player.Player_Job==7 and Player.Player_Job_Leve==2 and Player.Player_Sex==1 then
				AddItmePropts(UserWorldId,811300003,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,801200012,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				elseif Player.Player_Job==7 and Player.Player_Job_Leve==2 and Player.Player_Sex==2 then
				AddItmePropts(UserWorldId,821300003,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,801200012,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				end
				------------ HQB
				if Player.Player_Job==8 and Player.Player_Job_Leve==1 and Player.Player_Sex==1 then
				AddItmePropts(UserWorldId,111304001,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				elseif Player.Player_Job==8 and Player.Player_Job_Leve==1 and Player.Player_Sex==2 then
				AddItmePropts(UserWorldId,121304001,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				end
				
				if Player.Player_Job==8 and Player.Player_Job_Leve==2 and Player.Player_Sex==1 then
				AddItmePropts(UserWorldId,111304003,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				elseif Player.Player_Job==8 and Player.Player_Job_Leve==2 and Player.Player_Sex==2 then
				AddItmePropts(UserWorldId,121304003,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				end
				------------ DHL
				if Player.Player_Job==9 and Player.Player_Job_Leve==1 and Player.Player_Sex==1 then
				AddItmePropts(UserWorldId,211304001,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				elseif Player.Player_Job==9 and Player.Player_Job_Leve==1 and Player.Player_Sex==2 then
				AddItmePropts(UserWorldId,221304001,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				end
				
				if Player.Player_Job==9 and Player.Player_Job_Leve==2 and Player.Player_Sex==1 then
				AddItmePropts(UserWorldId,211304003,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				elseif Player.Player_Job==9 and Player.Player_Job_Leve==2 and Player.Player_Sex==2 then
				AddItmePropts(UserWorldId,221304003,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				end
				------------ Quyen Su
				if Player.Player_Job==10 and Player.Player_Job_Leve==1 and Player.Player_Sex==1 then
				AddItmePropts(UserWorldId,911300001,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,901200011,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				elseif Player.Player_Job==10 and Player.Player_Job_Leve==1 and Player.Player_Sex==2 then
				AddItmePropts(UserWorldId,921300001,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,901200011,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				end
				
				if Player.Player_Job==10 and Player.Player_Job_Leve==2 and Player.Player_Sex==1 then
				AddItmePropts(UserWorldId,911300003,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,901200012,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				elseif Player.Player_Job==10 and Player.Player_Job_Leve==2 and Player.Player_Sex==2 then
				AddItmePropts(UserWorldId,921300003,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,901200012,weizs[1],1,0,0,0,0,0,0,0,0,0,3)
				end
				------------ MLC
				if Player.Player_Job==11 and Player.Player_Job_Leve==1 and Player.Player_Sex==1 then
				AddItmePropts(UserWorldId,411304001,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				elseif Player.Player_Job==11 and Player.Player_Job_Leve==1 and Player.Player_Sex==2 then
				AddItmePropts(UserWorldId,421304001,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				end
				
				if Player.Player_Job==11 and Player.Player_Job_Leve==2 and Player.Player_Sex==1 then
				AddItmePropts(UserWorldId,411304003,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				elseif Player.Player_Job==11 and Player.Player_Job_Leve==2 and Player.Player_Sex==2 then
				AddItmePropts(UserWorldId,421304003,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				end
				------------ Tu Hao
				if Player.Player_Job==12 and Player.Player_Job_Leve==1 then
				AddItmePropts(UserWorldId,311304001,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				elseif Player.Player_Job==12 and Player.Player_Job_Leve==2 then
				AddItmePropts(UserWorldId,311304003,weizs[0],1,0,0,0,0,0,0,0,0,0,3)
				end
				
				------------ Than Nu
				if Player.Player_Job==13 and Player.Player_Job_Leve==1 then
				AddItmePropts(UserWorldId,520304001,weizs[0],1,0,20000010,20000010,0,0,0,0,0,1,3)
				elseif Player.Player_Job==13 and Player.Player_Job_Leve==2 then
				AddItmePropts(UserWorldId,520304003,weizs[0],1,0,20000010,20000010,0,0,0,0,0,1,3)
				end
				
				-----------------------------
				if Player.Player_Job_Leve==1 and Player.Player_Job==11 then
				AddItmePropts(UserWorldId,1500102,weizs[2],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,1500102,weizs[3],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,1800103,weizs[4],1,0,0,0,0,0,0,0,0,0,3)
				elseif Player.Player_Job_Leve==2 and Player.Player_Job==11 then
				AddItmePropts(UserWorldId,1500104,weizs[2],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,1500104,weizs[3],1,0,0,0,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,1800105,weizs[4],1,0,0,0,0,0,0,0,0,0,3)
				elseif Player.Player_Job_Leve==1 then
	            AddItmePropts(UserWorldId,500002,weizs[2],1,20000006,20000010,20000010,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,500002,weizs[3],1,20000006,20000010,20000010,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,800003,weizs[4],1,20000006,20000010,20000010,0,0,0,0,0,0,3)
				elseif Player.Player_Job_Leve==2 then
	            AddItmePropts(UserWorldId,500004,weizs[2],1,20000006,20000020,20000020,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,500004,weizs[3],1,20000006,20000020,20000020,0,0,0,0,0,0,3)
				AddItmePropts(UserWorldId,800005,weizs[4],1,20000006,20000020,20000020,0,0,0,0,0,0,3)
end
end