--Tools tao Script Lua nhiem vu Hiep <PERSON>ch
--Ban quyen thuoc ve hkphongtran
--Ten nhiem vu: (S?? ki��o?n cu?a HKPhongTran) --- So buoc: (2)
function MissIon224(UserWorldId,QuestId, StepQuest, StatusQuest)
  local Player = GetPlayer(UserWorldId)
  local listvatpham=math.random(1,49) -- random ngau nhien 3 vat pham co the tuy chinh them
  local exprandom=math.random(5000,20000) -- random ngau nhien 3 vat pham co the tuy chinh them
  local weizs=GetPackages(UserWorldId,96) 
  local Player_Job_Leve = GetPlayer_Job_leve(UserWorldId)
--Cac buoc xu li nhiem vu
  if StepQuest==1 then
      if Player.Player_Level<60 then 
          SendSysMsg(UserWorldId,"Ban phai dat cap do 60 moi co the nhan nhiem vu !", 9, "He thong")
          SendMissionMsg(User<PERSON>orldId,QuestId, 12, StatusQuest)  --Chua du cap du yeu cau 
      else
          local QuestLevel=GetQuestLevel(UserWorldId,QuestId)  --lay gia tri QuestLevel la buoc hien tai cua nhan vatt		  
          if QuestLevel==1 then  --neu dang o buoc 1
              local  bool900000600 = GetQuestItme(UserWorldId, 900000600, 1)
              if bool900000600 then --du yeu cau			  
              AddQuestItme(UserWorldId,900000600, -1) --xoa vat pham nhiem vu
			
				------------ tra nhiem vu TLC level 60 - 30%
			if Player_Job_Leve==3 then 
		     	if listvatpham > 0 and listvatpham <= 18 then
				AddExpToCharacter(UserWorldId,exprandom * Player.Player_Level)
		    elseif listvatpham > 18 and listvatpham <= 25 then
				local vhprandom = math.random(10,500)
		          AddWuxunToCharacter_CTP(UserWorldId,vhprandom)
				  AddVoHoangToCharacter(UserWorldId,vhprandom)
			elseif listvatpham > 25 and listvatpham <= 42 then
				AddExpToCharacter(UserWorldId,exprandom * Player.Player_Level)
			elseif listvatpham > 42 and listvatpham <= 50 then
				local vhprandom = math.random(20,1000)
		          AddWuxunToCharacter_CTP(UserWorldId,vhprandom)
				  AddVoHoangToCharacter(UserWorldId,vhprandom)
				  end  
                ---------- tra nhiem vu TLC level 80 - 40%
			elseif Player_Job_Leve==4 then 
				if listvatpham > 0 and listvatpham <= 15 then -- EXP
				AddExpToCharacter(UserWorldId,exprandom * Player.Player_Level)
		    elseif listvatpham > 15 and listvatpham <= 25 then -- VH
				local vhprandom = math.random(20,1000)
		         AddWuxunToCharacter_CTP(UserWorldId,vhprandom)
				  AddVoHoangToCharacter(UserWorldId,vhprandom)
			elseif listvatpham > 25 and listvatpham <= 40 then -- EXP
				AddExpToCharacter(UserWorldId,exprandom * Player.Player_Level)
			elseif listvatpham > 40 and listvatpham <= 50 then -- VH
				local vhprandom = math.random(30,1500)
		          AddWuxunToCharacter_CTP(UserWorldId,vhprandom)
				  AddVoHoangToCharacter(UserWorldId,vhprandom)
				end
				------------ tra nhiem vu TLC level 100 - 50%
			elseif Player_Job_Leve==5 then 
				if listvatpham > 0 and listvatpham <= 13 then -- EXP
				AddExpToCharacter(UserWorldId,exprandom * Player.Player_Level)
		    elseif listvatpham > 13 and listvatpham <= 25 then -- VH
				local vhprandom = math.random(30,1500)
		          AddWuxunToCharacter_CTP(UserWorldId,vhprandom)
				  AddVoHoangToCharacter(UserWorldId,vhprandom)
			elseif listvatpham > 25 and listvatpham <= 38 then -- EXP
				AddExpToCharacter(UserWorldId,exprandom * Player.Player_Level)
			elseif listvatpham > 38 and listvatpham <= 50 then -- VH
				local vhprandom = math.random(40,2000)
		         AddWuxunToCharacter_CTP(UserWorldId,vhprandom)
				  AddVoHoangToCharacter(UserWorldId,vhprandom)
				end
					------------ tra nhiem vu TLC level 110 - 60%
			elseif Player_Job_Leve==6 then 
				if listvatpham > 0 and listvatpham <= 10 then -- EXP
				AddExpToCharacter(UserWorldId,exprandom * Player.Player_Level)
		    elseif listvatpham > 10 and listvatpham <= 25 then -- VH
				local vhprandom = math.random(30,1500)
		          AddWuxunToCharacter_CTP(UserWorldId,vhprandom)
				  AddVoHoangToCharacter(UserWorldId,vhprandom)
			elseif listvatpham > 25 and listvatpham <= 35 then -- EXP
				AddExpToCharacter(UserWorldId,exprandom * Player.Player_Level)
			elseif listvatpham > 35 and listvatpham <= 50 then -- VH
				local vhprandom = math.random(40,2000)
		          AddWuxunToCharacter_CTP(UserWorldId,vhprandom)
				  AddVoHoangToCharacter(UserWorldId,vhprandom)
				end
				------------ tra nhiem vu TLC level 120 - 70%
			elseif Player_Job_Leve==7 then 
				if listvatpham > 0 and listvatpham <= 7 then -- EXP
				AddExpToCharacter(UserWorldId,exprandom * Player.Player_Level)
		    elseif listvatpham > 7 and listvatpham <= 25 then -- VH
				local vhprandom = math.random(40,2000)
		          AddWuxunToCharacter_CTP(UserWorldId,vhprandom)
				  AddVoHoangToCharacter(UserWorldId,vhprandom)
			elseif listvatpham > 25 and listvatpham <= 32 then -- EXP
				AddExpToCharacter(UserWorldId,exprandom * Player.Player_Level)
			elseif listvatpham > 32 and listvatpham <= 50 then -- VH
				local vhprandom = math.random(50,3000)
		          AddWuxunToCharacter_CTP(UserWorldId,vhprandom)
				  AddVoHoangToCharacter(UserWorldId,vhprandom)
				end
				------------ tra nhiem vu TLC level 130 - 80%
			elseif Player_Job_Leve==8 then 
				if listvatpham > 0 and listvatpham <= 5 then -- EXP
				AddExpToCharacter(UserWorldId,exprandom * Player.Player_Level)
		    elseif listvatpham > 5 and listvatpham <= 25 then -- VH
				local vhprandom = math.random(50,3000)
		          AddWuxunToCharacter_CTP(UserWorldId,vhprandom)
				  AddVoHoangToCharacter(UserWorldId,vhprandom)
			elseif listvatpham > 25 and listvatpham <= 30 then -- EXP
				AddExpToCharacter(UserWorldId,exprandom * Player.Player_Level)
			elseif listvatpham > 30 and listvatpham <= 50 then -- VH
				local vhprandom = math.random(60,4000)
		          AddWuxunToCharacter_CTP(UserWorldId,vhprandom)
				  AddVoHoangToCharacter(UserWorldId,vhprandom)
				end
					------------ tra nhiem vu TLC level 140 - 90%
			elseif Player_Job_Leve==9 then 
				if listvatpham > 0 and listvatpham <= 3 then -- EXP
				AddExpToCharacter(UserWorldId,exprandom * Player.Player_Level)
		    elseif listvatpham > 3 and listvatpham <= 25 then -- VH
				local vhprandom = math.random(60,4000)
		          AddWuxunToCharacter_CTP(UserWorldId,vhprandom)
				  AddVoHoangToCharacter(UserWorldId,vhprandom)
			elseif listvatpham > 25 and listvatpham <= 28 then -- EXP
				AddExpToCharacter(UserWorldId,exprandom * Player.Player_Level)
			elseif listvatpham > 28 and listvatpham <= 50 then -- VH
				local vhprandom = math.random(80,5000)
		          AddWuxunToCharacter_CTP(UserWorldId,vhprandom)
				  AddVoHoangToCharacter(UserWorldId,vhprandom)
				end
						------------ tra nhiem vu TLC level 150 - 100%
			elseif Player_Job_Leve==10 then
		    if listvatpham > 0 and listvatpham <= 25 then -- VH
				local vhprandom = math.random(90,6000)
		          AddWuxunToCharacter_CTP(UserWorldId,vhprandom)
				  AddVoHoangToCharacter(UserWorldId,vhprandom)
			elseif listvatpham > 25 and listvatpham <= 50 then -- VH
				local vhprandom = math.random(100,7000)
		          AddWuxunToCharacter_CTP(UserWorldId,vhprandom)
				  AddVoHoangToCharacter(UserWorldId,vhprandom)
				end
				else 
              SendMissionMsg(UserWorldId,QuestId, 12, StatusQuest)  --cho lam buoc tiep theo la 2
                end
              StatusQuest=1 
              AddQuest(UserWorldId,QuestId, StatusQuest) 
              SendMissionMsg(UserWorldId,QuestId, 11, StatusQuest)  --cho lam buoc tiep theo la 2
              
			  else 
              SendSysMsg(UserWorldId,"Chua du nguyen lieu", 9, "He thong")
              SendMissionMsg(UserWorldId,QuestId, 12, StatusQuest)  --cho lam buoc tiep theo la 2
              end
          elseif QuestLevel==2 then
              local controng=GetPackages(UserWorldId,3)  --vi tri con trong
              if controng.Count < 2 then  --neu thung do day 
                SendSysMsg(UserWorldId,"Tui do khong du 2 khong gian trong!", 9, "Chu Y")
                SendMissionMsg(UserWorldId,QuestId, 12, StatusQuest)
                return 
              else  --neu con cho trong
              local  bool900000630 = GetQuestItme(UserWorldId, 900000630, 1)             
			  if bool900000630 then --du yeu cau			  
              AddQuestItme(UserWorldId,900000630,0) --xoa vat pham nhiem vu
				---- Add phan thuong o day
              StatusQuest=1  --dat buoc tiep theo la: 3
              AddQuest(UserWorldId, QuestId, StatusQuest)  --cho lam buoc tiep theo la: 3
              SendMissionMsg(UserWorldId,QuestId, 11, StatusQuest)  --cho lam buoc tiep theo la: 3
              else 
                  SendSysMsg(UserWorldId,"Chua dat yeu cau, quay lai sau !", 9, "He thong")
                  SendMissionMsg(UserWorldId,QuestId, 12, StatusQuest) 
              end
           end
           else
           SendMissionMsg(UserWorldId,QuestId, 11, StatusQuest) 
           end
		end   
  elseif StepQuest==2 then
      local QuestLevel=GetQuestLevel(UserWorldId,QuestId)
      if Player.Player_Level<35 then  
          SendSysMsg(UserWorldId,"Ban phai dat cap do 35 moi co the nhan nhiem vu !", 9, "He thong")
          SendMissionMsg(UserWorldId,QuestId, 12, StatusQuest)  --chua dat yeu cau, quay lai sau
      else
            StatusQuest=1
            AddQuest(UserWorldId,QuestId, StatusQuest)
            SendMissionMsg(UserWorldId,QuestId, 21, StatusQuest)
            SendSysMsg(UserWorldId,"Nhan nhiem vu thanh cong !", 9, "He thong")
         --end
      end
  elseif StepQuest==3 then
      SendMissionMsg(UserWorldId,QuestId, 31, StatusQuest)
      SendSysMsg(UserWorldId,"Ban vua tu choi nhiem vu !", 9, "He thong")
  elseif StepQuest==4 then
      SendSysMsg(UserWorldId,"Huy bo nhiem vu thanh cong !", 9, "He thong")
  elseif StepQuest==5 then
      local QuestLevel=GetQuestLevel(UserWorldId,QuestId)
	  -- add phan thuong
      SendMissionMsg(UserWorldId,QuestId, 51, StatusQuest)
  end
end